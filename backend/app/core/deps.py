from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON>2<PERSON><PERSON>wordBearer
from jose import jwt, JWTError
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, text
from datetime import datetime, timedelta
from typing import Optional, Union

from ..db.session import get_db
from ..models.user import User
from ..schemas.token import TokenPayload
from ..core.config import settings

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/api/v1/auth/login")

async def get_current_user(
    db: AsyncSession = Depends(get_db),
    token: str = Depends(oauth2_scheme)
) -> Union[User, int]:
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(
            token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM]
        )
        user_id: Optional[str] = payload.get("sub")
        if user_id is None:
            raise credentials_exception
        
        # Convert to int and create token data
        try:
            user_id_int = int(user_id)
            token_data = TokenPayload(user_id=user_id_int)
        except (ValueError, TypeError):
            raise credentials_exception
        
    except JWTError:
        raise credentials_exception
    
    # Get user from database using select() to get the full User object
    # Cẩn thận chỉ lấy các field tồn tại trong schema thực tế
    try:
        # Sử dụng text để chỉ lấy các cột cần thiết và tồn tại
        result = await db.execute(
            text("""SELECT id, email, hashed_password, is_active, created_at 
                FROM users 
                WHERE id = :user_id AND is_active"""),
            {"user_id": token_data.user_id}
        )
        user_data = result.mappings().first()
        
        if not user_data:
            # If user is None, let's try a direct approach for handling this case
            print(f"DEBUG: User not found, returning user_id_int: {user_id_int}")
            return user_id_int
        
        # Tạo đối tượng User từ dữ liệu truy vấn
        user = User(
            id=user_data["id"],
            email=user_data["email"],
            hashed_password=user_data["hashed_password"],
            is_active=user_data["is_active"],
            created_at=user_data["created_at"]
        )
        
        return user
    except Exception as e:
        print(f"Error getting current user: {e}")
        # Fail gracefully by returning just the user ID
        return user_id_int 