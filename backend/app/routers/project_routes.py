import os
from fastapi import APIRouter, Depends, HTTPException, status, File, UploadFile, Form
from fastapi.responses import StreamingResponse
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Any, List, Optional, Union, Dict

from ..db.session import get_db
from ..core.deps import get_current_user
from ..models.user import User
from ..schemas.project import Project, ProjectCreate, ProjectUpdate
from ..services.project_service import (
    get_projects_by_user, create_project, get_project_by_id, update_project,
    delete_project, upload_requirements_file, search_projects
)

router = APIRouter(prefix="/projects", tags=["projects"])

@router.get("", response_model=Dict[str, Any])
async def get_user_projects(
    skip: int = 0,
    limit: int = 10,
    current_user: Union[User, int] = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """
    Get projects for the current user with pagination
    """
    # Handle case where current_user is an int
    user_id = current_user.id if isinstance(current_user, User) else current_user
    result = await get_projects_by_user(db, user_id, skip, limit)
    return result

@router.post("", response_model=Project, status_code=status.HTTP_201_CREATED)
async def create_new_project(
    project_in: ProjectCreate,
    current_user: Union[User, int] = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """
    Create a new project
    """
    # Handle case where current_user is an int
    user_id = current_user.id if isinstance(current_user, User) else current_user
    project = await create_project(db, project_in, user_id)
    return project

@router.get("/{project_id}", response_model=Project)
async def get_project(
    project_id: int,
    current_user: Union[User, int] = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """
    Get project by ID
    """
    # Handle case where current_user is an int
    user_id = current_user.id if isinstance(current_user, User) else current_user
    project = await get_project_by_id(db, project_id, user_id)
    if not project:
        raise HTTPException(status_code=404, detail="Project not found")
    return project

@router.put("/{project_id}", response_model=Project)
async def update_existing_project(
    project_id: int,
    project_in: ProjectUpdate,
    current_user: Union[User, int] = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """
    Update a project
    """
    # Handle case where current_user is an int
    user_id = current_user.id if isinstance(current_user, User) else current_user
    project = await update_project(db, project_id, project_in, user_id)
    if not project:
        raise HTTPException(status_code=404, detail="Project not found")
    return project

@router.delete("/{project_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_existing_project(
    project_id: int,
    current_user: Union[User, int] = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> None:
    """
    Delete a project
    """
    # Handle case where current_user is an int
    user_id = current_user.id if isinstance(current_user, User) else current_user
    deleted = await delete_project(db, project_id, user_id)
    if not deleted:
        raise HTTPException(status_code=404, detail="Project not found")
    return None

@router.post("/{project_id}/upload-requirements", response_model=Project)
async def upload_project_requirements(
    project_id: int,
    file: UploadFile = File(...),
    current_user: Union[User, int] = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """
    Upload requirements file for a project
    """
    # Validate file type
    if not file.filename.lower().endswith(('.pdf', '.doc', '.docx', '.txt')):
        raise HTTPException(
            status_code=400,
            detail="Only PDF, DOC, DOCX, or TXT files are allowed"
        )
    
    # Handle case where current_user is an int
    user_id = current_user.id if isinstance(current_user, User) else current_user
    project = await upload_requirements_file(db, project_id, user_id, file)
    if not project:
        raise HTTPException(status_code=404, detail="Project not found")
    
    return project

@router.get("/search", response_model=Dict[str, Any])
async def search_user_projects(
    search_term: Optional[str] = None,
    skip: Optional[int] = 0,
    limit: Optional[int] = 10,
    current_user: Union[User, int] = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """
    Search projects by name, description, or requirements text
    """
    # Handle case where current_user is an int
    user_id = current_user.id if isinstance(current_user, User) else current_user
    result = await search_projects(db, user_id, search_term or "", skip or 0, limit or 10)
    return result

@router.get("/search/{term}", response_model=Dict[str, Any])
async def search_projects_by_path(
    term: str,
    skip: int = 0,
    limit: int = 10,
    current_user: Union[User, int] = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """
    Search projects by name, description, or requirements text using path parameter
    """
    # Handle case where current_user is an int
    user_id = current_user.id if isinstance(current_user, User) else current_user
    result = await search_projects(db, user_id, term, skip, limit)
    return result