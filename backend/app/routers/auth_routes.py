from datetime import timed<PERSON><PERSON>, datetime
from fastapi import APIRouter, Body, Depends, HTTPException, status
from fastapi.responses import JSONResponse
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Any, Dict

from ..db.session import get_db
from ..schemas.user import UserC<PERSON>, User, UserGoogleAuth, EmailVerification, ResendVerification, ResetPasswordRequest, ResetPassword
from ..schemas.token import Token
from ..core.config import settings
from ..services.auth_service import (
    get_password_hash, authenticate_user, create_access_token, 
    get_or_create_google_user
)
from ..services.email_verification_service import (
    create_verification_code, verify_email_code, 
    resend_verification_code, send_verification_email
)
from ..services.password_reset_service import (
    create_password_reset_token, reset_user_password
)
from ..models.user import User as UserModel

router = APIRouter(prefix="/auth", tags=["auth"])

@router.post("/register", response_model=Dict[str, str])
async def register(
    user_in: UserCreate,
    db: AsyncSession = Depends(get_db)
) -> Any:
    """
    Register a new user and send verification code
    """
    try:
        #get language (locale) from  user_in.language
        language = user_in.language or "en"
        supported_languages = ['en', 'vi', 'ja']
        if language == 'jp':
            language = 'ja'
        if language not in supported_languages:
            language = 'en'

        # Check if passwords match
        if user_in.password != user_in.password_confirm:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Passwords do not match"
            )
        
        # Check if user already exists
        stmt = (
            UserModel.__table__.select()
            .where(UserModel.email == user_in.email)
        )
        result = await db.execute(stmt)
        existing_user = result.mappings().one_or_none()
        
        if existing_user:
            if existing_user["is_verified"]:
                error_message = {
                    "en": "Email already exists. Please log in or use another email.",
                    "vi": "Email này đã tồn tại. Vui lòng nhập email khác.",
                    "ja": "このメールアドレスは既に登録されています。別のメールアドレスを使用するか、ログインしてください。"
                }
                error_detail = error_message.get(language, error_message["en"])
                return JSONResponse(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    content={"detail": error_detail}
                )
            
            current_time = datetime.utcnow()
            expires_at = existing_user["verification_code_expires_at"]
            
            if expires_at is not None and expires_at.tzinfo is not None:
                from datetime import timezone
                current_time = current_time.replace(tzinfo=timezone.utc)
            
            if (expires_at is None or expires_at < current_time):
                # If verification code has expired, update login information
                update_stmt = (
                    UserModel.__table__.update()
                    .where(UserModel.id == existing_user["id"])
                    .values(hashed_password=get_password_hash(user_in.password))
                )
                await db.execute(update_stmt)
                await db.commit()

                verification_code = create_verification_code(existing_user["id"])
                email_sent = send_verification_email(existing_user["email"], verification_code, language)

                if not email_sent:
                    print(f"Failed to queue verification email to {existing_user['email']}")

                return {
                    "message": "Verification code has been resent. Please check your email.",
                    "email_sent": "true" if email_sent else "false"
                }
            else:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Email already registered but not verified. Please check your email for verification code or use the resend verification endpoint."
                )
        
        # Create new user
        user = UserModel(
            email=user_in.email,
            hashed_password=get_password_hash(user_in.password),
            is_active=True,
            is_verified=False  # Not verified by default
        )
        db.add(user)
        await db.commit()
        await db.refresh(user)
        
        # Generate verification code (sync)
        verification_code = create_verification_code(user.id)
        
        # Send verification email using AWS SES via worker thread
        email_sent = send_verification_email(user.email, verification_code, language)
        
        if not email_sent:
            # Log the error but don't expose it to the client
            print(f"Failed to queue verification email to {user.email}")
        
        # For development/testing, we'll still return the code in the response
        # In production, you should remove the verification_code from the response
        return {
            "message": "User registered successfully. Please check your email for verification code.",
            "email_sent": "true" if email_sent else "false"  # Convert boolean to string
        }
    except HTTPException as http_exc:
        # Re-raise HTTPException as is
        raise http_exc
    except Exception as e:
        # Ensure any database transaction is rolled back
        await db.rollback()
        print(f"Registration error: {str(e)}")
        import traceback
        traceback.print_exc()
        
        # For unexpected errors
        error_message = {
            "en": "An unexpected error occurred. Please try again.",
            "vi": "Đã xảy ra lỗi không mong muốn. Vui lòng thử lại.",
            "ja": "予期せぬエラーが発生しました。もう一度お試しください。"
        }
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=error_message.get(language, error_message["en"])
        )

@router.post("/verify-email", response_model=Dict[str, str])
async def verify_email(
    verification: EmailVerification,
    db: AsyncSession = Depends(get_db)
) -> Any:
    """
    Verify email with verification code
    """
    result = verify_email_code(verification.email, verification.code)
    
    if not result:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid or expired verification code"
        )
    
    return {"message": "Email verified successfully"}

@router.post("/resend-verification", response_model=Dict[str, str])
async def resend_verification(
    resend_data: ResendVerification,
    db: AsyncSession = Depends(get_db),
    language: str = "en"
) -> Any:
    """
    Resend verification code
    """
    try:
        # Generate new verification code (sync)
        verification_code = resend_verification_code(resend_data.email)
        
        # Send verification email via worker thread
        email_sent = send_verification_email(resend_data.email, verification_code, language)
        
        if not email_sent:
            # Log the error but don't expose it to the client
            print(f"Failed to queue verification email to {resend_data.email}")
        
        return {
            "message": "Verification code sent successfully",
            "email_sent": "true" if email_sent else "false"
        }
    except Exception as e:
        await db.rollback()
        print(f"Resend verification error: {str(e)}")
        import traceback
        traceback.print_exc()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to resend verification code: {str(e)}"
        )

@router.post("/login", response_model=Token)
async def login(
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """
    Login with email and password
    """
    try:
        print(f"Attempting login for user: {form_data.username}")
        user = await authenticate_user(db, form_data.username, form_data.password)
        if not user:
            print(f"Authentication failed for user: {form_data.username}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid email or password. Please try again.",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        print(f"Authentication successful for user: {form_data.username}")
        access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            user_id=user.id, expires_delta=access_token_expires
        )
        
        return {"access_token": access_token, "token_type": "bearer"}
    except HTTPException:
        # Re-raise HTTPException as-is (401, 422, etc.)
        raise
    except Exception as e:
        import traceback
        print(f"Login error: {e}")
        print("Detailed traceback:")
        traceback.print_exc()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Login error occurred: {str(e)}"
        )

import requests

@router.post("/google", response_model=Token)
async def google_auth(
    user_data: UserGoogleAuth,
    db: AsyncSession = Depends(get_db)
) -> Any:
    """
    Authenticate with Google
    """
    try:
        # Verify the Google token with Google's API
        if user_data.token:
            # Verify token with Google
            google_response = requests.get(
                f"https://www.googleapis.com/oauth2/v3/tokeninfo?id_token={user_data.token}"
            )
            
            if google_response.status_code != 200:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid Google token"
                )
            
            # Extract user info from Google's response
            google_data = google_response.json()
            
            # Verify that the email matches
            if google_data.get("email") != user_data.email:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Email mismatch in Google token"
                )
            
            # Use the Google ID from the verified token
            google_id = google_data.get("sub")
        else:
            # If no token provided, use the provided Google ID
            # This is less secure and should be used only for development/testing
            google_id = user_data.google_id
        
        # Get or create user with name and picture if provided
        user = await get_or_create_google_user(
            db, 
            user_data.email, 
            google_id,
            name=user_data.name,
            picture=user_data.picture
        )
        
        # Create access token
        access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            user_id=user.id, expires_delta=access_token_expires
        )
        
        return {"access_token": access_token, "token_type": "bearer"}
    except HTTPException:
        raise
    except Exception as e:
        print(f"Google auth error: {str(e)}")
        import traceback
        traceback.print_exc()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Google authentication error: {str(e)}"
        )

@router.post("/reset-password-request")
async def reset_password_request(
    reset_request: ResetPasswordRequest,
    db: AsyncSession = Depends(get_db)
) -> Dict[str, str]:
    """
    Request a password reset
    """
    try:
        # get locale from body, fallback "en"
        locale = getattr(reset_request, 'locale', None) or "en"

        # Create password reset token and send email
        token = await create_password_reset_token(db, reset_request.email, locale)
        
        # Always return success to prevent email enumeration
        return {"message": "If your email is registered, you will receive a reset link"}
    except Exception as e:
        print(f"Error in password reset request: {str(e)}")
        import traceback
        traceback.print_exc()
        # Always return success to prevent information leakage
        return {"message": "If your email is registered, you will receive a reset link"}

@router.post("/reset-password")
async def reset_password(
    reset_data: ResetPassword,
    db: AsyncSession = Depends(get_db)
) -> Dict[str, str]:
    """
    Reset password with token
    """
    try:
        # Reset the password using the token
        success = await reset_user_password(db, reset_data.token, reset_data.new_password)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid or expired reset token"
            )
        
        return {"message": "Password reset successfully"}
    except HTTPException:
        raise
    except Exception as e:
        print(f"Error in password reset: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Password reset failed"
        ) 