from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, text, update
from typing import Any, Dict, Union

from ..db.session import get_db
from ..core.deps import get_current_user
from ..models.user import User, UserSettings as UserSettingsModel
from ..schemas.user import PasswordChange, UserSettings, UserSettingsUpdate
from ..services.auth_service import verify_password, get_password_hash

router = APIRouter(prefix="/users", tags=["users"])

@router.get("/me", response_model=Dict[str, Any])
async def get_current_user_info(
    current_user: Union[User, int] = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """
    Get current user info
    """
    try:
        # Handle case where current_user is an int
        if isinstance(current_user, int):
            # Chỉ l<PERSON>y các cột chắc chắn tồn tại trong database
            result = await db.execute(
                text("""SELECT id, email, is_active, created_at
                    FROM users 
                    WHERE id = :user_id"""),
                {"user_id": current_user}
            )
            user_data = result.mappings().first()
            
            if not user_data:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="User not found"
                )
                
            # Convert sqlalchemy result to dict
            user = {
                "id": user_data["id"],
                "email": user_data["email"],
                "is_active": user_data["is_active"],
                "created_at": user_data["created_at"]
            }
        else:
            # Convert model to dict, excluding hashed_password
            user = {
                "id": current_user.id,
                "email": current_user.email,
                "is_active": current_user.is_active,
                "created_at": current_user.created_at
            }
        
        return user
    except Exception as e:
        print(f"Error getting user info: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving user information"
        )

@router.post("/change-password", status_code=status.HTTP_200_OK)
async def change_password(
    password_data: PasswordChange,
    current_user: Union[User, int] = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, str]:
    """
    Change user password
    """
    # Handle case where current_user is an int
    if isinstance(current_user, int):
        stmt = select(User).where(User.id == current_user)
        result = await db.execute(stmt)
        user = result.scalars().first()
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
    else:
        user = current_user
    
    # Verify current password
    if not verify_password(password_data.current_password, user.hashed_password):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Current password is incorrect"
        )
    
    # Check if new passwords match
    if password_data.new_password != password_data.confirm_password:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="New passwords do not match"
        )
    
    # Update password using direct UPDATE statement
    new_hashed_password = get_password_hash(password_data.new_password)
    stmt = update(User).where(User.id == user.id).values(hashed_password=new_hashed_password)
    await db.execute(stmt)
    await db.commit()
    
    return {"message": "Password updated successfully"}

@router.get("/settings", response_model=UserSettings)
async def get_user_settings(
    current_user: Union[User, int] = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """
    Get user settings
    """
    # Handle case where current_user is an int
    user_id = current_user.id if isinstance(current_user, User) else current_user
    
    # Get user settings
    stmt = select(UserSettingsModel).where(UserSettingsModel.user_id == user_id)
    result = await db.execute(stmt)
    settings = result.scalars().first()

    # If settings don't exist, create default settings
    if not settings:
        settings = UserSettingsModel(
            user_id=user_id,
            currency="USD",
            role_rates={}
        )
        db.add(settings)
        await db.commit()
        await db.refresh(settings)

    return settings

@router.put("/settings", response_model=UserSettings)
async def update_user_settings(
    settings_update: UserSettingsUpdate,
    current_user: Union[User, int] = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """
    Update user settings
    """
    # Handle case where current_user is an int
    user_id = current_user.id if isinstance(current_user, User) else current_user
    
    # Get user settings
    stmt = select(UserSettingsModel).where(UserSettingsModel.user_id == user_id)
    result = await db.execute(stmt)
    settings = result.scalars().first()
    
    # If settings don't exist, create default settings
    if not settings:
        settings = UserSettingsModel(
            user_id=user_id,
            currency="USD",
            role_rates={}
        )
    
    # Update settings
    if settings_update.currency is not None:
        settings.currency = settings_update.currency
    
    if settings_update.role_rates is not None:
        # Merge existing role_rates with updates
        current_rates = settings.role_rates or {}
        settings.role_rates = {**current_rates, **settings_update.role_rates}
    
    db.add(settings)
    await db.commit()
    await db.refresh(settings)
    
    return settings