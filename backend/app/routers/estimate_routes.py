from typing import List, Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File, Form, Body
from fastapi.responses import StreamingResponse
from sqlalchemy.ext.asyncio import AsyncSession
from datetime import datetime
from io import BytesIO

from ..db.session import get_db
from ..services.estimate_service import (
    create_estimate,
    get_estimates_by_project,
    get_estimate_by_id,
    update_estimate,
    delete_estimate,
    generate_estimate_from_requirements
)
from ..services.project_service import get_project
from ..services.ai_service import generate_project_estimate, create_pdf_report
from ..core.deps import get_current_user
from ..models.user import User
from ..schemas.estimate import EstimatePreviewRequest

router = APIRouter(prefix="/projects/{project_id}/estimates", tags=["estimates"])

@router.post("", response_model=Dict[str, Any])
async def create_project_estimate(
    project_id: int,
    estimate_data: Dict[str, Any],
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    # Verify project exists and belongs to user
    project = await get_project(db, project_id)
    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Project not found"
        )
    
    if project["user_id"] != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to access this project"
        )
    
    # Check if project has requirements
    if not project.get("requirements_text") and not project.get("requirements_file_path"):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Project does not have requirements. Please add requirements first."
        )
    
    # Create the estimate
    estimate = await create_estimate(db, project_id, estimate_data)
    
    # Convert to dictionary for response
    return {
        "id": estimate.id,
        "name": estimate.name,
        "project_id": estimate.project_id,
        "development_items": estimate.development_items,
        "summary_items": estimate.summary_items,
        "grand_total": estimate.grand_total,
        "total_cost": estimate.total_cost,
        "day_rate": estimate.day_rate,
        "currency": estimate.currency,
        "is_active": estimate.is_active,
        "created_at": estimate.created_at,
        "updated_at": estimate.updated_at
    }

@router.get("", response_model=List[Dict[str, Any]])
async def get_project_estimates(
    project_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    # Verify project exists and belongs to user
    project = await get_project(db, project_id)
    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Project not found"
        )
    
    if project["user_id"] != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to access this project"
        )
    
    # Get all estimates for the project
    estimates = await get_estimates_by_project(db, project_id)
    return estimates

@router.get("/{estimate_id}", response_model=Dict[str, Any])
async def get_estimate(
    project_id: int,
    estimate_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    # Verify project exists and belongs to user
    project = await get_project(db, project_id)
    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Project not found"
        )
    
    if project["user_id"] != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to access this project"
        )
    
    # Get the specific estimate
    estimate = await get_estimate_by_id(db, estimate_id)
    if not estimate:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Estimate not found"
        )
    
    # Verify estimate belongs to the specified project
    if estimate["project_id"] != project_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Estimate does not belong to this project"
        )
    
    return estimate

@router.put("/{estimate_id}", response_model=Dict[str, Any])
async def update_project_estimate(
    project_id: int,
    estimate_id: int,
    estimate_data: Dict[str, Any],
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    # Verify project exists and belongs to user
    project = await get_project(db, project_id)
    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Project not found"
        )
    
    if project["user_id"] != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to access this project"
        )
    
    # Get the specific estimate
    estimate = await get_estimate_by_id(db, estimate_id)
    if not estimate:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Estimate not found"
        )
    
    # Verify estimate belongs to the specified project
    if estimate["project_id"] != project_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Estimate does not belong to this project"
        )
    
    # Update the estimate
    updated_estimate = await update_estimate(db, estimate_id, estimate_data)
    return updated_estimate

@router.delete("/{estimate_id}", response_model=Dict[str, bool])
async def delete_project_estimate(
    project_id: int,
    estimate_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    # Verify project exists and belongs to user
    project = await get_project(db, project_id)
    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Project not found"
        )
    
    if project["user_id"] != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to access this project"
        )
    
    # Get the specific estimate
    estimate = await get_estimate_by_id(db, estimate_id)
    if not estimate:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Estimate not found"
        )
    
    # Verify estimate belongs to the specified project
    if estimate["project_id"] != project_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Estimate does not belong to this project"
        )
    
    # Delete the estimate
    success = await delete_estimate(db, estimate_id)
    return {"success": success}

@router.post("/generate-from-requirements", response_model=Dict[str, Any])
async def generate_estimate_from_project_requirements(
    project_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    # Verify project exists and belongs to user
    project = await get_project(db, project_id)
    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Project not found"
        )
    
    if project["user_id"] != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to access this project"
        )
    
    # Check if project has requirements
    if not project.get("requirements_text") and not project.get("requirements_file_path"):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Project does not have requirements. Please add requirements first."
        )
    
    # Get requirements text
    requirements_text = project.get("requirements_text", "")
    
    # If no text requirements but has file, return error suggesting to extract text first
    if not requirements_text and project.get("requirements_file_path"):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Project has requirements file but no extracted text. Please extract text from file first."
        )
    
    try:
        # Generate estimate using AI
        ai_estimate = await generate_project_estimate(
            requirements=requirements_text,
            software_type=project.get("software_type"),
            technology_stack=project.get("technology_stack")
        )
        
        # Create estimate data
        estimate_name = f"AI Generated Estimate {project.get('name')} - {project.get('id')}"
        estimate_data = {
            "name": estimate_name,
            "development_items": ai_estimate.get("development_items", []),
            "summary_items": ai_estimate.get("summary_items", []),
            "grand_total": ai_estimate.get("grand_total", 0),
            "total_cost": ai_estimate.get("total_cost", 0),
            "day_rate": ai_estimate.get("day_rate", 500),
            "currency": ai_estimate.get("currency", "USD"),
        }
        
        # Create the estimate in database
        estimate = await create_estimate(db, project_id, estimate_data)
        
        # Return the generated estimate
        return {
            "id": estimate.id,
            "name": estimate.name,
            "project_id": estimate.project_id,
            "development_items": estimate.development_items,
            "summary_items": estimate.summary_items,
            "grand_total": estimate.grand_total,
            "total_cost": estimate.total_cost,
            "day_rate": estimate.day_rate,
            "currency": estimate.currency,
            "is_active": estimate.is_active,
            "created_at": estimate.created_at,
            "updated_at": estimate.updated_at
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error generating estimate: {str(e)}"
        )

@router.get("/{estimate_id}/export-pdf")
async def export_estimate_as_pdf(
    project_id: int,
    estimate_id: int,
    language: str = "en",
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> StreamingResponse:
    """Export estimate as PDF"""
    # Verify project exists and belongs to user
    project = await get_project(db, project_id)
    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Project not found"
        )
    
    if project["user_id"] != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to access this project"
        )
    
    # Get the specific estimate
    estimate = await get_estimate_by_id(db, estimate_id)
    if not estimate:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Estimate not found"
        )
    
    try:
        # Chuẩn bị dữ liệu cho PDF report
        # Đảm bảo dữ liệu có định dạng đúng cho hàm create_pdf_report
        estimate_data_for_pdf = {
            "name": estimate.get("name", ""),
            "development_items": estimate.get("development_items", []),
            "summary_items": estimate.get("summary_items", []),
            "grand_total": estimate.get("grand_total", 0),
            "total_cost": estimate.get("total_cost", 0),
            "day_rate": estimate.get("day_rate", 500),
            "currency": estimate.get("currency", "USD"),
        }
        
        # Pass the language value as is, just change jp to ja
        pdf_language = language
        if pdf_language == "jp":
            pdf_language = "ja"
        
        # Gọi hàm tạo PDF với ngôn ngữ đã chọn, thêm tham số fix_overflow để xử lý số lớn
        pdf_buffer = await create_pdf_report(estimate_data_for_pdf, project["name"], pdf_language)

        
        # Rewind the buffer
        pdf_buffer.seek(0)
        
        # Return the PDF as a streaming response
        # Xử lý tên file để đảm bảo không có ký tự đặc biệt và hỗ trợ Unicode
        import re
        import urllib.parse
        
        # Tạo tên file cơ bản
        filename = f"{project['name']}_{estimate['name']}_estimate.pdf"
        
        # Thay thế khoảng trắng và ký tự đặc biệt
        filename = filename.replace(" ", "_")
        
        # Mã hóa tên file để đảm bảo an toàn với Unicode
        filename = urllib.parse.quote(filename)
        
        return StreamingResponse(
            pdf_buffer,
            media_type="application/pdf",
            headers={
                "Content-Disposition": f'attachment; filename="{filename}"'
            }
        )
    except Exception as e:
        print(f"Error generating PDF: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error generating PDF: {str(e)}"
        )

@router.post("/generate-from-requirements/preview", response_model=Dict[str, Any])
async def preview_estimate_from_project_requirements(
    project_id: int,
    body: EstimatePreviewRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    locale = body.locale
    """Generate an estimate from project requirements but don't save it to the database"""
    # Verify project exists and belongs to user
    project = await get_project(db, project_id)
    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Project not found"
        )
    
    if project["user_id"] != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to access this project"
        )
    
    # Check if project has requirements
    requirements_text = project.get("requirements_text", "")
    requirements_file_path = project.get("requirements_file_path")
    
    if not requirements_text and not requirements_file_path:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Project does not have requirements. Please add requirements first."
        )
    
    try:
        # Get requirements text
        if not requirements_text and requirements_file_path:
            # TODO: Extract text from file if needed
            pass
            
        # Call AI service to generate estimate
        ai_estimate = await generate_project_estimate(
            requirements=requirements_text,
            software_type=project.get("software_type"),
            technology_stack=project.get("technology_stack"),
            locale=locale
        )
        
        # Prepare estimate data but don't save it
        estimate_data = {
            "name": f"Estimate {datetime.now().strftime('%Y-%m-%d %H:%M')}",
            "development_items": ai_estimate.get("development_items", []),
            "summary_items": ai_estimate.get("summary_items", []),
            "grand_total": ai_estimate.get("grand_total", 0),
            "total_cost": ai_estimate.get("total_cost", 0),
            "day_rate": ai_estimate.get("day_rate", 500),
            "currency": ai_estimate.get("currency", "USD")
        }
        
        return {
            "project_id": project_id,
            "estimate": estimate_data
        }
        
    except Exception as e:
        print(f"Error generating estimate preview: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error generating estimate preview: {str(e)}"
        )
