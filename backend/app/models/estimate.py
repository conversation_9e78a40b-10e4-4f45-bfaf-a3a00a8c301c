from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, Text, JSON, Boolean, Float
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship

from ..db.session import Base

class Estimate(Base):
    __tablename__ = "estimates"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, index=True)
    project_id = Column(Integer, ForeignKey("projects.id"))
    development_items = Column(JSON, nullable=True)
    summary_items = Column(JSON, nullable=True)
    grand_total = Column(Float, nullable=True)
    total_cost = Column(Float, nullable=True)
    day_rate = Column(Float, nullable=True)
    currency = Column(String, nullable=True, default="USD")
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationship
    project = relationship("Project", back_populates="estimates")
