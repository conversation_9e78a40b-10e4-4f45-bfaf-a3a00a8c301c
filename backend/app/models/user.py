from sqlalchemy import Column, Inte<PERSON>, String, <PERSON><PERSON><PERSON>, DateTime, ForeignKey, JSON
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship

from ..db.session import Base

class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    email = Column(String, unique=True, index=True, nullable=False)
    hashed_password = Column(String, nullable=True)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Field for Google OAuth
    google_id = Column(String, unique=True, nullable=True)
    
    # Thêm các trường cho xác minh email - để tương lai khi migration
    # Định nghĩa nullable=True để tránh lỗi khi cột chưa tồn tại
    is_verified = Column(Boolean, default=False, nullable=True)
    verification_code = Column(String, nullable=True)
    verification_code_expires_at = Column(DateTime(timezone=True), nullable=True)
    
    # Password reset fields
    reset_password_token = Column(String, nullable=True)
    reset_password_expires_at = Column(DateTime(timezone=True), nullable=True)
    
    # One-to-one relationship with UserSettings
    settings = relationship("UserSettings", back_populates="user", uselist=False)
    projects = relationship("Project", back_populates="user")

class UserSettings(Base):
    __tablename__ = "user_settings"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    currency = Column(String, default="USD", nullable=False)
    role_rates = Column(JSON, default={})
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Link back to User
    user = relationship("User", back_populates="settings") 