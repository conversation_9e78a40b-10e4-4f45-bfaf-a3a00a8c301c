from typing import List, Dict, Any, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import desc

from ..models.estimate import Estimate
from ..models.project import Project
from ..services.ai_service import generate_project_estimate

async def create_estimate(db: AsyncSession, project_id: int, estimate_data: Dict[str, Any]) -> Estimate:
    """
    Create a new estimate for a project
    """
    # Create a name for the estimate based on the creation date
    import datetime
    estimate_name = f"Estimate {datetime.datetime.now().strftime('%Y-%m-%d %H:%M')}"
    
    # Create the estimate object
    db_estimate = Estimate(
        name=estimate_name,
        project_id=project_id,
        development_items=estimate_data.get("development_items"),
        summary_items=estimate_data.get("summary_items"),
        grand_total=estimate_data.get("grand_total"),
        total_cost=estimate_data.get("total_cost"),
        day_rate=estimate_data.get("day_rate"),
        currency=estimate_data.get("currency", "USD"),
        is_active=True
    )
    
    db.add(db_estimate)
    await db.commit()
    await db.refresh(db_estimate)
    return db_estimate

async def get_estimates_by_project(db: AsyncSession, project_id: int) -> List[Dict[str, Any]]:
    """
    Get all estimates for a project
    """
    result = await db.execute(
        select(Estimate)
        .where(Estimate.project_id == project_id)
        .order_by(desc(Estimate.created_at))
    )
    
    estimates = result.scalars().all()
    
    # Convert to dictionaries for JSON serialization
    estimate_list = []
    for estimate in estimates:
        estimate_dict = {
            "id": estimate.id,
            "name": estimate.name,
            "project_id": estimate.project_id,
            "development_items": estimate.development_items,
            "summary_items": estimate.summary_items,
            "grand_total": estimate.grand_total,
            "total_cost": estimate.total_cost,
            "day_rate": estimate.day_rate,
            "currency": estimate.currency,
            "is_active": estimate.is_active,
            "created_at": estimate.created_at,
            "updated_at": estimate.updated_at
        }
        estimate_list.append(estimate_dict)
    
    return estimate_list

async def get_estimate_by_id(db: AsyncSession, estimate_id: int) -> Optional[Dict[str, Any]]:
    """
    Get a specific estimate by ID
    """
    result = await db.execute(
        select(Estimate).where(Estimate.id == estimate_id)
    )
    
    estimate = result.scalars().first()
    
    if not estimate:
        return None
    
    # Convert to dictionary for JSON serialization
    estimate_dict = {
        "id": estimate.id,
        "name": estimate.name,
        "project_id": estimate.project_id,
        "development_items": estimate.development_items,
        "summary_items": estimate.summary_items,
        "grand_total": estimate.grand_total,
        "total_cost": estimate.total_cost,
        "day_rate": estimate.day_rate,
        "currency": estimate.currency,
        "is_active": estimate.is_active,
        "created_at": estimate.created_at,
        "updated_at": estimate.updated_at
    }
    
    return estimate_dict

async def update_estimate(db: AsyncSession, estimate_id: int, estimate_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """
    Update an existing estimate
    """
    result = await db.execute(
        select(Estimate).where(Estimate.id == estimate_id)
    )
    
    estimate = result.scalars().first()
    
    if not estimate:
        return None
    
    # Update the estimate fields
    if "name" in estimate_data:
        estimate.name = estimate_data["name"]
    if "development_items" in estimate_data:
        estimate.development_items = estimate_data["development_items"]
    if "summary_items" in estimate_data:
        estimate.summary_items = estimate_data["summary_items"]
    if "grand_total" in estimate_data:
        estimate.grand_total = estimate_data["grand_total"]
    if "total_cost" in estimate_data:
        estimate.total_cost = estimate_data["total_cost"]
    if "day_rate" in estimate_data:
        estimate.day_rate = estimate_data["day_rate"]
    if "currency" in estimate_data:
        estimate.currency = estimate_data["currency"]
    if "is_active" in estimate_data:
        estimate.is_active = estimate_data["is_active"]
    
    await db.commit()
    await db.refresh(estimate)
    
    # Convert to dictionary for JSON serialization
    estimate_dict = {
        "id": estimate.id,
        "name": estimate.name,
        "project_id": estimate.project_id,
        "development_items": estimate.development_items,
        "summary_items": estimate.summary_items,
        "grand_total": estimate.grand_total,
        "total_cost": estimate.total_cost,
        "day_rate": estimate.day_rate,
        "currency": estimate.currency,
        "is_active": estimate.is_active,
        "created_at": estimate.created_at,
        "updated_at": estimate.updated_at
    }
    
    return estimate_dict

async def delete_estimate(db: AsyncSession, estimate_id: int) -> bool:
    """
    Delete an estimate
    """
    result = await db.execute(
        select(Estimate).where(Estimate.id == estimate_id)
    )
    
    estimate = result.scalars().first()
    
    if not estimate:
        return False
    
    await db.delete(estimate)
    await db.commit()
    
    return True

async def generate_estimate_from_requirements(db: AsyncSession, project_id: int, user_id: int) -> Optional[Dict[str, Any]]:
    """
    Generate an estimate from project requirements using AI
    """
    # Get project
    result = await db.execute(
        select(Project).where(Project.id == project_id, Project.user_id == user_id)
    )
    project = result.scalars().first()
    
    if not project:
        return None
    
    # Check if project has requirements
    if not project.requirements_text and not project.requirements_file_path:
        return None
    
    # Get requirements text
    requirements_text = project.requirements_text or ""
    
    # Generate estimate using AI
    try:
        ai_estimate = await generate_project_estimate(
            requirements=requirements_text,
            software_type=project.software_type,
            technology_stack=project.technology_stack
        )
        
        # Create estimate data
        estimate_name = f"AI Generated Estimate {project.name}"
        estimate_data = {
            "name": estimate_name,
            "development_items": ai_estimate.get("development_items", []),
            "summary_items": ai_estimate.get("summary_items", []),
            "grand_total": ai_estimate.get("grand_total", 0),
            "total_cost": ai_estimate.get("total_cost", 0),
            "day_rate": ai_estimate.get("day_rate", 500),
            "currency": ai_estimate.get("currency", "USD"),
        }
        
        # Create the estimate
        estimate = await create_estimate(db, project_id, estimate_data)
        
        # Convert to dictionary
        estimate_dict = {
            "id": estimate.id,
            "name": estimate.name,
            "project_id": estimate.project_id,
            "development_items": estimate.development_items,
            "summary_items": estimate.summary_items,
            "grand_total": estimate.grand_total,
            "total_cost": estimate.total_cost,
            "day_rate": estimate.day_rate,
            "currency": estimate.currency,
            "is_active": estimate.is_active,
            "created_at": estimate.created_at,
            "updated_at": estimate.updated_at
        }
        
        return estimate_dict
    except Exception as e:
        print(f"Error generating estimate: {str(e)}")
        return None
