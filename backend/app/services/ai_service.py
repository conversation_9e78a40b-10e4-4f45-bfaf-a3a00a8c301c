import os
import re
import json
import logging
import asyncio
from datetime import datetime
from typing import Dict, Any, Optional, List
from io import BytesIO

# Import for PDF generation
from reportlab.lib import colors
from reportlab.lib.units import inch
from reportlab.lib.pagesizes import letter, landscape
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, PageBreak, KeepTogether
from reportlab.lib.enums import TA_CENTER, TA_RIGHT
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.pdfbase.cidfonts import UnicodeCIDFont
from reportlab.pdfbase import pdfdoc
from reportlab.pdfgen import canvas
from decimal import Decimal, ROUND_HALF_UP
import os
import sys
import logging
from typing import Dict, Any, Optional, Tuple, Union

def register_fonts() -> Tuple[bool, str]:
    """
    Đ<PERSON><PERSON> ký các font cần thiết cho PDF
    Trả về (success, font_name)
    """
    try:
        # Đ<PERSON>ng ký font mặc định hỗ trợ Unicode
        try:
            pdfmetrics.registerFont(UnicodeCIDFont('HeiseiMin-W3'))
            print("Registered HeiseiMin-W3 font")
        except Exception as e:
            print(f"Warning: Could not register HeiseiMin-W3 font: {e}")

        # Đường dẫn đến thư mục chứa font
        base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        font_dir = os.path.join(base_dir, 'fonts')
        
        # Tạo thư mục fonts nếu chưa tồn tại
        if not os.path.exists(font_dir):
            try:
                os.makedirs(font_dir, exist_ok=True)
                print(f"Created font directory at {font_dir}")
            except Exception as e:
                print(f"Warning: Could not create font directory {font_dir}: {e}")
                return False, 'Helvetica'

        # Đăng ký DejaVuSans nếu có
        dejavu_sans = os.path.join(font_dir, 'DejaVuSans.ttf')
        dejavu_sans_bold = os.path.join(font_dir, 'DejaVuSans-Bold.ttf')

        if os.path.exists(dejavu_sans) and os.path.exists(dejavu_sans_bold):
            try:
                pdfmetrics.registerFont(TTFont('DejaVuSans', dejavu_sans))
                pdfmetrics.registerFont(TTFont('DejaVuSans-Bold', dejavu_sans_bold))
                print("Registered DejaVuSans fonts")
                return True, 'DejaVuSans'
            except Exception as e:
                print(f"Warning: Could not register DejaVuSans fonts: {e}")
        else:
            print(f"DejaVuSans fonts not found at {font_dir}")

        # Nếu không có font nào khác, sử dụng Helvetica mặc định
        print("Using default Helvetica font")
        return True, 'Helvetica'

    except Exception as e:
        logging.error(f"Error in register_fonts: {e}")
        print(f"Error registering fonts: {e}")
        return False, 'Helvetica'

# Đăng ký font khi import module
font_registered, default_font = register_fonts()
if not font_registered:
    print("Warning: Font registration failed, using system default font")

import google.generativeai as genai
from langchain.output_parsers import ResponseSchema, StructuredOutputParser

from ..core.config import settings

# Initialize Gemini API
genai.configure(api_key=settings.GOOGLE_API_KEY)

def _fix_common_json_errors(json_str: str) -> str:
    """
    Fix common JSON errors in the response from Gemini API
    """
    # Fix missing commas between objects in arrays
    json_str = re.sub(r'(\})(\s*)(\{)', r'\1,\2\3', json_str)
    
    # Fix trailing commas in arrays and objects
    json_str = re.sub(r',\s*\}', r'\}', json_str)
    json_str = re.sub(r',\s*\]', r'\]', json_str)
    
    # Fix missing quotes around keys
    json_str = re.sub(r'([{,]\s*)([a-zA-Z_][a-zA-Z0-9_]*)\s*:', r'\1"\2":', json_str)
    
    # Fix single quotes used instead of double quotes
    # First, escape any existing escaped double quotes
    json_str = json_str.replace("\\\"", "__ESCAPED_DOUBLE_QUOTE__")
    # Then replace single quotes with double quotes
    json_str = json_str.replace("'", '"')
    # Finally, restore escaped double quotes
    json_str = json_str.replace("__ESCAPED_DOUBLE_QUOTE__", "\\\"")
    
    # Fix boolean and null values
    json_str = re.sub(r':\s*True\b', r': true', json_str)
    json_str = re.sub(r':\s*False\b', r': false', json_str)
    json_str = re.sub(r':\s*None\b', r': null', json_str)
    
    return json_str

async def generate_project_estimate(
    requirements: str, 
    software_type: Optional[str] = None, 
    technology_stack: Optional[str] = None,
    locale: Optional[str] = None
) -> Dict[str, Any]:
    """
    Generate a project estimate using Google Gemini API
    """
    # Validate requirements input
    if not requirements or len(requirements.strip()) < 50:
        raise ValueError("The project requirements are too short. Please provide a more detailed description of the project.")
    
    # Print debug information
    print(f"DEBUG: Starting estimate generation with Google Gemini")
    print(f"DEBUG: Software type: {software_type}")
    print(f"DEBUG: Technology stack: {technology_stack}")
    print(f"DEBUG: Requirements length: {len(requirements)}")
    
    # Use AI to determine if the text is a project requirement
    try:
        # Validation with Gemini
        validation_prompt = f"""
        Analyze the following text and determine if it describes software project requirements.
        It could be in any language (English, Vietnamese, Japanese, etc.).
        
        TEXT:
        {requirements}
        
        Answer with only "YES" if the text contains software project requirements, features, user stories, or system specifications.
        Answer with only "NO" if the text is not related to software project requirements.
        """
        
        print(f"DEBUG: Calling validation with Gemini")
        
        try:
            # Create Gemini model instance for validation
            validation_model = genai.GenerativeModel(model_name="gemini-2.0-flash-exp")
            validation_response = await asyncio.to_thread(
                validation_model.generate_content, 
                validation_prompt,
                generation_config={
                    "max_output_tokens": 10,
                    "temperature": 0.1,
                }
            )
            
            print(f"DEBUG: Validation response received successfully")
            response_content = validation_response.text.strip().upper()
            
            print(f"DEBUG: Validation result: {response_content}")
            
            # Check if the response indicates this is a project requirement
            if "NO" in response_content:
                raise ValueError("The content does not match project requirements. Please provide information about specific features, functions, or specific requirements of the project.")
        
        except Exception as validation_error:
            print(f"DEBUG: Error during validation call: {validation_error}")
            print(f"DEBUG: Error type: {type(validation_error)}")
            print(f"DEBUG: Continuing despite validation error")
            # Continue despite validation error
    
    except Exception as e:
        # If AI validation fails, fall back to basic validation
        print(f"AI validation error: {e}")
        # No additional exception raised, continue with estimation
    
    software_type_str = f"Software Type: {software_type}" if software_type else ""
    technology_stack_str = f"Technology Stack: {technology_stack}" if technology_stack else ""
    
    # Define the response schema for structured output
    development_item_schema = ResponseSchema(
        name="development_items",
        description="""List of development items with structure:
        {
            "feature": "User story name (specific, small)",
            "description": "Implementation details",
            "epic": "Parent epic/feature name",
            "priority": "MUST_HAVE or NICE_TO_HAVE",
            "backend_effort": float,
            "frontend_effort": float,
            "unit_test_effort": float
        }""",
        type="list[dict]"
    )
    
    summary_item_schema = ResponseSchema(
        name="summary_items",
        description="List of summary items with category, effort, and description",
        type="list[dict]"
    )
    
    grand_total_schema = ResponseSchema(
        name="grand_total",
        description="Grand total of effort",
        type="float"
    )
    
    response_schemas = [
        development_item_schema,
        summary_item_schema,
        grand_total_schema
    ]
    
    parser = StructuredOutputParser.from_response_schemas(response_schemas)
    format_instructions = parser.get_format_instructions()
    
    # Determine language instructions based on locale
    if locale == "ja":
        prompt_language = "Japanese"
    elif locale == "en":
        prompt_language = "English"
    elif locale == "vi":
        prompt_language = "Vietnamese"
    else:
        prompt_language = "English"

    if prompt_language:
        language_instruction = f"IMPORTANT: Generate ALL output in {prompt_language}, regardless of the input requirements language."
    else:
        language_instruction = "IMPORTANT: You must detect the language of the requirements (English, Vietnamese, Japanese, etc.) and generate ALL output in the SAME LANGUAGE as the input requirements. For example, if requirements are in Japanese, all epics, user stories, and descriptions must be in Japanese."

    # Create the full prompt
    prompt = f"""
    You are an expert software project estimator with years of experience in estimation. I need you to analyze the provided requirements and estimate the effort required in man-days. Based on the requirements, consider typical development productivity and include all necessary phases.

    {software_type_str}
    {technology_stack_str}
    
    # Requirements
    {requirements}
    
    Create a detailed estimate following these guidelines:
    
    ## Language Matching:
     {language_instruction}
    
    ## Feature Breakdown:
    1. First, identify main features/epics from the requirements
    2. Break down each epic into smaller user stories or specific functionalities (at least 3-5 per epic)
    3. Each user story should be focused on a single, distinct capability that delivers user value
    4. Keep user stories small enough to be completed in 1-5 days
    5. Use clear, specific names for each user story, avoiding vague descriptions

    ## Estimation:
    For each user story or specific functionality, provide:
    1. User story name (concise, specific) - in the SAME LANGUAGE as the requirements
    2. Implementation description (technical details) - in the SAME LANGUAGE as the requirements
    3. Parent epic/feature it belongs to - in the SAME LANGUAGE as the requirements
    4. Priority (MUST_HAVE or NICE_TO_HAVE)
    5. Effort breakdown:
       - Backend effort (man-days)
       - Frontend effort (man-days)
       - Unit Test effort (man-days)
    
    Ensure unit test effort is approximately 50% of the combined backend and frontend effort.
    
    ## Summary:
    - Total Development Effort: sum of all development tasks
    - Requirements Definition: effort for requirements refinement (10% of total development)
    - UI/UX Design: effort for design work (10% of total development)
    - Integration Test: effort for integration testing (25% of total development)
    - UAT Support: effort for supporting user acceptance testing (10% of total development)
    - Project Management: effort for project management (10% of total development)
    
    {format_instructions}
    
    Respond with a valid JSON object that includes:
    1. development_items: array of objects with feature, description, epic, priority, backend_effort, frontend_effort, and unit_test_effort
    2. summary_items: array of objects with category, effort, and description
    3. grand_total: total effort for all items

    Note: 
    - All effort values should be floating point numbers representing man-days.
    - Each user story should be small, focused, and implementable in 1-5 days.
    - Avoid creating large, vague epics as individual items in development_items.
    - ALL text fields (feature names, descriptions, epic names) MUST be in the SAME LANGUAGE as the input requirements.
    """
    
    try:
        # Call Gemini API
        print(f"DEBUG: Calling main estimation with Gemini")
        
        try:
            # Create Gemini model instance for estimation
            model = genai.GenerativeModel(model_name="gemini-2.0-flash-exp")
            response = await asyncio.to_thread(
                model.generate_content, 
                prompt,
                generation_config={
                    "max_output_tokens": 4000,
                    "temperature": 0.2,
                }
            )
            
            print(f"DEBUG: Main estimation response received")
            
            # Parse the response
            try:
                # Extract content from response
                content = response.text
                
                print(f"DEBUG: Response parsing: Got content of length {len(content)}")
                
                # Try to extract JSON from the response if it's wrapped in text
                if isinstance(content, str):
                    # Try to find JSON-like structure
                    start_idx = content.find('{')
                    end_idx = content.rfind('}') + 1
                    
                    print(f"DEBUG: JSON extraction: start_idx={start_idx}, end_idx={end_idx}")
                    
                    if start_idx >= 0 and end_idx > start_idx:
                        json_str = content[start_idx:end_idx]
                        print(f"DEBUG: Found JSON-like structure, parsing...")
                        try:
                            result = json.loads(json_str)
                        except json.JSONDecodeError as json_err:
                            print(f"DEBUG: JSON parsing error: {json_err}")
                            # Thử sửa lỗi JSON phổ biến
                            json_str = _fix_common_json_errors(json_str)
                            print(f"DEBUG: Attempting to parse fixed JSON")
                            result = json.loads(json_str)
                    else:
                        print(f"DEBUG: No JSON structure found in response")
                        raise ValueError("No JSON structure found in response")
                else:
                    result = parser.parse(content)
                
                # Validate the structure
                if not all(key in result for key in ['development_items', 'summary_items', 'grand_total']):
                    raise ValueError("Missing required fields in response")
                
                # Ensure all effort values are float
                for item in result['development_items']:
                    item['backend_effort'] = float(item.get('backend_effort', 0))
                    item['frontend_effort'] = float(item.get('frontend_effort', 0))
                    item['unit_test_effort'] = float(item.get('unit_test_effort', 0))
                    
                    # Đảm bảo các trường mới có giá trị mặc định nếu không có trong kết quả
                    if 'epic' not in item:
                        item['epic'] = ''
                    if 'priority' not in item:
                        item['priority'] = 'MUST_HAVE'
                
                for item in result['summary_items']:
                    item['effort'] = float(item.get('effort', 0))
                
                result['grand_total'] = float(result['grand_total'])
                
                return result
                
            except Exception as parse_error:
                print(f"DEBUG: Error parsing response: {parse_error}")
                print(f"DEBUG: Error type: {type(parse_error)}")
                print(f"DEBUG: First 200 chars of response: {str(content)[:200] if content else 'No content'}")
                raise ValueError(f"Could not parse AI response: {parse_error}")
                
        except Exception as api_error:
            print(f"DEBUG: Error calling Gemini API: {api_error}")
            print(f"DEBUG: Error type: {type(api_error)}")
            raise ValueError(f"Failed to call Google Gemini API: {api_error}")
            
    except Exception as e:
        print(f"Error in estimate generation: {e}")
        raise ValueError(f"Failed to generate estimate: {e}")

def round_half_up(value, digits=2):
    """
   Rounds a number according to the "half up" rule and formats the number.

   Args:

   value: The value to be rounded (number or string representation of a number).
   digits: The number of decimal places to retain (default is 2).
   Returns:

   A formatted string with thousands separators.
    """
    try:
        # Handle special cases
        if value is None or str(value).strip() == '':
            return "0"

        # If a string, remove thousand separators if present
        if isinstance(value, str):
            value = value.replace(',', '')

        # Chuyển đổi sang Decimal và làm tròn
        decimal_value = Decimal(str(value))
        rounded = decimal_value.quantize(
            Decimal('1.' + '0' * digits),
            rounding=ROUND_HALF_UP
        )

        # Chuyển về float
        return float(rounded)

    except (ValueError, TypeError, InvalidOperation) as e:
        logging.error(f"Lỗi khi làm tròn số {value}: {e}")
        return "0"


async def create_pdf_report(estimate_data: Dict[str, Any], project_name: str, language: str = "en") -> BytesIO:
    # Import các thư viện cần thiết
    from reportlab.pdfbase import pdfmetrics
    from reportlab.pdfbase.ttfonts import TTFont
    from reportlab.pdfbase.cidfonts import UnicodeCIDFont
    import os
    # Pass the language value as is, just change jp to ja
    if language == 'jp':
        language = 'ja'
    """
    Create a PDF report from the estimate data using ReportLab with Unicode support for multiple languages
    """
    # Create buffer to save PDF
    buffer = BytesIO()
    
    # Ensure estimate_data has the correct structure or use default empty values
    if 'development_items' in estimate_data and isinstance(estimate_data['development_items'], list):
        # Data has the correct structure
        actual_data = estimate_data
    else:
        # Data is directly in the dictionary provided
        actual_data = estimate_data
    
    # Prepare data for template
    # Calculate total values
    total_backend = 0
    total_frontend = 0
    total_unit_test = 0
    
    # Xử lý development items
    dev_items = actual_data.get('development_items', [])
    processed_dev_items = []
    
    for item in dev_items:
        # Check if item is selected (is_selected = True)
        is_selected = item.get('is_selected', True)  # Default is True if field is not present
        
        # Skip items that are not selected (unchecked) in the edit screen
        if is_selected is False:
            continue
        
        # Extract values with default values
        backend = float(item.get('backend_effort', 0) or 0)  # Ensure not None
        frontend = float(item.get('frontend_effort', 0) or 0)  # Ensure not None
        unit_test = float(item.get('unit_test_effort', 0) or 0)  # Ensure not None
        total = backend + frontend + unit_test
        
        # Update totals
        total_backend += backend
        total_frontend += frontend
        total_unit_test += unit_test
        
        # Add to processed list
        processed_item = {
            'feature': item.get('feature', '') or '',
            'epic': item.get('epic', '') or '',
            'priority': item.get('priority', '') or '',
            'description': item.get('description', '') or '',
            'backend_effort': backend,
            'frontend_effort': frontend,
            'unit_test_effort': unit_test,
            'total': total
        }
        processed_dev_items.append(processed_item)
    
    # Report title - Support multiple languages
    if language == "ja":
        report_title = "プロジェクト見積書"
        section_title = "開発項目の内訳"
        headers = {
            'feature': "機能",
            'epic': "Epic",
            'priority': "優先度",
            'description': "説明",
            'backend': "バックエンド \n(日)",
            'frontend': "フロントエンド \n(日)",
            'unit_test': "単体テスト \n(日)",
            'total': "合計 (日)"
        }
        total_label = "合計"
        summary_title = "プロジェクト概要"
        summary_headers = {
            'category': "カテゴリー",
            'role': "役割",
            'effort': "工数 (日)",
            'unit_rate': "日単価",
            'description': "説明"
        }
        grand_total_label = "総合計"
        total_effort_label = "プロジェクト総工数"
        cost_title = "コスト概要"
        cost_headers = {
            'description': "項目",
            'value': "値"
        }
        day_rate_label = "日単価"
        total_effort_label_cost = "総工数 (日)"
        total_cost_label = "プロジェクト総コスト"
    elif language == "vi":
        report_title = "Ước Tính Dự Án"
        section_title = "Chi Tiết Phát Triển"
        headers = {
            'feature': "Tính năng",
            'epic': "Epic",
            'priority': "Độ ưu tiên",
            'description': "Mô tả chi tiết",
            'backend': "Backend (ngày)",
            'frontend': "Frontend (ngày)",
            'unit_test': "Unit Test (ngày)",
            'total': "Tổng (ngày)"
        }
        total_label = "Tổng cộng"
        summary_title = "Tổng Quan Dự Án"
        summary_headers = {
            'category': "Hạng mục",
            'role': "Vai trò",
            'effort': "Công số (ngày)",
            'unit_rate': "Đơn giá",
            'description': "Mô tả chi tiết"
        }
        grand_total_label = "Tổng cộng"
        total_effort_label = "Tổng công số dự án"
        cost_title = "Tổng Hợp Chi Phí"
        cost_headers = {
            'description': "Mô tả chi tiết",
            'value': "Giá trị"
        }
        total_effort_label_cost = "Tổng công số (ngày)"
        total_cost_label = "Tổng chi phí dự án"
    else:  # Default to English
        report_title = "Project Estimation Report"
        section_title = "Development Breakdown"
        headers = {
            'feature': "Feature",
            'epic': "Epic",
            'priority': "Priority",
            'description': "Description",
            'backend': "Backend\n(days)",
            'frontend': "Frontend\n(days)",
            'unit_test': "Unit Test\n(days)",
            'total': "Total \n(days)"
        }
        total_label = "Total"
        summary_title = "Project Summary"
        summary_headers = {
            'category': "Category",
            'role': "Role",
            'effort': "Effort (days)",
            'unit_rate': "Day Rate",
            'description': "Description"
        }
        grand_total_label = "Grand Total"
        total_effort_label = "Total project effort"
        cost_title = "Cost Summary"
        cost_headers = {
            'description': "Description",
            'value': "Value"
        }
        day_rate_label = "Day Rate"
        total_effort_label_cost = "Total Effort (days)"
        total_cost_label = "Total Project Cost"
    
    # Xử lý summary items
    summary_items = actual_data.get('summary_items', [])
    processed_summary_items = []
    
    # Hàm dịch category sang ngôn ngữ hiện tại
    def translate_category(category: str) -> str:
        # Mapping các category tiếng Anh sang các ngôn ngữ khác
        if language == "ja":
            # Tiếng Nhật
            category_map = {
                'Requirements Definition': '要件定義',
                'Development': '開発',
                'UI/UX Design': 'UI/UXデザイン',
                'Integration Test': '統合テスト',
                'UAT Support': 'UATサポート',
                'Project Management': 'プロジェクト管理'
            }
            return category_map.get(category, category)
        elif language == "vi":
            # Tiếng Việt
            category_map = {
                'Requirements Definition': 'Định nghĩa yêu cầu',
                'Development': 'Phát triển',
                'UI/UX Design': 'Thiết kế UI/UX',
                'Integration Test': 'Kiểm thử tích hợp',
                'UAT Support': 'Hỗ trợ UAT',
                'Project Management': 'Quản lý dự án'
            }
            return category_map.get(category, category)
        # Default to English
        return category
    
    # Hàm dịch description sang ngôn ngữ hiện tại
    def translate_description(description: str) -> str:
        # Mapping các description tiếng Anh sang các ngôn ngữ khác
        if language == "ja":
            # Tiếng Nhật
            description = description\
                .replace('Requirements gathering and analysis', '要件収集と分析')\
                .replace('User interface and experience design', 'ユーザーインターフェースとエクスペリエンスデザイン')\
                .replace('Total development effort', '総開発工数')\
                .replace('Integration and system testing', '統合およびシステムテスト')\
                .replace('User acceptance testing support', 'ユーザー受け入れテストサポート')\
                .replace('Project management overhead', 'プロジェクト管理オーバーヘッド')\
                .replace('of development effort', 'の開発工数')\
                .replace('of all efforts', 'の全工数')
        elif language == "vi":
            # Tiếng Việt
            description = description\
                .replace('Requirements gathering and analysis', 'Thu thập và phân tích yêu cầu')\
                .replace('User interface and experience design', 'Thiết kế giao diện và trải nghiệm người dùng')\
                .replace('Total development effort', 'Tổng công số phát triển')\
                .replace('Integration and system testing', 'Kiểm thử tích hợp và hệ thống')\
                .replace('User acceptance testing support', 'Hỗ trợ kiểm thử chấp nhận người dùng')\
                .replace('Project management overhead', 'Chi phí quản lý dự án')\
                .replace('of development effort', 'công số phát triển')\
                .replace('of all efforts', 'tổng công số')
        # Default to English
        return description
        
    # Hàm dịch role sang ngôn ngữ hiện tại và xét dấu ■
    def translate_role(role: str) -> str:
        # Xét dấu ■ trong role để xác định vai trò
        if role and '■' in role:
            # Đếm số lượng dấu ■ để xác định vai trò
            square_count = role.count('■')
            if square_count >= 10:
                return 'Project Manager'  # Những chức danh đại diện
            elif square_count >= 4:
                return 'Designer'  # Chức danh trung bình
            else:
                return 'Developer'  # Chức danh nhỏ
        
        # Mapping English roles to other languages
        if language == "ja":
            # Japanese
            role_map = {
                'Project Manager': 'プロジェクトマネージャー',
                'Developer': '開発者',
                'Designer': 'デザイナー',
                'Tester': 'テスター',
                'QA': 'QAエンジニア'
            }
            return role_map.get(role, role)
        elif language == "vi":
            # Tiếng Việt
            role_map = {
                'Project Manager': 'Quản lý dự án',
                'Developer': 'Lập trình viên',
                'Designer': 'Nhà thiết kế',
                'Tester': 'Kiểm thử viên',
                'QA': 'Kỹ sư QA'
            }
            return role_map.get(role, role)
        # Default to English
        return role
    
    # Process summary items
    for item in summary_items:
        # Extract values with default values
        category = item.get('category', '') or ''
        role = item.get('role', '') or ''
        effort = float(item.get('effort', 0) or 0)  # Ensure not None
        day_rate = float(item.get('day_rate', 0) or 0)  # Ensure not None
        description = item.get('description', '') or ''
        
        # Translate category, role and description
        translated_category = translate_category(category)
        translated_role = translate_role(role)
        translated_description = translate_description(description)
        
        # Add to processed list
        processed_item = {
            'category': translated_category,
            'role': translated_role,
            'effort': effort,
            'day_rate': day_rate,
            'description': translated_description
        }
        processed_summary_items.append(processed_item)
    
    # Get cost information
    day_rate = actual_data.get('day_rate', 0) or 0  # Đảm bảo không phải None
    total_cost = actual_data.get('total_cost', 0) or 0  # Đảm bảo không phải None
    grand_total = actual_data.get('grand_total', 0) or 0  # Đảm bảo không phải None

    # Hàm định dạng tiền tệ
    def format_currency(amount: float, currency: str = 'USD') -> str:
        """Định dạng số tiền theo đơn vị tiền tệ"""
        try:
            amount = float(amount or 0)
            # Làm tròn đến số nguyên gần nhất
            amount_int = int(round(amount))
            
            # Định dạng số với dấu phân cách hàng nghìn
            formatted = f"{amount_int:,}"
            
            # Thêm ký hiệu tiền tệ
            if currency == 'VND':
                return f"{formatted} ₫"  # Có dấu cách trước ký hiệu
            elif currency == 'JPY':
                return f"¥{formatted}"
            elif currency == 'EUR':
                return f"€{formatted}"
            elif currency == 'GBP':
                return f"£{formatted}"
            else:  # USD mặc định
                return f"${formatted}"
        except (ValueError, TypeError):
            return f"${0:,}"  # Trả về giá trị mặc định nếu có lỗi

    # Lấy thông tin tiền tệ
    currency = actual_data.get('currency', 'USD')
    
    # Tạo paragraph cho số tiền để đảm bảo hiển thị đúng
    def create_currency_paragraph(value: Union[float, int, str], style: ParagraphStyle) -> Paragraph:
        """Tạo paragraph cho số tiền với định dạng phù hợp"""
        try:
            value = float(value or 0)
            formatted_value = format_currency(value, currency)
            return Paragraph(formatted_value, style)
        except (ValueError, TypeError):
            return Paragraph("N/A", style)

    # Ensure we have currency symbol
    currency_symbol = '$'
    if currency == 'VND':
        currency_symbol = '₫'
    elif currency == 'JPY':
        currency_symbol = '¥'
    
    # Calculate total effort
    total_effort = total_backend + total_frontend + total_unit_test
    
    # Use ReportLab to create PDF directly from data
    try:
        # Đảm bảo font đã được đăng ký
        font_registered, default_font = register_fonts()
        
        # Kiểm tra dữ liệu có chứa tiếng Nhật và tiếng Việt không, bất kể language setting là gì
        contains_japanese = False
        contains_vietnamese = False

        # Hàm kiểm tra chuỗi có chứa ký tự tiếng Nhật không
        def contains_japanese_chars(text):
            if not text:
                return False
            # Kiểm tra phạm vi Unicode của tiếng Nhật
            # Hiragana: U+3040-U+309F, Katakana: U+30A0-U+30FF, Kanji: U+4E00-U+9FFF
            for char in text:
                if ('぀' <= char <= 'ゟ') or ('゠' <= char <= 'ヿ') or ('一' <= char <= '鿿'):
                    return True
            return False

        # Hàm kiểm tra chuỗi có chứa ký tự tiếng Việt không
        def contains_vietnamese_chars(text):
            if not text:
                return False
            # Kiểm tra các ký tự đặc biệt của tiếng Việt
            vietnamese_chars = set([
                'à', 'á', 'ả', 'ã', 'ạ', 'ă', 'ằ', 'ắ', 'ẳ', 'ẵ', 'ặ',
                'â', 'ầ', 'ấ', 'ẩ', 'ẫ', 'ậ', 'è', 'é', 'ẻ', 'ẽ', 'ẹ',
                'ê', 'ề', 'ế', 'ể', 'ễ', 'ệ', 'ì', 'í', 'ỉ', 'ĩ', 'ị',
                'ò', 'ó', 'ỏ', 'õ', 'ọ', 'ô', 'ồ', 'ố', 'ổ', 'ỗ', 'ộ',
                'ơ', 'ờ', 'ớ', 'ở', 'ỡ', 'ợ', 'ù', 'ú', 'ủ', 'ũ', 'ụ',
                'ư', 'ừ', 'ứ', 'ử', 'ữ', 'ự', 'ỳ', 'ý', 'ỷ', 'ỹ', 'ỵ',
                'đ', 'À', 'Á', 'Ả', 'Ã', 'Ạ', 'Ă', 'Ằ', 'Ắ', 'Ẳ', 'Ẵ', 'Ặ',
                'Â', 'Ầ', 'Ấ', 'Ẩ', 'Ẫ', 'Ậ', 'È', 'É', 'Ẻ', 'Ẽ', 'Ẹ',
                'Ê', 'Ề', 'Ế', 'Ể', 'Ễ', 'Ệ', 'Ì', 'Í', 'Ỉ', 'Ĩ', 'Ị',
                'Ò', 'Ó', 'Ỏ', 'Õ', 'Ọ', 'Ô', 'Ồ', 'Ố', 'Ổ', 'Ỗ', 'Ộ',
                'Ơ', 'Ờ', 'Ớ', 'Ở', 'Ỡ', 'Ợ', 'Ù', 'Ú', 'Ủ', 'Ũ', 'Ụ',
                'Ư', 'Ừ', 'Ứ', 'Ử', 'Ữ', 'Ự', 'Ỳ', 'Ý', 'Ỷ', 'Ỹ', 'Ỵ', 'Đ'
            ])
            return any(char in vietnamese_chars for char in text)

        # Hàm kiểm tra và xử lý văn bản hỗn hợp
        def process_mixed_text(text, style, jp_font_name=None):
            """Xử lý văn bản hỗn hợp nhiều ngôn ngữ"""
            if not text:
                return Paragraph("", style)
                
            # Tạo một bản sao của style để không ảnh hưởng đến style gốc
            current_style = ParagraphStyle(
                f"{style.name}_mixed",
                parent=style,
                fontName=style.fontName
            )
            
            # Nếu không có ký tự tiếng Nhật hoặc không có font tiếng Nhật, trả về bình thường
            if not contains_japanese_chars(text) or not jp_font_name:
                return Paragraph(text, current_style)
                
            # Nếu có ký tự tiếng Nhật, chia nhỏ và xử lý từng đoạn
            parts = []
            current_part = ""
            current_is_jp = False
            
            for char in text:
                char_is_jp = contains_japanese_chars(char)
                
                if char_is_jp != current_is_jp and current_part:
                    if current_is_jp:
                        # Áp dụng font tiếng Nhật cho đoạn tiếng Nhật
                        jp_style = ParagraphStyle(
                            f"{style.name}_jp",
                            parent=current_style,
                            fontName=jp_font_name
                        )
                        parts.append(Paragraph(current_part, jp_style))
                    else:
                        parts.append(Paragraph(current_part, current_style))
                    current_part = ""
                
                current_part += char
                current_is_jp = char_is_jp
            
            # Thêm phần còn lại
            if current_part:
                if current_is_jp:
                    jp_style = ParagraphStyle(
                        f"{style.name}_jp",
                        parent=current_style,
                        fontName=jp_font_name
                    )
                    parts.append(Paragraph(current_part, jp_style))
                else:
                    parts.append(Paragraph(current_part, current_style))
            
            # Kết hợp các phần lại với nhau
            if len(parts) == 1:
                return parts[0]
            else:
                # Tạo một Paragraph chứa tất cả các phần
                return KeepTogether(parts)

        # Khởi tạo biến để lưu font name
        font_name = 'Helvetica'
        font_name_bold = 'Helvetica-Bold'
        jp_font_name = None
        
        # Kiểm tra dữ liệu có chứa tiếng Nhật và tiếng Việt không
        contains_japanese = contains_japanese_chars(project_name) or any(
            contains_japanese_chars(str(value)) 
            for value in actual_data.values() 
            if isinstance(value, str)
        )
        
        contains_vietnamese = contains_vietnamese_chars(project_name) or any(
            contains_vietnamese_chars(str(value))
            for value in actual_data.values()
            if isinstance(value, str)
        )

        # Chọn font dựa trên nội dung văn bản
        print(f"Font selection - Language: {language}, Contains Japanese: {contains_japanese}, Contains Vietnamese: {contains_vietnamese}")
        
        # Kiểm tra font có sẵn
        available_fonts = pdfmetrics.getRegisteredFontNames()
        print(f"Available fonts: {available_fonts}")
        
        try:
            # Đăng ký font DejaVu nếu chưa có
            if 'DejaVuSans' not in available_fonts:
                # Đường dẫn đến thư mục chứa font
                font_dir = os.path.join(os.path.dirname(__file__), '..', '..', 'fonts')
                dejavu_sans_path = os.path.join(font_dir, 'DejaVuSans.ttf')
                dejavu_sans_bold_path = os.path.join(font_dir, 'DejaVuSans-Bold.ttf')
                
                if os.path.exists(dejavu_sans_path):
                    pdfmetrics.registerFont(TTFont('DejaVuSans', dejavu_sans_path))
                    pdfmetrics.registerFont(TTFont('DejaVuSans-Bold', dejavu_sans_bold_path))
                    print("Registered DejaVuSans fonts")
            
            # Cập nhật danh sách font có sẵn sau khi đăng ký
            available_fonts = pdfmetrics.getRegisteredFontNames()
            
            # Chọn font chính
            if 'DejaVuSans' in available_fonts:
                font_name = 'DejaVuSans'
                font_name_bold = 'DejaVuSans-Bold'
                print("Using DejaVuSans as primary font")
            
            # Đăng ký font HeiseiMin-W3 cho tiếng Nhật nếu cần
            if (language == "ja" or contains_japanese):
                if 'HeiseiMin-W3' not in available_fonts:
                    try:
                        pdfmetrics.registerFont(UnicodeCIDFont('HeiseiMin-W3'))
                        print("Registered HeiseiMin-W3 for Japanese characters")
                        jp_font_name = 'HeiseiMin-W3'
                    except Exception as e:
                        print(f"Warning: Could not register HeiseiMin-W3 font: {e}")
                else:
                    jp_font_name = 'HeiseiMin-W3'
                    print("Using existing HeiseiMin-W3 for Japanese characters")
                    
        except Exception as e:
            print(f"Warning: Error processing fonts: {e}")
            # Fallback to basic fonts
            font_name = 'Helvetica'
            font_name_bold = 'Helvetica-Bold'
            jp_font_name = None

        def create_paragraph_with_fallback(text, style, multiline=False):
            """
            Tạo Paragraph với font fallback và hỗ trợ xuống dòng
            
            Args:
                text: Chuỗi văn bản cần xử lý
                style: Đối tượng ParagraphStyle
                multiline: Nếu True, sẽ xử lý ký tự \n thành ngắt dòng
                
            Returns:
                Đối tượng Paragraph đã được xử lý
            """
            if not text:
                return Paragraph("", style)
                
            try:
                # Đảm bảo text là string và có encoding đúng
                if isinstance(text, bytes):
                    text = text.decode('utf-8')
                text_str = str(text).strip()
                
                # Xử lý xuống dòng nếu được yêu cầu
                if multiline and '\n' in text_str:
                    text_str = text_str.replace('\n', '<br/>')
                
                # Tạo bản sao của style để không ảnh hưởng đến style gốc
                current_style = ParagraphStyle(
                    f"{style.name}_dynamic",
                    parent=style,
                    fontName=style.fontName
                )
                
                # Nếu có ký tự tiếng Nhật và có font tiếng Nhật, sử dụng font đó
                if jp_font_name and any(0x3000 <= ord(char) <= 0x9fff for char in text_str):
                    current_style.fontName = jp_font_name
                
                # Tạo paragraph với style hiện tại
                return Paragraph(text_str, current_style)
                
            except Exception as e:
                print(f"Warning: Error creating paragraph: {e}")
                
                # Fallback: Thử với font DejaVu nếu có sẵn
                if 'DejaVuSans' in available_fonts and style.fontName != 'DejaVuSans':
                    try:
                        fallback_style = ParagraphStyle(
                            f'{style.name}_fallback',
                            parent=style,
                            fontName='DejaVuSans'
                        )
                        return Paragraph(text_str, fallback_style)
                    except Exception as e2:
                        print(f"Warning: Error with fallback font: {e2}")
                
                # Fallback cuối cùng: sử dụng Helvetica
                try:
                    basic_style = ParagraphStyle(
                        f'{style.name}_basic',
                        parent=style,
                        fontName='Helvetica'
                    )
                    return Paragraph(text_str, basic_style)
                except Exception as e3:
                    print(f"Warning: Error with basic font: {e3}")
                    # Cuối cùng: xóa các ký tự không hỗ trợ
                    clean_text = ''.join(char if ord(char) < 0x10000 else '?' for char in text_str)
                    return Paragraph(clean_text, basic_style)

            try:
                # Đảm bảo text là string và có encoding đúng
                if isinstance(text, bytes):
                    text = text.decode('utf-8')
                text_str = str(text).strip()

                # Tạo bản sao của style để không ảnh hưởng đến style gốc
                current_style = ParagraphStyle(
                    f"{style.name}_dynamic",
                    parent=style,
                    fontName=style.fontName
                )
                
                # Nếu có ký tự tiếng Nhật và có font tiếng Nhật, sử dụng font đó
                if jp_font_name and any(0x3000 <= ord(char) <= 0x9fff for char in text_str):
                    current_style.fontName = jp_font_name
                
                # Thử tạo paragraph với font hiện tại
                return Paragraph(text_str, current_style)
                
            except Exception as e:
                print(f"Warning: Error creating paragraph with font {style.fontName}: {e}")
                
                # Fallback 1: Thử với font DejaVu nếu chưa phải
                if 'DejaVuSans' in available_fonts and style.fontName != 'DejaVuSans':
                    try:
                        fallback_style = ParagraphStyle(
                            f'{style.name}_fallback',
                            parent=style,
                            fontName='DejaVuSans'
                        )
                        return Paragraph(text_str, fallback_style)
                    except Exception as e2:
                        print(f"Warning: Error with DejaVu fallback font: {e2}")
                
                # Fallback 2: Thử với font mặc định của hệ thống
                try:
                    basic_style = ParagraphStyle(
                        f'{style.name}_basic',
                        parent=style,
                        fontName='Helvetica'
                    )
                    # Thử với văn bản gốc trước
                    return Paragraph(text_str, basic_style)
                except Exception as e3:
                    print(f"Warning: Error with basic font: {e3}")
                    # Cuối cùng: xóa các ký tự không hỗ trợ
                    clean_text = ''.join(char if ord(char) < 0x10000 else '?' for char in text_str)
                    return Paragraph(clean_text, basic_style)

      # Define PDF metadata
        pdf_metadata = {
            'title': f"{report_title} - {project_name}",
            'author': 'HapoEstimate',
            'subject': f'Project Estimate for {project_name}',
            'creator': 'HapoEstimate',
            'producer': 'HapoEstimate',
            'keywords': ['estimate', 'project', 'development', 'cost']
        }

        # Create PDF buffer with larger size
        pagesize = landscape(letter)
        doc = SimpleDocTemplate(
            buffer, 
            pagesize=pagesize, 
            rightMargin=24,  # Reduce margin to make room for table
            leftMargin=24,
            topMargin=36,
            bottomMargin=36,
            title=pdf_metadata['title'],
            author=pdf_metadata['author'],
            subject=pdf_metadata['subject'],
            creator=pdf_metadata['creator'],
            producer=pdf_metadata['producer'],
            keywords=','.join(pdf_metadata['keywords']),
            # Quan trọng: Kích hoạt nhúng font
            embedTTF=True,
            # Tối ưu hóa cho việc hiển thị trên web
            _pageBreakQuick=1,
            # Tăng độ phân giải ảnh đồ họa
            invariant=1
        )
        
        # Styles with font support for Unicode and text wrapping configuration
        styles = getSampleStyleSheet()
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=16,
            spaceAfter=12,
            alignment=TA_CENTER,
            fontName=font_name_bold
        )
        subtitle_style = ParagraphStyle(
            'CustomSubtitle',
            parent=styles['Heading2'],
            fontSize=14,
            spaceAfter=10,
            alignment=TA_CENTER,
            fontName=font_name_bold
        )
        header_style = ParagraphStyle(
            'CustomHeader',
            parent=styles['Heading3'],
            fontSize=12,
            spaceAfter=6,
            fontName=font_name_bold
        )
        normal_style = ParagraphStyle(
            'Normal',
            parent=styles['Normal'],
            fontSize=10,
            fontName=font_name,
            wordWrap='CJK',  # Configure word wrap for Unicode
            leading=12       # Line spacing
        )
        
        # Style for table header with better word wrap configuration
        header_cell_style = ParagraphStyle(
            'HeaderCell',
            parent=styles['Normal'],
            fontSize=10,           # Reduce font size to display more text
            fontName=font_name_bold,
            alignment=1,          # CENTER
            wordWrap='CJK',       # Configure word wrap for Unicode
            leading=10,           # Line spacing
            spaceAfter=2,         # Add space after each paragraph
            spaceBefore=2,        # Add space before each paragraph
            textColor=colors.whitesmoke  # White text color for contrast with gray background
        )
        
        # Story (content)
        story = []
        
        # Report title and project information
        story.append(create_paragraph_with_fallback(report_title, title_style))
        
        # Sử dụng process_mixed_text cho project_name để hỗ trợ đa ngôn ngữ
        project_name_paragraph = process_mixed_text(project_name, subtitle_style, jp_font_name)
        story.append(project_name_paragraph)
        story.append(Spacer(1, 20))

        # Development Items table
        story.append(create_paragraph_with_fallback(section_title, header_style))
        
        # Create table data (skip priority column) and use Paragraph for header to support text wrapping
        # Sử dụng tham số multiline=True cho các header cần xuống dòng
        dev_data = [
            [create_paragraph_with_fallback(headers['feature'], header_cell_style),
             create_paragraph_with_fallback(headers['epic'], header_cell_style),
             create_paragraph_with_fallback(headers['description'], header_cell_style),
             create_paragraph_with_fallback(headers['backend'], header_cell_style, multiline=True),
             create_paragraph_with_fallback(headers['frontend'], header_cell_style, multiline=True),
             create_paragraph_with_fallback(headers['unit_test'], header_cell_style, multiline=True),
             create_paragraph_with_fallback(headers['total'], header_cell_style, multiline=True)]
        ]
        
        # Add development items (skip priority column)
        for item in processed_dev_items:
            dev_data.append([
                process_mixed_text(item['feature'], normal_style, jp_font_name),
                process_mixed_text(item['epic'], normal_style, jp_font_name),
                process_mixed_text(item['description'], normal_style, jp_font_name),
                f"{round_half_up(item['backend_effort'], 2):,.2f}",
                f"{round_half_up(item['frontend_effort'], 2):,.2f}",
                f"{round_half_up(item['unit_test_effort'], 2):,.2f}",
                f"{round_half_up(item['total'], 2):,.2f}"
            ])
        # Hàm tính chiều rộng dựa trên độ dài văn bản
        def calculate_width(text, min_width=0.7, max_width=3.0, is_header=False):
            if not text:
                return min_width
                
            # Chuyển đổi text thành chuỗi nếu chưa phải
            text_str = str(text)
            
            # Nếu là header, tính toán khác đi để đảm bảo đủ rộng
            if is_header:
                # Với header, tính toán dựa trên số ký tự tiếng Nhật
                # Mỗi ký tự tiếng Nhật cần khoảng 0.2 inch
                jp_chars = sum(1 for char in text_str if ord(char) > 127)
                other_chars = len(text_str) - jp_chars
                # Tính chiều rộng ước tính
                calculated_width = (jp_chars * 0.25) + (other_chars * 0.1)
                # Đảm bảo tối thiểu 1.5 inch cho các cột có tiêu đề tiếng Nhật
                return max(1.5, min(max_width, calculated_width))
            
            # Với nội dung thông thường
            # Tính số ký tự tiếng Nhật và ký tự thường
            jp_chars = sum(1 for char in text_str if ord(char) > 127)
            other_chars = len(text_str) - jp_chars
            
            # Tính chiều rộng ước tính
            calculated_width = (jp_chars * 0.15) + (other_chars * 0.08)
            
            # Áp dụng giới hạn min và max
            return max(min_width, min(max_width, calculated_width))
        
        # Tính chiều rộng cho từng cột
        # Với các cột có tiêu đề tiếng Nhật, ưu tiên kích thước của tiêu đề
        header_texts = [
            headers['feature'],
            headers['epic'],
            headers['description'],
            headers['backend'],
            headers['frontend'],
            headers['unit_test'],
            headers['total']
        ]
        
        # Tính chiều rộng cho từng cột dựa trên tiêu đề
        col_widths = [calculate_width(text, is_header=True) for text in header_texts]
        
        # Điều chỉnh thêm dựa trên nội dung các ô
        for row in dev_data[1:]:  # Bỏ qua hàng tiêu đề
            for i, cell in enumerate(row):
                if hasattr(cell, 'text'):
                    text = cell.text
                else:
                    text = str(cell) if cell is not None else ""
                
                # Cập nhật chiều rộng nếu nội dung dài hơn
                col_widths[i] = max(col_widths[i], calculate_width(text))
        
        # Đảm bảo chiều rộng tối thiểu cho các cột
        min_widths = [
            1.5,  # Feature
            1.2,  # Epic
            2.5,  # Description
            2,  # Backend
            2,  # Frontend (rộng hơn cho tiếng Nhật)
            1.5,  # Unit Test
            1.2   # Total
        ]
        
        # Kết hợp chiều rộng đã tính với chiều rộng tối thiểu
        col_widths = [max(w, min_w * inch) for w, min_w in zip(col_widths, min_widths)]
        
        # Giới hạn chiều rộng tối đa cho cột mô tả
        col_widths[2] = min(col_widths[2], 4.0 * inch)  # Tối đa 4 inch cho cột mô tả
        # Dùng Paragraph cho các ô số để tự động xuống dòng nếu vẫn quá dài
        for i in range(1, len(dev_data)):
            for col in [3,4,5,6]:
                num_str = str(dev_data[i][col])
                style = ParagraphStyle('NumCell', parent=normal_style, alignment=2, fontSize=9, wordWrap='CJK')
                dev_data[i][col] = Paragraph(num_str, style)

        
        # Create bold style for text
        bold_style = ParagraphStyle(
            'Bold',
            parent=normal_style,
            fontName=font_name_bold
        )
        
        # Add total row (skip priority column)
        dev_data.append([
            create_paragraph_with_fallback(total_label, bold_style),
            "", "",  # Only 2 empty columns instead of 3
            Paragraph(f"{round_half_up(total_backend, 2):,.2f}", ParagraphStyle('NumCell', parent=bold_style, alignment=2, fontSize=9, wordWrap='CJK')),
            Paragraph(f"{round_half_up(total_frontend, 2):,.2f}", ParagraphStyle('NumCell', parent=bold_style, alignment=2, fontSize=9, wordWrap='CJK')),
            Paragraph(f"{round_half_up(total_unit_test, 2):,.2f}", ParagraphStyle('NumCell', parent=bold_style, alignment=2, fontSize=9, wordWrap='CJK')),
            Paragraph(f"{round_half_up(total_effort, 2):,.2f}", ParagraphStyle('NumCell', parent=bold_style, alignment=2, fontSize=9, wordWrap='CJK'))
        ])

        # Thiết lập độ rộng cột cố định phù hợp
        # Tổng chiều rộng không nên vượt quá 10-11 inch để vừa trang giấy A4
        col_widths = [
            1.6 * inch,  # Feature
            1.3 * inch,  # Epic
            3.2 * inch,  # Description
            1.0 * inch,  # Backend
            1.4 * inch,  # Frontend (rộng hơn cho tiếng Nhật)
            1.0 * inch,  # Unit Test
            1.0 * inch   # Total
        ]
        
        # Điều chỉnh style cho phép text xuống dòng trong ô
        for row in dev_data:
            for i, cell in enumerate(row):
                if hasattr(cell, 'get'):
                    cell.keepWithNext = True  # Giữ các dòng của cùng một ô với nhau
        
        # Tạo bảng với độ rộng cột đã tính toán
        dev_table = Table(dev_data, colWidths=col_widths, repeatRows=1, rowHeights=[0.6*inch] + [None] * (len(dev_data)-1))
        dev_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('FONTNAME', (0, 0), (-1, 0), font_name_bold),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 8),
            ('TOPPADDING', (0, 0), (-1, 0), 8),
            ('LEFTPADDING', (0, 0), (-1, 0), 4),
            ('RIGHTPADDING', (0, 0), (-1, 0), 4),
            ('BACKGROUND', (0, -1), (-1, -1), colors.lightgrey),
            ('FONTNAME', (0, -1), (-1, -1), font_name_bold),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('ALIGN', (4, 1), (-1, -1), 'RIGHT'),
            ('FONTNAME', (0, 1), (-1, -1), font_name),
            ('FONTSIZE', (0, 1), (-1, -1), 9),
            ('TOPPADDING', (0, 1), (-1, -1), 5),
            ('BOTTOMPADDING', (0, 1), (-1, -1), 5),
            ('FONTWEIGHT', (0, 0), (-1, 0), 'BOLD')
        ]))
        story.append(dev_table)


        # Add page break before summary section        # Add page break before summary section
        story.append(PageBreak())

        # Summary Items table
        story.append(create_paragraph_with_fallback(summary_title, header_style))

        # Create summary data with Paragraph for headers to support text wrapping
        summary_data = [[
            create_paragraph_with_fallback(summary_headers['category'], header_cell_style),
            create_paragraph_with_fallback(summary_headers['role'], header_cell_style),
            create_paragraph_with_fallback(summary_headers['effort'], header_cell_style),
            create_paragraph_with_fallback(summary_headers['unit_rate'], header_cell_style),
            create_paragraph_with_fallback(summary_headers['description'], header_cell_style)
        ]]

        # Add summary items with fixed column widths
        # max_len_effort = max([len(f"{round_half_up(item['effort'], 1):.1f}") for item in processed_summary_items] + [len(f"{round_half_up(grand_total, 1):.1f}")])
        # max_len_rate = max([len(f"{currency_symbol}{item['day_rate']:,.0f}") for item in processed_summary_items] + [2])
        # def col_width_num(l): return max(0.8, min(2.0, l*0.18))
        # effort_col_width = col_width_num(max_len_effort)*inch
        # rate_col_width = col_width_num(max_len_rate)*inch
        # Sử dụng chiều rộng cố định thay vì tính toán động
        effort_col_width = 1.0 * inch
        rate_col_width = 1.5 * inch

        for item in processed_summary_items:
            summary_data.append([
                create_paragraph_with_fallback(item['category'], normal_style),
                create_paragraph_with_fallback(item['role'], normal_style),
                Paragraph(f"{round_half_up(item['effort'], 2):,.2f}", ParagraphStyle('NumCell', parent=normal_style, alignment=2, fontSize=9, wordWrap='CJK')),
                Paragraph(f"{currency_symbol}{item['day_rate']:,.0f}", ParagraphStyle('NumCell', parent=normal_style, alignment=2, fontSize=9, wordWrap='CJK')),
                create_paragraph_with_fallback(item['description'], normal_style)
            ])

        # Add total row using bold_style instead of HTML tag
        summary_data.append([
            create_paragraph_with_fallback(grand_total_label, bold_style),
            "",
            Paragraph(f"{round_half_up(grand_total, 2):,.2f}", ParagraphStyle('NumCell', parent=bold_style, alignment=2, fontSize=9, wordWrap='CJK')),
            "",
            create_paragraph_with_fallback(total_effort_label, normal_style)
        ])

        # Create summary table với colWidths động cho cột số
        col_widths = [2*inch, 1.5*inch, effort_col_width, rate_col_width, 2.7*inch]
        summary_table = Table(summary_data, colWidths=col_widths, repeatRows=1, rowHeights=[0.6*inch] + [None] * (len(summary_data)-1))
        summary_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('FONTNAME', (0, 0), (-1, 0), font_name_bold),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 8),
            ('TOPPADDING', (0, 0), (-1, 0), 8),
            ('LEFTPADDING', (0, 0), (-1, 0), 4),
            ('RIGHTPADDING', (0, 0), (-1, 0), 4),
            ('BACKGROUND', (0, -1), (-1, -1), colors.lightgrey),
            ('FONTNAME', (0, -1), (-1, -1), font_name_bold),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('ALIGN', (2, 1), (2, -1), 'RIGHT'),
            ('ALIGN', (3, 1), (3, -1), 'RIGHT'),
            ('FONTNAME', (0, 1), (-1, -1), font_name),
            ('FONTSIZE', (0, 1), (-1, -1), 9),
            ('TOPPADDING', (0, 1), (-1, -1), 5),
            ('BOTTOMPADDING', (0, 1), (-1, -1), 5),
        ]))
        story.append(summary_table)

        # Add space before cost summary
        story.append(Spacer(1, 30))

        # Cost Summary table
        story.append(create_paragraph_with_fallback(cost_title, header_style))


        # Create cost table data with Paragraph for header to support text wrapping
        cost_data = [
            [create_paragraph_with_fallback(cost_headers['description'], header_cell_style),
             create_paragraph_with_fallback(cost_headers['value'], header_cell_style)]
        ]

        # Tạo style cho số tiền
        currency_style = ParagraphStyle(
            'CurrencyStyle',
            parent=normal_style,
            fontName=font_name,
            alignment=TA_RIGHT,
            fontSize=10,
            textColor=colors.black
        )
        
        # Tạo style cho tổng tiền (in đậm)
        total_currency_style = ParagraphStyle(
            'TotalCurrencyStyle',
            parent=currency_style,
            fontName=font_name_bold,
            fontSize=11,
            textColor=colors.black
        )
        
        # Định dạng số tiền
        grand_total_str = f"{grand_total:,.2f}"
        formatted_cost = create_currency_paragraph(total_cost, total_currency_style)
        # Xác định độ dài số lớn nhất để set colWidths hợp lý
        # max_len_val = max(len(grand_total_str), len(formatted_cost), 8)
        # def col_width_val(l): return max(2.0, min(3.5, l*0.18))
        # val_col_width = col_width_val(max_len_val)*inch
        # Sử dụng chiều rộng cố định cho cột giá trị
        val_col_width = 2.5 * inch
        cost_data.append([
            create_paragraph_with_fallback(total_effort_label_cost, normal_style),
            Paragraph(grand_total_str, ParagraphStyle(
                'NumCell', 
                parent=normal_style, 
                alignment=TA_RIGHT, 
                fontSize=10, 
                fontName=font_name,
                wordWrap='CJK'
            ))
        ])
        cost_data.append([
            create_paragraph_with_fallback(total_cost_label, bold_style),
            formatted_cost  # Sử dụng paragraph đã được định dạng sẵn
        ])
        
        # Create cost table với colWidths động cho cột số
        cost_table = Table(cost_data, colWidths=[4*inch, val_col_width], repeatRows=1, rowHeights=[0.6*inch] + [None] * (len(cost_data)-1))
        cost_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('FONTNAME', (0, 0), (-1, 0), font_name_bold),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 8),
            ('TOPPADDING', (0, 0), (-1, 0), 8),
            ('LEFTPADDING', (0, 0), (-1, 0), 4),
            ('RIGHTPADDING', (0, 0), (-1, 0), 4),
            ('BACKGROUND', (0, -1), (-1, -1), colors.lightgrey),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('ALIGN', (1, 1), (1, -1), 'RIGHT'),
            ('FONTNAME', (0, 1), (-1, -1), font_name),
            ('FONTSIZE', (0, 1), (-1, -1), 9),
            ('TOPPADDING', (0, 1), (-1, -1), 5),
            ('BOTTOMPADDING', (0, 1), (-1, -1), 5),
        ]))
        story.append(cost_table)
        
        # Add report creation date at the end
        story.append(Spacer(1, 30))
        
        # Build PDF
        doc.build(story)
    except Exception as e:
        # Log error and raise exception
        logging.error(f"Error generating PDF with ReportLab: {e}")
        raise ValueError(f"Error generating PDF: {str(e)}")
    
    # Reset buffer position
    buffer.seek(0)
    return buffer