import secrets
import string
from datetime import datetime, timedelta, timezone
from typing import Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update

from ..models.user import User
from ..services.auth_service import get_password_hash
from ..services.email_service import email_service


def generate_reset_token() -> str:
    """Generate a secure random token for password reset"""
    alphabet = string.ascii_letters + string.digits
    return ''.join(secrets.choice(alphabet) for _ in range(64))


async def create_password_reset_token(db: AsyncSession, email: str, language: str = "en") -> Optional[str]:
    """Create a password reset token for a user and send email"""
    try:
        print(f"Creating password reset token for email: {email}")
        
        # Find user by email
        stmt = select(User).where(User.email == email, User.is_active == True)
        result = await db.execute(stmt)
        user = result.scalar_one_or_none()
        
        if not user:
            print(f"User not found for email: {email}")
            # Return None but don't indicate if user exists (security)
            return None
        
        # Store user details before any commits to avoid lazy loading issues
        user_id = user.id
        user_email = user.email
        is_active = user.is_active
        
        print(f"User found: {user_id}, Active: {is_active}")
        
        # Generate reset token
        reset_token = generate_reset_token()
        expires_at = datetime.now(timezone.utc) + timedelta(hours=1)  # Token valid for 1 hour
        
        print(f"Generated token: {reset_token[:8]}... (expires at: {expires_at})")
        
        # Update user with reset token
        stmt = (
            update(User)
            .where(User.id == user_id)
            .values(
                reset_password_token=reset_token,
                reset_password_expires_at=expires_at
            )
        )
        await db.execute(stmt)
        await db.commit()
        
        print(f"Token saved to database for user {user_id}")
        
        # Send password reset email using email service directly
        print(f"Attempting to send password reset email to: {user_email}")
        try:
            email_service.send_password_reset_email(user_email, reset_token, language)
            email_sent = True
            print(f"Password reset email queued successfully for {user_email}")
        except Exception as email_error:
            print(f"Failed to send password reset email: {email_error}")
            email_sent = False
        
        print(f"Email sent result: {email_sent}")
        
        if email_sent:
            print(f"Password reset email sent successfully to {user_email}")
            return reset_token
        else:
            print(f"Failed to send password reset email to {user_email}")
            # If email failed, remove the reset token
            stmt = (
                update(User)
                .where(User.id == user_id)
                .values(
                    reset_password_token=None,
                    reset_password_expires_at=None
                )
            )
            await db.execute(stmt)
            await db.commit()
            print(f"Reset token removed from database due to email failure")
            return None
            
    except Exception as e:
        await db.rollback()
        print(f"Error creating password reset token: {e}")
        import traceback
        traceback.print_exc()
        return None


async def verify_reset_token(db: AsyncSession, token: str) -> Optional[User]:
    """Verify a password reset token and return the user if valid"""
    try:
        current_time = datetime.now(timezone.utc)
        
        # Find user by token and check if token is not expired
        stmt = select(User).where(
            User.reset_password_token == token,
            User.reset_password_expires_at > current_time,
            User.is_active == True
        )
        result = await db.execute(stmt)
        user = result.scalar_one_or_none()
        
        return user
        
    except Exception as e:
        print(f"Error verifying reset token: {e}")
        return None


async def reset_user_password(db: AsyncSession, token: str, new_password: str) -> bool:
    """Reset user password using a valid token"""
    try:
        # Verify token and get user
        user = await verify_reset_token(db, token)
        if not user:
            return False
        
        # Hash the new password
        hashed_password = get_password_hash(new_password)
        
        # Update user password and clear reset token
        stmt = (
            update(User)
            .where(User.id == user.id)
            .values(
                hashed_password=hashed_password,
                reset_password_token=None,
                reset_password_expires_at=None
            )
        )
        await db.execute(stmt)
        await db.commit()
        
        return True
        
    except Exception as e:
        await db.rollback()
        print(f"Error resetting password: {e}")
        return False


async def cleanup_expired_tokens(db: AsyncSession) -> int:
    """Clean up expired password reset tokens"""
    try:
        current_time = datetime.now(timezone.utc)
        
        # Clear expired tokens
        stmt = (
            update(User)
            .where(User.reset_password_expires_at < current_time)
            .values(
                reset_password_token=None,
                reset_password_expires_at=None
            )
        )
        result = await db.execute(stmt)
        await db.commit()
        
        return result.rowcount
        
    except Exception as e:
        await db.rollback()
        print(f"Error cleaning up expired tokens: {e}")
        return 0 