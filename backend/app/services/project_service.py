import os
import shutil
from fastapi import UploadFile
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, func, or_
from typing import List, Optional, Dict, Any
from fastapi import HTTPException

from ..models.project import Project
from ..schemas.project import ProjectCreate, ProjectUpdate
from ..core.config import settings

async def get_project_by_id(db: AsyncSession, project_id: int, user_id: int) -> Optional[Project]:
    """
    Get a project by ID for a specific user
    """
    stmt = (
        select(Project)
        .where(Project.id == project_id)
        .where(Project.user_id == user_id)
    )
    result = await db.execute(stmt)
    return result.scalar_one_or_none()

async def get_project(db: AsyncSession, project_id: int) -> Optional[Dict[str, Any]]:
    """
    Get a project by ID without user check (for internal use)
    """
    stmt = select(Project).where(Project.id == project_id)
    result = await db.execute(stmt)
    project = result.scalar_one_or_none()
    
    if not project:
        return None
    
    # Convert to dictionary for JSON serialization
    return {
        "id": project.id,
        "name": project.name,
        "description": project.description,
        "user_id": project.user_id,
        "software_type": project.software_type,
        "technology_stack": project.technology_stack,
        "requirements_text": project.requirements_text,
        "requirements_file_path": project.requirements_file_path,
        "created_at": project.created_at,
        "updated_at": project.updated_at
    }

async def get_projects_by_user(
    db: AsyncSession, user_id: int, skip: int = 0, limit: int = 10
) -> Dict[str, Any]:
    """
    Get projects for a specific user with pagination metadata
    """
    # Get total count of projects for pagination
    count_stmt = select(func.count()).select_from(
        select(Project).where(Project.user_id == user_id).subquery()
    )
    total_count = await db.scalar(count_stmt)
    
    # Get projects with pagination
    from sqlalchemy.orm import selectinload
    stmt = (
        select(Project)
        .where(Project.user_id == user_id)
        .order_by(Project.created_at.desc())  # Order by creation date, newest first
        .offset(skip)
        .limit(limit)
        .options(selectinload(Project.estimates))
    )
    result = await db.execute(stmt)
    projects = result.scalars().all()
    
    # Convert Project objects to dictionaries for JSON serialization
    project_list = []
    for project in projects:
        # Lấy các total_cost của estimate
        total_costs = [e.total_cost for e in project.estimates if e.total_cost is not None]
        if total_costs:
            estimate_min = min(total_costs)
            estimate_max = max(total_costs)
        else:
            estimate_min = None
            estimate_max = None
        project_dict = {
            "id": project.id,
            "name": project.name,
            "description": project.description,
            "user_id": project.user_id,
            "software_type": project.software_type,
            "technology_stack": project.technology_stack,
            "requirements_file_path": project.requirements_file_path,
            "created_at": project.created_at,
            "updated_at": project.updated_at,
            "estimate_min": estimate_min,
            "estimate_max": estimate_max
        }
        project_list.append(project_dict)
    
    # Return projects with pagination metadata
    return {
        "items": project_list,
        "total": total_count,
        "page": skip // limit + 1,
        "pages": (total_count + limit - 1) // limit if total_count > 0 else 1,  # Ceiling division, min 1 page
        "size": limit
    }

async def create_project(db: AsyncSession, project_in: ProjectCreate, user_id: int) -> Project:
    """
    Create a new project
    """
    project = Project(
        **project_in.dict(),
        user_id=user_id
    )
    db.add(project)
    await db.commit()
    await db.refresh(project)
    return project

async def update_project(db: AsyncSession, project_id: int, project_in: ProjectUpdate, user_id: int) -> Optional[Project]:
    """
    Update a project
    """
    project = await get_project_by_id(db, project_id, user_id)
    if not project:
        return None
    
    update_data = project_in.dict(exclude_unset=True)
    for key, value in update_data.items():
        setattr(project, key, value)
    
    await db.commit()
    await db.refresh(project)
    return project

async def delete_project(db: AsyncSession, project_id: int, user_id: int) -> bool:
    """
    Delete a project
    """
    project = await get_project_by_id(db, project_id, user_id)
    if not project:
        return False
    
    await db.delete(project)
    await db.commit()
    return True

async def upload_requirements_file(
    db: AsyncSession, project_id: int, user_id: int, file: UploadFile
) -> Optional[Project]:
    """
    Upload a requirements file for a project
    """
    project = await get_project_by_id(db, project_id, user_id)
    if not project:
        return None
    
    # Create upload directory if it doesn't exist
    os.makedirs(f"{settings.UPLOAD_DIR}/{user_id}", exist_ok=True)
    
    # Save file
    file_path = f"{settings.UPLOAD_DIR}/{user_id}/{project_id}_{file.filename}"
    with open(file_path, "wb") as buffer:
        shutil.copyfileobj(file.file, buffer)
    
    # Update project with file path
    project.requirements_file_path = file_path
    await db.commit()
    await db.refresh(project)
    
    return project

async def search_projects(
    db: AsyncSession, user_id: int, search_term: str = "", skip: int = 0, limit: int = 10
) -> Dict[str, Any]:
    """
    Search projects by name, description, or requirements text
    """
    if not search_term or search_term.strip() == "":
        # If no search term, return all projects
        return await get_projects_by_user(db, user_id, skip, limit)
    
    # Create search condition for fulltext search
    search_term = search_term.strip()
    search_condition = or_(
        Project.name.ilike(f"%{search_term}%"),
        Project.description.ilike(f"%{search_term}%"),
        Project.requirements_text.ilike(f"%{search_term}%")
    )
    
    # Get total count of matching projects for pagination
    count_stmt = select(func.count()).select_from(
        select(Project)
        .where(Project.user_id == user_id)
        .where(search_condition)
        .subquery()
    )
    total_count = await db.scalar(count_stmt)
    
    # Get matching projects with pagination
    from sqlalchemy.orm import selectinload
    stmt = (
        select(Project)
        .where(Project.user_id == user_id)
        .where(search_condition)
        .order_by(Project.created_at.desc())  # Order by creation date, newest first
        .offset(skip)
        .limit(limit)
        .options(selectinload(Project.estimates))
    )
    result = await db.execute(stmt)
    projects = result.scalars().all()
    
    # Convert Project objects to dictionaries for JSON serialization
    project_list = []
    for project in projects:
        total_costs = [e.total_cost for e in project.estimates if e.total_cost is not None]
        if total_costs:
            estimate_min = min(total_costs)
            estimate_max = max(total_costs)
        else:
            estimate_min = None
            estimate_max = None
        project_dict = {
            "id": project.id,
            "name": project.name,
            "description": project.description,
            "user_id": project.user_id,
            "software_type": project.software_type,
            "technology_stack": project.technology_stack,
            "requirements_file_path": project.requirements_file_path,
            "created_at": project.created_at,
            "updated_at": project.updated_at,
            "estimate_min": estimate_min,
            "estimate_max": estimate_max
        }
        project_list.append(project_dict)
    
    # Return projects with pagination metadata
    return {
        "items": project_list,
        "total": total_count,
        "page": skip // limit + 1,
        "pages": (total_count + limit - 1) // limit if total_count > 0 else 1,  # Ceiling division, min 1 page
        "size": limit
    }
