from datetime import datetime, timedelta, timezone
from fastapi import HTT<PERSON>Ex<PERSON>, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text
import random
import string
import traceback
import psycopg2
import os
from dotenv import load_dotenv

from .email_service import email_service

# Load environment variables
load_dotenv()

# Get database connection parameters from environment
DATABASE_URL = os.getenv("DATABASE_URL", "postgresql+asyncpg://postgres:postgres@localhost:5432/hapoest")
IS_DOCKER = os.getenv("IS_DOCKER", "false").lower() == "true"

# Parse connection parameters from DATABASE_URL
if "postgres" in DATABASE_URL:
    # Extract connection parameters
    conn_parts = DATABASE_URL.replace("postgresql+asyncpg://", "").split("/")
    conn_auth = conn_parts[0].split("@")
    conn_user_pass = conn_auth[0].split(":")
    conn_host_port = conn_auth[1].split(":")
    
    DB_USER = conn_user_pass[0]
    DB_PASSWORD = conn_user_pass[1]
    DB_HOST = conn_host_port[0]
    DB_PORT = conn_host_port[1] if len(conn_host_port) > 1 else "5432"
    DB_NAME = conn_parts[1]
    
    # Handle Docker case
    if "db:5432" in DATABASE_URL and not IS_DOCKER:
        DB_HOST = "localhost"

# Generate a random verification code
def generate_verification_code(length: int = 6) -> str:
    """Generate a random verification code"""
    return ''.join(random.choices(string.digits, k=length))

# Connect to the database using psycopg2
def get_db_connection():
    """Create a database connection using psycopg2"""
    try:
        conn = psycopg2.connect(
            user=DB_USER,
            password=DB_PASSWORD,
            host=DB_HOST,
            port=DB_PORT,
            database=DB_NAME
        )
        return conn
    except Exception as e:
        print(f"Error connecting to database: {str(e)}")
        traceback.print_exc()
        raise

# Create and save a new verification code for a user
def create_verification_code(user_id: int) -> str:
    """Create a new verification code and save it to the database"""
    # Generate a 6-digit code
    code = generate_verification_code()
    
    # Set expiration time (10 minutes)
    # Use timezone-aware datetime to avoid comparison issues
    expires_at = datetime.now(timezone.utc) + timedelta(minutes=10)
    
    # Save to database using psycopg2
    conn = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Update verification code and expiration time
        cursor.execute(
            "UPDATE users SET verification_code = %s, verification_code_expires_at = %s WHERE id = %s",
            (code, expires_at, user_id)
        )
        conn.commit()
        
        return code
    except Exception as e:
        print(f"Error creating verification code: {str(e)}")
        traceback.print_exc()
        if conn:
            conn.rollback()
        raise
    finally:
        if conn:
            conn.close()

# Verify the code and mark the email as verified
def verify_email_code(email: str, code: str) -> bool:
    conn = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Get user information
        cursor.execute(
            """SELECT id, verification_code, verification_code_expires_at 
               FROM users 
               WHERE email = %s""",
            (email,)
        )
        user_data = cursor.fetchone()
        
        if not user_data:
            raise HTTPException(status_code=404, detail="User not found")
        
        # Verify the code
        user_id, verification_code, expires_at = user_data
        
        # Ensure both datetime objects are timezone-aware
        current_time = datetime.now(timezone.utc)
        if expires_at is not None and expires_at.tzinfo is not None:
            # If expires_at is offset-aware, convert current_time to offset-aware
            current_time = current_time.replace(tzinfo=timezone.utc)
        
        if (verification_code != code or 
            expires_at is None or 
            expires_at < current_time):
            return False
        
        # Mark email as verified
        cursor.execute(
            """UPDATE users 
               SET is_verified = TRUE, 
                   verification_code = NULL, 
                   verification_code_expires_at = NULL 
               WHERE email = %s""",
            (email,)
        )
        conn.commit()
        
        return True
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        print(f"Error verifying email code: {str(e)}")
        traceback.print_exc()
        if conn:
            conn.rollback()
        raise
    finally:
        if conn:
            conn.close()

# Resend verification code
def resend_verification_code(email: str) -> str:
    """Send a new verification code"""
    conn = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Get user information
        cursor.execute(
            """SELECT id, is_verified 
               FROM users 
               WHERE email = %s""",
            (email,)
        )
        user_data = cursor.fetchone()
        
        if not user_data:
            raise HTTPException(status_code=404, detail="User not found")
        
        user_id, is_verified = user_data
        
        # Check if email is already verified
        if is_verified:
            raise HTTPException(status_code=400, detail="Email is already verified")
        
        # Create new verification code
        return create_verification_code(user_id)
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        print(f"Error resending verification code: {str(e)}")
        traceback.print_exc()
        if conn:
            conn.rollback()
        raise
    finally:
        if conn:
            conn.close()

def send_verification_email(email: str, code: str, language: str = "en") -> bool:
    """Send verification email to user using AWS SES"""
    try:
        email_service.send_verification_email(email, code, language)
        return True
    except Exception as e:
        print(f"Error sending verification email: {str(e)}")
        traceback.print_exc()
        return False

def send_password_reset_email(email: str, reset_token: str, language: str = "en") -> bool:
    """Send password reset email to user using AWS SES"""
    try:
        email_service.send_password_reset_email(email, reset_token, language)
        return True
    except Exception as e:
        print(f"Error sending password reset email: {str(e)}")
        traceback.print_exc()
        return False