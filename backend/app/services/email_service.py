import boto3
from botocore.exceptions import Client<PERSON>rror
from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>, status
from ..core.config import settings
import threading
import queue
import time

class EmailService:
    """Service for sending emails using AWS SES with a dedicated worker thread"""
    
    def __init__(self):
        """Initialize the AWS SES client and worker thread"""
        # Initialize AWS SES client
        self.client = boto3.client(
            'ses',
            region_name=settings.AWS_REGION,
            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY
        )
        self.sender = settings.EMAIL_SENDER
        
        # Initialize queue and worker thread
        self.email_queue = queue.Queue()
        self.worker_thread = threading.Thread(target=self._process_email_queue, daemon=True)
        self.worker_thread.start()
        print("Email worker thread started")
    
    def _process_email_queue(self):
        """Worker thread function to process emails from the queue"""
        while True:
            try:
                # Get email task from queue
                task = self.email_queue.get(block=True)
                if task is None:  # Signal to stop thread
                    break
                    
                # Handle different email types
                if len(task) == 3:
                    # Verification email
                    recipient, code, language = task
                    self._send_verification_email_internal(recipient, code, language)
                elif len(task) == 4:
                    # Password reset email
                    email_type, recipient, token, language = task
                    if email_type == "password_reset":
                        self._send_password_reset_email_internal(recipient, token, language)
                
                self.email_queue.task_done()
            except Exception as e:
                print(f"Error in email worker thread: {str(e)}")
                import traceback
                traceback.print_exc()
            time.sleep(0.1)  # Avoid using too much CPU
    
    def _send_verification_email_internal(self, recipient: str, code: str, language: str):
        """Internal method to actually send the verification email"""
        # Email content based on language
        subjects = {
            "en": "Verify Your Email - Hapoest",
            "vi": "Xác minh Email của bạn - Hapoest",
            "ja": "メールアドレスの確認 - Hapoest"
        }
        
        bodies = {
            "en": f"""
                <h2>Email Verification</h2>
                <p>Thank you for registering with Hapoest. Please use the following code to verify your email address:</p>
                <h3 style="background-color: #f0f0f0; padding: 10px; text-align: center; font-size: 24px; letter-spacing: 5px;">{code}</h3>
                <p>This code will expire in 10 minutes.</p>
                <p>If you did not request this verification, please ignore this email.</p>
                <p>Best regards,<br>The Hapoest Team</p>
            """,
            "vi": f"""
                <h2>Xác minh Email</h2>
                <p>Cảm ơn bạn đã đăng ký với Hapoest. Vui lòng sử dụng mã sau để xác minh địa chỉ email của bạn:</p>
                <h3 style="background-color: #f0f0f0; padding: 10px; text-align: center; font-size: 24px; letter-spacing: 5px;">{code}</h3>
                <p>Mã này sẽ hết hạn trong 10 phút.</p>
                <p>Nếu bạn không yêu cầu xác minh này, vui lòng bỏ qua email này.</p>
                <p>Trân trọng,<br>Đội ngũ Hapoest</p>
            """,
            "ja": f"""
                <h2>メール確認</h2>
                <p>Hapoestにご登録いただきありがとうございます。以下のコードを使用してメールアドレスを確認してください：</p>
                <h3 style="background-color: #f0f0f0; padding: 10px; text-align: center; font-size: 24px; letter-spacing: 5px;">{code}</h3>
                <p>このコードは10分後に期限切れになります。</p>
                <p>この確認をリクエストしていない場合は、このメールを無視してください。</p>
                <p>敬具、<br>Hapoestチーム</p>
            """
        }
        
        # Default to English if language not supported
        subject = subjects.get(language, subjects["en"])
        body_html = bodies.get(language, bodies["en"])
        
        try:
            response = self.client.send_email(
                Source=self.sender,
                Destination={'ToAddresses': [recipient]},
                Message={
                    'Subject': {'Data': subject},
                    'Body': {
                        'Html': {'Data': body_html}
                    }
                }
            )
            print(f"Verification email sent successfully to {recipient}")
            return response
        except ClientError as e:
            print(f"Error sending verification email: {e.response['Error']['Message']}")
            return None
    
    def _send_password_reset_email_internal(self, recipient: str, reset_token: str, language: str):
        """Internal method to actually send the password reset email"""
        # Generate reset URL with proper locale
        frontend_url = settings.FRONTEND_URL  # Use the configurable frontend URL
        reset_url = f"{frontend_url}/{language}/reset-password?token={reset_token}"
        
        # Email content based on language
        subjects = {
            "en": "Password Reset Request - Hapoest",
            "vi": "Yêu cầu đặt lại mật khẩu - Hapoest",
            "ja": "パスワードリセットのご依頼 - Hapoest"
        }
        
        bodies = {
            "en": f"""
                <h2>Password Reset Request</h2>
                <p>We received a request to reset your password for your Hapoest account.</p>
                <p>Click the button below to reset your password:</p>
                <div style="text-align: center; margin: 20px 0;">
                    <a href="{reset_url}" style="background-color: #3B82F6; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">Reset Password</a>
                </div>
                <p>Or copy and paste this link into your browser:</p>
                <p style="word-break: break-all; background-color: #f5f5f5; padding: 10px; border-radius: 4px;">{reset_url}</p>
                <p>This link will expire in 1 hour for security reasons.</p>
                <p>If you did not request a password reset, please ignore this email. Your password will remain unchanged.</p>
                <p>Best regards,<br>The Hapoest Team</p>
            """,
            "vi": f"""
                <h2>Yêu cầu đặt lại mật khẩu</h2>
                <p>Chúng tôi đã nhận được yêu cầu đặt lại mật khẩu cho tài khoản Hapoest của bạn.</p>
                <p>Nhấp vào nút bên dưới để đặt lại mật khẩu:</p>
                <div style="text-align: center; margin: 20px 0;">
                    <a href="{reset_url}" style="background-color: #3B82F6; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">Đặt lại mật khẩu</a>
                </div>
                <p>Hoặc sao chép và dán liên kết này vào trình duyệt:</p>
                <p style="word-break: break-all; background-color: #f5f5f5; padding: 10px; border-radius: 4px;">{reset_url}</p>
                <p>Liên kết này sẽ hết hạn trong 1 giờ vì lý do bảo mật.</p>
                <p>Nếu bạn không yêu cầu đặt lại mật khẩu, vui lòng bỏ qua email này. Mật khẩu của bạn sẽ không thay đổi.</p>
                <p>Trân trọng,<br>Đội ngũ Hapoest</p>
            """,
            "ja": f"""
                <h2>パスワードリセットのご依頼</h2>
                <p>Hapoestアカウントのパスワードリセットのご依頼を受け付けました。</p>
                <p>以下のボタンをクリックしてパスワードをリセットしてください：</p>
                <div style="text-align: center; margin: 20px 0;">
                    <a href="{reset_url}" style="background-color: #3B82F6; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">パスワードをリセット</a>
                </div>
                <p>または、このリンクをブラウザにコピー・ペーストしてください：</p>
                <p style="word-break: break-all; background-color: #f5f5f5; padding: 10px; border-radius: 4px;">{reset_url}</p>
                <p>このリンクはセキュリティ上の理由により1時間で無効になります。</p>
                <p>パスワードリセットをご依頼でない場合は、このメールを無視してください。パスワードは変更されません。</p>
                <p>敬具、<br>Hapoestチーム</p>
            """
        }
        
        # Default to English if language not supported
        subject = subjects.get(language, subjects["en"])
        body_html = bodies.get(language, bodies["en"])
        
        try:
            response = self.client.send_email(
                Source=self.sender,
                Destination={'ToAddresses': [recipient]},
                Message={
                    'Subject': {'Data': subject},
                    'Body': {
                        'Html': {'Data': body_html}
                    }
                }
            )
            print(f"Password reset email sent successfully to {recipient}")
            return response
        except ClientError as e:
            print(f"Error sending password reset email: {e.response['Error']['Message']}")
            return None

    def _send_email_internal(self, recipient: str, code: str, language: str):
        """Legacy method - redirect to verification email"""
        return self._send_verification_email_internal(recipient, code, language)
            
    def send_verification_email(self, recipient: str, code: str, language: str = "en"):
        """
        Queue a verification email to be sent by the worker thread
        
        Args:
            recipient: Email address of the recipient
            code: Verification code
            language: Language for email content (en, vi, ja)
        """
        try:
            # Put task into queue to be processed by worker thread
            self.email_queue.put((recipient, code, language))
            print(f"Verification email to {recipient} queued for sending")
            return True
        except Exception as e:
            print(f"Error queuing verification email: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

    def send_password_reset_email(self, recipient: str, reset_token: str, language: str = "en"):
        """
        Queue a password reset email to be sent by the worker thread
        
        Args:
            recipient: Email address of the recipient
            reset_token: Password reset token
            language: Language for email content (en, vi, ja)
        """
        try:
            # Put task into queue to be processed by worker thread with proper format
            self.email_queue.put(("password_reset", recipient, reset_token, language))
            print(f"Password reset email to {recipient} queued for sending")
            return True
        except Exception as e:
            print(f"Error queuing password reset email: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

# Create a singleton instance
email_service = EmailService()
print("Email service initialized with worker thread")
