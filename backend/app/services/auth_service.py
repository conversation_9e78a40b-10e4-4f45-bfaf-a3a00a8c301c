from datetime import datetime, timedelta
from jose import jwt
from passlib.context import <PERSON>pt<PERSON>ontext
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, text
from typing import Optional
from fastapi import HTTPException, status

from ..core.config import settings
from ..models.user import User
from ..schemas.token import TokenPayload

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def verify_password(plain_password: str, hashed_password: str) -> bool:
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    return pwd_context.hash(password)

def create_access_token(*, user_id: int, expires_delta: Optional[timedelta] = None) -> str:
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    
    to_encode = {"exp": expire, "sub": str(user_id)}
    encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
    return encoded_jwt

async def authenticate_user(db: AsyncSession, email: str, password: str) -> Optional[User]:
    try:
        # Use raw SQL to only select existing columns
        result = await db.execute(
            text("""SELECT id, email, hashed_password, is_active, created_at, google_id, 
                is_verified, verification_code, verification_code_expires_at, updated_at
                FROM users 
                WHERE email = :email AND is_active = TRUE"""),
            {"email": email}
        )
        user_data = result.mappings().first()
        
        if not user_data:
            return None
        
        # Create User object with only existing columns
        user = User(
            id=user_data["id"],
            email=user_data["email"],
            hashed_password=user_data["hashed_password"],
            is_active=user_data["is_active"],
            created_at=user_data["created_at"],
            google_id=user_data.get("google_id"),
            is_verified=user_data.get("is_verified", False),
            verification_code=user_data.get("verification_code"),
            verification_code_expires_at=user_data.get("verification_code_expires_at"),
            updated_at=user_data.get("updated_at")
        )
        
        if not user.hashed_password:
            return None
            
        if not verify_password(password, user.hashed_password):
            return None
        
        return user
    except Exception as e:
        print(f"Authentication error: {e}")
        import traceback
        traceback.print_exc()
        try:
            await db.rollback()
        except Exception:
            pass
        return None

async def get_or_create_google_user(db: AsyncSession, email: str, google_id: str, name: str = None, picture: str = None) -> User:
    try:
        # Only select columns that exist in the database
        result = await db.execute(
            text("""SELECT id, email, hashed_password, is_active, created_at, google_id,
                is_verified, verification_code, verification_code_expires_at, updated_at
                FROM users 
                WHERE email = :email OR google_id = :google_id"""),
            {"email": email, "google_id": google_id}
        )
        user_data = result.mappings().first()
        
        if user_data:
            # Create User object with only existing columns
            user = User(
                id=user_data["id"],
                email=user_data["email"],
                hashed_password=user_data["hashed_password"],
                is_active=user_data["is_active"],
                created_at=user_data["created_at"],
                google_id=user_data.get("google_id"),
                is_verified=user_data.get("is_verified", False),
                verification_code=user_data.get("verification_code"),
                verification_code_expires_at=user_data.get("verification_code_expires_at"),
                updated_at=user_data.get("updated_at")
            )
            
            if not user.google_id:
                try:
                    await db.execute(
                        text("UPDATE users SET google_id = :google_id WHERE id = :user_id"),
                        {"google_id": google_id, "user_id": user.id}
                    )
                    await db.commit()
                    user.google_id = google_id
                except Exception as update_error:
                    print(f"Error updating google_id: {update_error}")
                    await db.rollback()
            
            return user
        
        # Create new user if not exists, with optional fields
        try:
            # Use raw SQL to insert directly
            current_time = datetime.utcnow()
            
            # Check if the users table has name and picture columns
            try:
                # Try to query table schema information
                columns_result = await db.execute(
                    text("""SELECT column_name FROM information_schema.columns 
                        WHERE table_name = 'users' AND column_name IN ('name', 'picture')""")
                )
                columns = [row[0] for row in columns_result]
                
                has_name_column = 'name' in columns
                has_picture_column = 'picture' in columns
                
                # Build SQL statement based on existing columns
                if has_name_column and has_picture_column and name and picture:
                    result = await db.execute(
                        text("""INSERT INTO users (email, google_id, is_active, is_verified, created_at, updated_at, name, picture) 
                            VALUES (:email, :google_id, TRUE, TRUE, :created_at, :updated_at, :name, :picture) RETURNING id"""),
                        {"email": email, "google_id": google_id, "created_at": current_time, "updated_at": current_time, 
                         "name": name, "picture": picture}
                    )
                elif has_name_column and name:
                    result = await db.execute(
                        text("""INSERT INTO users (email, google_id, is_active, is_verified, created_at, updated_at, name) 
                            VALUES (:email, :google_id, TRUE, TRUE, :created_at, :updated_at, :name) RETURNING id"""),
                        {"email": email, "google_id": google_id, "created_at": current_time, "updated_at": current_time, "name": name}
                    )
                else:
                    # Default if no additional columns
                    result = await db.execute(
                        text("""INSERT INTO users (email, google_id, is_active, is_verified, created_at, updated_at) 
                            VALUES (:email, :google_id, TRUE, TRUE, :created_at, :updated_at) RETURNING id"""),
                        {"email": email, "google_id": google_id, "created_at": current_time, "updated_at": current_time}
                    )
            except Exception as schema_error:
                print(f"Error checking table schema: {schema_error}")
                # Default if cannot check schema
                result = await db.execute(
                    text("""INSERT INTO users (email, google_id, is_active, is_verified, created_at, updated_at) 
                        VALUES (:email, :google_id, TRUE, TRUE, :created_at, :updated_at) RETURNING id"""),
                    {"email": email, "google_id": google_id, "created_at": current_time, "updated_at": current_time}
                )
            user_id = result.scalar_one()
            await db.commit()
            
            # Get newly created user information
            result = await db.execute(
                text("""SELECT id, email, is_active, created_at, google_id, is_verified, updated_at 
                    FROM users WHERE id = :id"""),
                {"id": user_id}
            )
            new_user_data = result.mappings().first()
            
            new_user = User(
                id=new_user_data["id"],
                email=new_user_data["email"],
                is_active=new_user_data["is_active"],
                created_at=new_user_data["created_at"],
                google_id=new_user_data["google_id"],
                is_verified=new_user_data.get("is_verified", True),
                updated_at=new_user_data.get("updated_at")
            )
            return new_user
        except Exception as insert_error:
            print(f"Error creating new user: {insert_error}")
            await db.rollback()
            raise
    except Exception as e:
        print(f"Google authentication error: {e}")
        import traceback
        traceback.print_exc()
        try:
            await db.rollback()
        except Exception:
            pass
        # Handle error gracefully
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Authentication error occurred"
        ) 