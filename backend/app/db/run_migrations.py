import os
import sys
from alembic import command
from alembic.config import Config

# L<PERSON>y đường dẫn tuyệt đối đến thư mục gốc của dự án
ROOT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Thê<PERSON> thư mục gốc vào sys.path để import được các module
sys.path.append(ROOT_DIR)

# Đường dẫn đến file alembic.ini
alembic_ini = os.path.join(ROOT_DIR, "backend", "alembic.ini")

def run_migrations():
    """Chạy migration để cập nhật database."""
    alembic_config = Config(alembic_ini)
    command.upgrade(alembic_config, "head")

if __name__ == "__main__":
    run_migrations()
    print("Migration completed successfully.") 