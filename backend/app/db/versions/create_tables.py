"""create tables

Revision ID: <migration_id>
Revises: 
Create Date: 2023-01-01

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import JSON

# revision identifiers, used by Alembic.
revision = '<migration_id>'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # User table
    op.create_table(
        'users',
        sa.Column('id', sa.Integer(), primary_key=True, index=True),
        sa.Column('email', sa.String(), unique=True, index=True),
        sa.Column('hashed_password', sa.String()),
        sa.Column('is_active', sa.<PERSON>(), default=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(timezone=True), onupdate=sa.func.now()),
        sa.Column('google_id', sa.String(), unique=True, nullable=True),
        # Thêm các cột mới cho xác minh email
        sa.Column('is_verified', sa.<PERSON>(), default=False),
        sa.Column('verification_code', sa.String(), nullable=True),
        sa.Column('verification_code_expires_at', sa.DateTime(timezone=True), nullable=True),
    )
    
    # User Settings table
    op.create_table(
        'user_settings',
        sa.Column('id', sa.Integer(), primary_key=True, index=True),
        sa.Column('user_id', sa.Integer(), sa.ForeignKey('users.id')),
        sa.Column('currency', sa.String(), default='USD'),
        sa.Column('role_rates', JSON, default={}),
    )
    
    # Projects table
    op.create_table(
        'projects',
        sa.Column('id', sa.Integer(), primary_key=True, index=True),
        sa.Column('name', sa.String()),
        sa.Column('description', sa.Text()),
        sa.Column('software_type', sa.String()),
        sa.Column('technology_stack', sa.String()),
        sa.Column('requirements', sa.Text()),
        sa.Column('user_id', sa.Integer(), sa.ForeignKey('users.id')),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(timezone=True), onupdate=sa.func.now()),
        sa.Column('estimation_results', JSON, nullable=True),
    )


def downgrade():
    op.drop_table('projects')
    op.drop_table('user_settings')
    op.drop_table('users') 