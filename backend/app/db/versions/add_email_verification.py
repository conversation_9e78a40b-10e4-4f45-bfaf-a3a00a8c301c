"""add email verification columns

Revision ID: add_email_verification
Revises: create_tables
Create Date: 2023-07-15

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = 'add_email_verification'
down_revision = 'create_tables'  # Thay đổi thành ID của migration trước đó
branch_labels = None
depends_on = None


def upgrade():
    # Thêm các cột mới cho xác minh email
    op.add_column('users', sa.Column('is_verified', sa.<PERSON>(), server_default='false'))
    op.add_column('users', sa.Column('verification_code', sa.String(), nullable=True))
    op.add_column('users', sa.Column('verification_code_expires_at', sa.DateTime(timezone=True), nullable=True))


def downgrade():
    # X<PERSON>a các cột đã thêm
    op.drop_column('users', 'verification_code_expires_at')
    op.drop_column('users', 'verification_code')
    op.drop_column('users', 'is_verified') 