from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.declarative import declarative_base
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Get DATABASE_URL from environment variable
DATABASE_URL = os.getenv("DATABASE_URL", "postgresql+asyncpg://postgres:postgres@localhost:5432/hapoest")
IS_DOCKER = os.getenv("IS_DOCKER", "false").lower() == "true"
# Ensure we're always using localhost in local development
if "db:5432" in DATABASE_URL and not IS_DOCKER:
    DATABASE_URL = DATABASE_URL.replace("db:5432", "localhost:5432")

print(f"Using DATABASE_URL: {DATABASE_URL}")

engine = create_async_engine(DATABASE_URL)
AsyncSessionLocal = sessionmaker(
    autocommit=False,
    autoflush=False,
    bind=engine,
    class_=AsyncSession,
)

Base = declarative_base()

async def get_db():
    db = AsyncSessionLocal()
    try:
        yield db
    finally:
        await db.close() 
