import asyncio
import datetime
import json
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession

from ...db.session import engine, get_db
from ...models.estimate import Estimate

async def migrate_estimates():
    """Migrate estimation data from projects table to estimates table"""
    print("Starting migration of estimates from projects to estimates table...")
    
    async with engine.begin() as conn:
        # Check if estimates table exists, create it if not
        await conn.execute(text("""
        CREATE TABLE IF NOT EXISTS estimates (
            id SERIAL PRIMARY KEY,
            name VARCHAR NOT NULL,
            project_id INTEGER NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
            development_items JSONB,
            summary_items JSONB,
            grand_total FLOAT,
            total_cost FLOAT,
            day_rate FLOAT,
            currency VARCHAR DEFAULT 'USD',
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE
        )
        """))
        
        # Check if estimation_results column exists in projects table
        check_column = await conn.execute(text("""
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name='projects' AND column_name='estimation_results'
        """))
        
        column_exists = check_column.scalar() is not None
        
        if not column_exists:
            print("Column estimation_results does not exist in projects table. Migration not needed.")
            return
        
        # Get all projects with estimation_results
        result = await conn.execute(text("""
        SELECT id, name, estimation_results 
        FROM projects 
        WHERE estimation_results IS NOT NULL
        """))
        
        projects_with_estimates = result.mappings().all()
        
        if not projects_with_estimates:
            print("No projects with estimation_results found. Nothing to migrate.")
        else:
            print(f"Found {len(projects_with_estimates)} projects with estimation data to migrate.")
            
            # Insert estimates into the new table
            for project in projects_with_estimates:
                estimate_name = f"Migrated Estimate {datetime.datetime.now().strftime('%Y-%m-%d %H:%M')}"
                
                # Extract data from estimation_results JSON
                estimation_results = project['estimation_results']
                
                # Skip if estimation_results is None
                if estimation_results is None:
                    print(f"Skipping project {project['id']} - {project['name']} because estimation_results is None")
                    continue
                
                development_items = estimation_results.get('development_items')
                summary_items = estimation_results.get('summary_items')
                grand_total = estimation_results.get('grand_total')
                total_cost = estimation_results.get('total_cost')
                day_rate = estimation_results.get('day_rate')
                currency = estimation_results.get('currency', 'USD')
                
                # Convert Python objects to JSON strings for PostgreSQL
                development_items_json = json.dumps(development_items) if development_items else None
                summary_items_json = json.dumps(summary_items) if summary_items else None
                
                # Insert into estimates table
                await conn.execute(text("""
                INSERT INTO estimates 
                (name, project_id, development_items, summary_items, grand_total, total_cost, day_rate, currency, created_at) 
                VALUES (:name, :project_id, :development_items, :summary_items, :grand_total, :total_cost, :day_rate, :currency, NOW())
                """), {
                    "name": estimate_name,
                    "project_id": project['id'],
                    "development_items": development_items_json,
                    "summary_items": summary_items_json,
                    "grand_total": grand_total,
                    "total_cost": total_cost,
                    "day_rate": day_rate,
                    "currency": currency
                })
                
                print(f"Migrated estimate for project {project['id']} - {project['name']}")
            
            print("Migration of estimate data completed successfully.")
        
        # Drop the estimation_results column from projects table
        await conn.execute(text("""
        ALTER TABLE projects DROP COLUMN IF EXISTS estimation_results
        """))
        
        print("Removed estimation_results column from projects table.")

if __name__ == "__main__":
    asyncio.run(migrate_estimates())
