from pydantic import BaseModel, <PERSON>
from typing import Optional, Dict, Any, List
from datetime import datetime

class ProjectBase(BaseModel):
    name: str
    description: Optional[str] = None
    software_type: Optional[str] = None
    technology_stack: Optional[str] = None

class ProjectCreate(ProjectBase):
    pass

class ProjectUpdate(ProjectBase):
    name: Optional[str] = None
    requirements_text: Optional[str] = None

class Project(ProjectBase):
    id: int
    user_id: int
    requirements_text: Optional[str] = None
    requirements_file_path: Optional[str] = None
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True