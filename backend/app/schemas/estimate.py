from pydantic import BaseModel, Field
from typing import Optional, Dict, Any, List
from datetime import datetime

class DevelopmentItem(BaseModel):
    feature: str
    description: str
    backend_effort: float
    frontend_effort: float
    unit_test_effort: float
    priority: str
    is_selected: bool = True

class SummaryItem(BaseModel):
    category: str
    effort: float
    description: str
    role: Optional[str] = None
    day_rate: Optional[float] = None
    cost: Optional[float] = None
    is_selected: bool = True

class EstimateBase(BaseModel):
    name: str
    project_id: int
    development_items: Optional[List[Dict[str, Any]]] = None
    summary_items: Optional[List[Dict[str, Any]]] = None
    grand_total: Optional[float] = None
    total_cost: Optional[float] = None
    day_rate: Optional[float] = None
    currency: Optional[str] = "USD"

class EstimateCreate(EstimateBase):
    pass

class EstimateUpdate(BaseModel):
    name: Optional[str] = None
    development_items: Optional[List[Dict[str, Any]]] = None
    summary_items: Optional[List[Dict[str, Any]]] = None
    grand_total: Optional[float] = None
    total_cost: Optional[float] = None
    day_rate: Optional[float] = None
    currency: Optional[str] = None
    is_active: Optional[bool] = None

class Estimate(EstimateBase):
    id: int
    is_active: bool
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True

class EstimatePreviewRequest(BaseModel):
    locale: Optional[str] = None