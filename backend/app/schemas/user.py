from pydantic import BaseModel, EmailStr, Field, validator, root_validator
from typing import Optional, Dict, Literal
from datetime import datetime
from enum import Enum
import re

class UserBase(BaseModel):
    email: str

class UserCreate(UserBase):
    password: str = Field(
        ..., 
        min_length=8,
        description="Password must be at least 8 characters long, include uppercase and lowercase letters, a number, and a special character"
    )
    password_confirm: str
    language: Optional[str] = "en"

    @root_validator(pre=True)
    def validate_all(cls, values):
        language = values.get('language') or 'en'
        email = values.get('email', '')
        email_regex = r'^[\w\.\+\-]+@[\w\.\-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_regex, email):
            if language == 'vi':
                raise ValueError('Email không hợp lệ. Vui lòng kiểm tra lại')
            elif language == 'ja':
                raise ValueError('メールアドレスが無効です。もう一度ご確認ください。')
            else:
                raise ValueError('Invalid email address. Please check again.')
        return values

    @validator('password')
    def password_strength(cls, v, values, **kwargs):
        language = values.get('language', 'en')
        if len(v) < 8:
            if language == 'vi':
                raise ValueError('Mật khẩu phải có ít nhất 8 ký tự')
            elif language == 'ja':
                raise ValueError('パスワードは8文字以上で入力してください')
            else:
                raise ValueError('Password must be at least 8 characters long')
        if not re.search(r'[A-Z]', v):
            if language == 'vi':
                raise ValueError('Mật khẩu phải có ít nhất 1 chữ hoa')
            elif language == 'ja':
                raise ValueError('パスワードには大文字を含めてください')
            else:
                raise ValueError('Password must contain at least one uppercase letter')
        if not re.search(r'[a-z]', v):
            if language == 'vi':
                raise ValueError('Mật khẩu phải có ít nhất 1 chữ thường')
            elif language == 'ja':
                raise ValueError('パスワードには小文字を含めてください')
            else:
                raise ValueError('Password must contain at least one lowercase letter')
        if not re.search(r'\d', v):
            if language == 'vi':
                raise ValueError('Mật khẩu phải có ít nhất 1 số')
            elif language == 'ja':
                raise ValueError('パスワードには数字を含めてください')
            else:
                raise ValueError('Password must contain at least one number')
        if not re.search(r'[!@#$%^&*(),.?":{}|<>]', v):
            if language == 'vi':
                raise ValueError('Mật khẩu phải có ít nhất 1 ký tự đặc biệt')
            elif language == 'ja':
                raise ValueError('パスワードには記号を含めてください')
            else:
                raise ValueError('Password must contain at least one special character')
        return v

    @validator('password_confirm')
    def passwords_match(cls, v, values, **kwargs):
        language = values.get('language', 'en')
        if 'password' in values and v != values['password']:
            if language == 'vi':
                raise ValueError('Mật khẩu xác nhận không khớp')
            elif language == 'ja':
                raise ValueError('パスワード確認が一致しません')
            else:
                raise ValueError('Passwords do not match')
        return v

class UserLogin(UserBase):
    password: str

class UserGoogleAuth(UserBase):
    google_id: Optional[str] = None
    token: Optional[str] = None
    name: Optional[str] = None
    picture: Optional[str] = None

class User(UserBase):
    id: int
    is_active: bool
    is_verified: bool = False
    created_at: datetime
    
    class Config:
        from_attributes = True

class PasswordChange(BaseModel):
    current_password: str
    new_password: str = Field(
        ..., 
        min_length=8,
        description="Password must be at least 8 characters long, include uppercase and lowercase letters, a number, and a special character"
    )
    confirm_password: str = Field(..., min_length=8)
    
    @validator('new_password')
    def password_strength(cls, v):
        # Check if password has at least 8 characters
        if len(v) < 8:
            raise ValueError("Password must be at least 8 characters long")
        
        # Check if password has at least 1 uppercase letter
        if not re.search(r'[A-Z]', v):
            raise ValueError("Password must contain at least one uppercase letter")
        
        # Check if password has at least 1 lowercase letter
        if not re.search(r'[a-z]', v):
            raise ValueError("Password must contain at least one lowercase letter")
        
        # Check if password has at least 1 number
        if not re.search(r'\d', v):
            raise ValueError("Password must contain at least one number")
        
        # Check if password has at least 1 special character
        if not re.search(r'[!@#$%^&*(),.?":{}|<>]', v):
            raise ValueError("Password must contain at least one special character")
        
        return v
    
    @validator('confirm_password')
    def passwords_match(cls, v, values):
        if 'new_password' in values and v != values['new_password']:
            raise ValueError("Passwords do not match")
        return v

class EmailVerification(BaseModel):
    email: EmailStr
    code: str

class ResendVerification(BaseModel):
    email: EmailStr

class UserRoleRate(BaseModel):
    role: str
    rate: float

class CurrencyType(str, Enum):
    USD = "USD"
    VND = "VND"
    JPY = "JPY"

class UserSettings(BaseModel):
    id: Optional[int] = None
    user_id: int
    currency: str = "USD"
    role_rates: Dict[str, float] = {}

    class Config:
        from_attributes = True

class UserSettingsUpdate(BaseModel):
    currency: Optional[CurrencyType] = None
    role_rates: Optional[Dict[str, float]] = None

class ResetPasswordRequest(BaseModel):
    email: EmailStr
    locale: Optional[str] = "en"

class ResetPassword(BaseModel):
    token: str
    new_password: str = Field(
        ..., 
        min_length=8,
        description="Password must be at least 8 characters long, include uppercase and lowercase letters, a number, and a special character"
    )
    
    @validator('new_password')
    def password_strength(cls, v):
        # Check if password has at least 8 characters
        if len(v) < 8:
            raise ValueError("Password must be at least 8 characters long")
        
        # Check if password has at least 1 uppercase letter
        if not re.search(r'[A-Z]', v):
            raise ValueError("Password must contain at least one uppercase letter")
        
        # Check if password has at least 1 lowercase letter
        if not re.search(r'[a-z]', v):
            raise ValueError("Password must contain at least one lowercase letter")
        
        # Check if password has at least 1 number
        if not re.search(r'\d', v):
            raise ValueError("Password must contain at least one number")
        
        # Check if password has at least 1 special character
        if not re.search(r'[!@#$%^&*(),.?":{}|<>]', v):
            raise ValueError("Password must contain at least one special character")
        
        return v 