import os
from fastapi import <PERSON><PERSON><PERSON>, Depends, Request, status
from fastapi.middleware.cors import CORSMiddleware
from sqlalchemy.ext.asyncio import Async<PERSON><PERSON><PERSON>, create_async_engine
import uvicorn
import sys
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse

# Monkey patch for bcrypt and passlib compatibility issue
try:
    import bcrypt
    if not hasattr(bcrypt, '__about__'):
        import types
        bcrypt.__about__ = types.ModuleType('__about__')
        bcrypt.__about__.__version__ = bcrypt.__version__
        print("Applied bcrypt monkey patch")
except ImportError:
    print("bcrypt not installed, skipping monkey patch")

from .core.config import settings
from .db.session import Base, engine
from .routers import auth_routes, project_routes, user_routes, estimate_routes

# Create uploads directory
os.makedirs(settings.UPLOAD_DIR, exist_ok=True)

app = FastAPI(
    title=settings.PROJECT_NAME,
    openapi_url=f"{settings.API_V1_STR}/openapi.json"
)

# Set all CORS enabled origins
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # TODO: Change to specific origins in production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(auth_routes.router, prefix=settings.API_V1_STR)
app.include_router(project_routes.router, prefix=settings.API_V1_STR)
app.include_router(user_routes.router, prefix=settings.API_V1_STR)
app.include_router(estimate_routes.router, prefix=settings.API_V1_STR)

# Startup/shutdown events
@app.on_event("startup")
async def startup():
    # Create tables
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)

@app.on_event("shutdown")
async def shutdown():
    # Close engine
    await engine.dispose()

@app.get("/")
async def root():
    return {"message": "Welcome to the Project Estimation API"}

if __name__ == "__main__":
    uvicorn.run("app.main:app", host="0.0.0.0", port=8000, reload=True)

@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    def get_clean_message(error):
        error_type = error.get("type", "")
        error_msg = error.get("msg", "Invalid input")

        # If it is an error from `raise ValueError()`, get the original message
        if error_type.startswith("value_error") and "ctx" in error and "error" in error["ctx"]:
            return str(error["ctx"]["error"])

        # If it is a built-in error (regex, min_length, ...), return a custom message
        if error_type == "string_too_short":
            return "Input is too short"
        if error_type == "string_pattern_mismatch":
            return "Invalid format"

        # If not a special error, remove any prefix (after `:` or `,`)
        if ":" in error_msg:
            return error_msg.split(":")[-1].strip()
        if "," in error_msg:
            return error_msg.split(",")[-1].strip()
        return error_msg

    messages = [get_clean_message(err) for err in exc.errors()]
    if not messages:
        messages = ["Invalid input"]

    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content={"messages": messages}
    )
