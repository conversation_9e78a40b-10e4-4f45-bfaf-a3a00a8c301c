import asyncio
import sys
from app.db.session import AsyncSessionLocal, Base, engine
from app.models.user import User
from app.models.project import Project
from app.services.auth_service import get_password_hash

async def seed_data():
    """Seed database with initial data"""
    print("Creating tables...")
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)

    async with AsyncSessionLocal() as db:
        # Check if data already exists
        result = await db.execute(User.__table__.select().limit(1))
        if result.scalar_one_or_none():
            print("Data already seeded. Skipping.")
            return

        print("Seeding users...")
        # Create test user
        test_user = User(
            email="<EMAIL>",
            hashed_password=get_password_hash("password123"),
            is_active=True
        )
        db.add(test_user)
        await db.commit()
        await db.refresh(test_user)
        
        print("Seeding projects...")
        # Create sample projects
        projects = [
            Project(
                name="Web Application",
                description="A responsive web application with user authentication and dashboard",
                user_id=test_user.id,
                software_type="Web App",
                technology_stack="React, Node.js, PostgreSQL",
                requirements_text="Build a responsive web application with authentication, user dashboard, and data visualization. Features include user registration, login, profile management, interactive charts, and admin panel."
            ),
            Project(
                name="Mobile App",
                description="Cross-platform mobile application for task management",
                user_id=test_user.id,
                software_type="Mobile App",
                technology_stack="React Native, Firebase",
                requirements_text="Develop a cross-platform mobile application for task management. Features should include user authentication, task creation/editing, notifications, offline functionality, and cloud synchronization."
            ),
            Project(
                name="E-commerce Platform",
                description="Online shopping platform with payment integration",
                user_id=test_user.id,
                software_type="Web App",
                technology_stack="Next.js, Django, MongoDB",
                requirements_text="Create an e-commerce platform with product catalog, shopping cart, user accounts, payment processing, order management, and admin dashboard for inventory management."
            )
        ]
        
        for project in projects:
            db.add(project)
        
        await db.commit()
        
        print("Data seeded successfully!")
        print("\nLogin credentials:")
        print("Email: <EMAIL>")
        print("Password: password123")

if __name__ == "__main__":
    asyncio.run(seed_data()) 