# Configuration Guide for HapoEst Backend

## Environment Variables Configuration

### Development Environment

For local development, use these default values:

```env
# Frontend & API URLs
NEXT_PUBLIC_API_URL=http://localhost:8000/api/v1
FRONTEND_URL=http://localhost:3000
```

### Production Environment

For production deployment, update these URLs to match your domain:

```env
# Frontend & API URLs
NEXT_PUBLIC_API_URL=https://api.yourdomain.com/api/v1
FRONTEND_URL=https://yourdomain.com
```

### Email Configuration Impact

The `FRONTEND_URL` environment variable is used to generate password reset links in emails sent via AWS SES.

**Email Template Example:**
- Development: `http://localhost:3000/en/reset-password?token=abc123`
- Production: `https://yourdomain.com/en/reset-password?token=abc123`

### Complete Environment Variables

Create a `.env` file in the backend directory with the following variables:

```env
# Database Configuration
DATABASE_URL=postgresql+asyncpg://postgres:postgres@localhost:5432/hapoest
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
POSTGRES_DB=hapoest

# JWT Authentication
SECRET_KEY=your-secret-key-here
ALGORITHM=HS256

# Google OAuth (Optional)
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# Google AI API Key
GOOGLE_API_KEY=your-google-ai-api-key

# AWS Configuration for SES Email
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=ap-northeast-1

# Email Configuration
EMAIL_SENDER=<EMAIL>

# Frontend & API URLs
NEXT_PUBLIC_API_URL=https://api.yourdomain.com/api/v1
FRONTEND_URL=https://yourdomain.com

# Docker Environment
IS_DOCKER=false
```

### Production Deployment Checklist

1. **Update Environment Variables:**
   - [ ] Set `FRONTEND_URL` to your production domain
   - [ ] Set `NEXT_PUBLIC_API_URL` to your API domain
   - [ ] Configure production database URL
   - [ ] Set secure `SECRET_KEY`

2. **AWS SES Configuration:**
   - [ ] Verify your sender email in AWS SES
   - [ ] Configure AWS credentials with SES permissions
   - [ ] Test email sending in production

3. **Domain Configuration:**
   - [ ] Ensure CORS is properly configured for your domain
   - [ ] SSL certificates are installed
   - [ ] DNS records point to correct servers

### Testing Email Configuration

To test if email configuration is working correctly:

```bash
# Test password reset request
curl -X POST "https://api.yourdomain.com/api/v1/auth/reset-password-request" \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>"}'
```

Check the email logs for successful sending and verify the reset link uses the correct domain. 