import pytest
import uuid
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from app.models.user import User, UserSettings

pytestmark = pytest.mark.asyncio

async def test_create_user(db_session: AsyncSession):
    """Test tạo user"""
    # Sử dụng UUID để đảm bảo email luôn khác nhau
    unique_email = f"model_test_{uuid.uuid4()}@example.com"
    
    user = User(
        email=unique_email,
        hashed_password="hashed_password",
        is_active=True
    )
    
    db_session.add(user)
    await db_session.commit()
    await db_session.refresh(user)
    
    assert user.id is not None
    assert user.email == unique_email
    assert user.hashed_password == "hashed_password"
    assert user.is_active is True
    
    # Kiểm tra không có settings bằng cách query trực tiếp
    stmt = select(UserSettings).where(UserSettings.user_id == user.id)
    result = await db_session.execute(stmt)
    settings = result.scalar_one_or_none()
    assert settings is None

async def test_create_user_settings(db_session: AsyncSession):
    """Test tạo user settings"""
    # Tạo user trước với email unique
    unique_email = f"settings_test_{uuid.uuid4()}@example.com"
    
    user = User(
        email=unique_email,
        hashed_password="hashed_password",
        is_active=True
    )
    
    db_session.add(user)
    await db_session.commit()
    await db_session.refresh(user)
    
    # Tạo settings cho user
    user_settings = UserSettings(
        user_id=user.id,
        currency="JPY",
        role_rates={
            "PM": 23000,
            "Developer": 18000
        }
    )
    
    db_session.add(user_settings)
    await db_session.commit()
    await db_session.refresh(user_settings)
    
    # Kiểm tra settings đã được tạo
    assert user_settings.id is not None
    assert user_settings.user_id == user.id
    assert user_settings.currency == "JPY"
    assert user_settings.role_rates["PM"] == 23000
    
    # Kiểm tra relationship bằng query trực tiếp
    stmt = select(User).join(UserSettings).where(User.id == user.id)
    result = await db_session.execute(stmt)
    joined_user = result.scalar_one()
    assert joined_user is not None
    assert joined_user.id == user.id

async def test_default_values(db_session: AsyncSession):
    """Test giá trị mặc định của model"""
    # Tạo user với giá trị mặc định và email unique
    unique_email = f"default_test_{uuid.uuid4()}@example.com"
    
    user = User(
        email=unique_email,
        hashed_password="hashed_password"
    )
    
    db_session.add(user)
    await db_session.commit()
    await db_session.refresh(user)
    
    # Kiểm tra giá trị mặc định
    assert user.is_active is True
    
    # Tạo settings với giá trị mặc định currency
    user_settings = UserSettings(
        user_id=user.id,
        role_rates={}
    )
    
    db_session.add(user_settings)
    await db_session.commit()
    await db_session.refresh(user_settings)
    
    # Kiểm tra giá trị mặc định
    assert user_settings.currency == "USD"
    assert user_settings.role_rates == {} 