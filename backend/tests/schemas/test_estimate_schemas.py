import pytest
from datetime import datetime
from pydantic import ValidationError

from app.schemas.estimate import (
    DevelopmentItem, 
    SummaryItem, 
    EstimateBase, 
    EstimateCreate, 
    EstimateUpdate, 
    Estimate
)


def test_development_item_valid():
    """Test valid DevelopmentItem creation"""
    data = {
        "feature": "User Authentication",
        "description": "Implement user login and registration",
        "backend_effort": 5.0,
        "frontend_effort": 3.0,
        "unit_test_effort": 2.0,
        "priority": "High"
    }
    
    item = DevelopmentItem(**data)
    
    assert item.feature == "User Authentication"
    assert item.description == "Implement user login and registration"
    assert item.backend_effort == 5.0
    assert item.frontend_effort == 3.0
    assert item.unit_test_effort == 2.0
    assert item.priority == "High"
    assert item.is_selected is True  # Default value


def test_development_item_with_is_selected():
    """Test DevelopmentItem with is_selected specified"""
    data = {
        "feature": "User Authentication",
        "description": "Implement user login and registration",
        "backend_effort": 5.0,
        "frontend_effort": 3.0,
        "unit_test_effort": 2.0,
        "priority": "High",
        "is_selected": False
    }
    
    item = DevelopmentItem(**data)
    assert item.is_selected is False


def test_development_item_invalid_missing_fields():
    """Test DevelopmentItem with missing required fields"""
    data = {
        "feature": "User Authentication",
        # Missing description
        "backend_effort": 5.0,
        "frontend_effort": 3.0,
        "unit_test_effort": 2.0,
        "priority": "High"
    }
    
    with pytest.raises(ValidationError):
        DevelopmentItem(**data)


def test_development_item_invalid_types():
    """Test DevelopmentItem with invalid field types"""
    data = {
        "feature": "User Authentication",
        "description": "Implement user login and registration",
        "backend_effort": "not a number",  # Should be float
        "frontend_effort": 3.0,
        "unit_test_effort": 2.0,
        "priority": "High"
    }
    
    with pytest.raises(ValidationError):
        DevelopmentItem(**data)


def test_summary_item_valid():
    """Test valid SummaryItem creation"""
    data = {
        "category": "Development",
        "effort": 10.0,
        "description": "Backend development"
    }
    
    item = SummaryItem(**data)
    
    assert item.category == "Development"
    assert item.effort == 10.0
    assert item.description == "Backend development"
    assert item.role is None  # Optional field
    assert item.day_rate is None  # Optional field
    assert item.cost is None  # Optional field
    assert item.is_selected is True  # Default value


def test_summary_item_with_optional_fields():
    """Test SummaryItem with optional fields specified"""
    data = {
        "category": "Development",
        "effort": 10.0,
        "description": "Backend development",
        "role": "Developer",
        "day_rate": 500.0,
        "cost": 5000.0,
        "is_selected": False
    }
    
    item = SummaryItem(**data)
    
    assert item.role == "Developer"
    assert item.day_rate == 500.0
    assert item.cost == 5000.0
    assert item.is_selected is False


def test_summary_item_invalid_missing_fields():
    """Test SummaryItem with missing required fields"""
    data = {
        "category": "Development",
        # Missing effort
        "description": "Backend development"
    }
    
    with pytest.raises(ValidationError):
        SummaryItem(**data)


def test_estimate_base_valid():
    """Test valid EstimateBase creation"""
    data = {
        "name": "Project Estimate",
        "project_id": 1
    }
    
    estimate = EstimateBase(**data)
    
    assert estimate.name == "Project Estimate"
    assert estimate.project_id == 1
    assert estimate.development_items is None  # Optional field
    assert estimate.summary_items is None  # Optional field
    assert estimate.grand_total is None  # Optional field
    assert estimate.total_cost is None  # Optional field
    assert estimate.day_rate is None  # Optional field
    assert estimate.currency == "USD"  # Default value


def test_estimate_base_with_optional_fields():
    """Test EstimateBase with optional fields specified"""
    dev_items = [
        {
            "feature": "User Authentication",
            "description": "Implement user login and registration",
            "backend_effort": 5.0,
            "frontend_effort": 3.0,
            "unit_test_effort": 2.0,
            "priority": "High"
        }
    ]
    
    summary_items = [
        {
            "category": "Development",
            "effort": 10.0,
            "description": "Backend development",
            "role": "Developer",
            "day_rate": 500.0,
            "cost": 5000.0
        }
    ]
    
    data = {
        "name": "Project Estimate",
        "project_id": 1,
        "development_items": dev_items,
        "summary_items": summary_items,
        "grand_total": 10.0,
        "total_cost": 5000.0,
        "day_rate": 500.0,
        "currency": "EUR"
    }
    
    estimate = EstimateBase(**data)
    
    assert estimate.development_items == dev_items
    assert estimate.summary_items == summary_items
    assert estimate.grand_total == 10.0
    assert estimate.total_cost == 5000.0
    assert estimate.day_rate == 500.0
    assert estimate.currency == "EUR"


def test_estimate_create():
    """Test EstimateCreate schema"""
    data = {
        "name": "Project Estimate",
        "project_id": 1
    }
    
    estimate = EstimateCreate(**data)
    
    assert estimate.name == "Project Estimate"
    assert estimate.project_id == 1
    assert estimate.development_items is None
    assert estimate.summary_items is None
    assert estimate.grand_total is None
    assert estimate.total_cost is None
    assert estimate.day_rate is None
    assert estimate.currency == "USD"


def test_estimate_update_valid():
    """Test valid EstimateUpdate creation"""
    # Empty update is valid
    estimate = EstimateUpdate()
    
    assert estimate.name is None
    assert estimate.development_items is None
    assert estimate.summary_items is None
    assert estimate.grand_total is None
    assert estimate.total_cost is None
    assert estimate.day_rate is None
    assert estimate.currency is None
    assert estimate.is_active is None


def test_estimate_update_with_fields():
    """Test EstimateUpdate with fields specified"""
    data = {
        "name": "Updated Estimate",
        "grand_total": 15.0,
        "total_cost": 7500.0,
        "is_active": False
    }
    
    estimate = EstimateUpdate(**data)
    
    assert estimate.name == "Updated Estimate"
    assert estimate.grand_total == 15.0
    assert estimate.total_cost == 7500.0
    assert estimate.is_active is False
    assert estimate.development_items is None  # Not updated
    assert estimate.summary_items is None  # Not updated


def test_estimate_schema():
    """Test Estimate schema (full model)"""
    now = datetime.utcnow()
    
    data = {
        "id": 1,
        "name": "Project Estimate",
        "project_id": 1,
        "is_active": True,
        "created_at": now,
        "updated_at": now
    }
    
    estimate = Estimate(**data)
    
    assert estimate.id == 1
    assert estimate.name == "Project Estimate"
    assert estimate.project_id == 1
    assert estimate.is_active is True
    assert estimate.created_at == now
    assert estimate.updated_at == now
    assert estimate.development_items is None
    assert estimate.summary_items is None
    assert estimate.grand_total is None
    assert estimate.total_cost is None
    assert estimate.day_rate is None
    assert estimate.currency == "USD"


def test_estimate_with_all_fields():
    """Test Estimate schema with all fields populated"""
    now = datetime.utcnow()
    
    dev_items = [
        {
            "feature": "User Authentication",
            "description": "Implement user login and registration",
            "backend_effort": 5.0,
            "frontend_effort": 3.0,
            "unit_test_effort": 2.0,
            "priority": "High"
        }
    ]
    
    summary_items = [
        {
            "category": "Development",
            "effort": 10.0,
            "description": "Backend development",
            "role": "Developer",
            "day_rate": 500.0,
            "cost": 5000.0
        }
    ]
    
    data = {
        "id": 1,
        "name": "Project Estimate",
        "project_id": 1,
        "development_items": dev_items,
        "summary_items": summary_items,
        "grand_total": 10.0,
        "total_cost": 5000.0,
        "day_rate": 500.0,
        "currency": "EUR",
        "is_active": True,
        "created_at": now,
        "updated_at": now
    }
    
    estimate = Estimate(**data)
    
    assert estimate.id == 1
    assert estimate.name == "Project Estimate"
    assert estimate.project_id == 1
    assert estimate.development_items == dev_items
    assert estimate.summary_items == summary_items
    assert estimate.grand_total == 10.0
    assert estimate.total_cost == 5000.0
    assert estimate.day_rate == 500.0
    assert estimate.currency == "EUR"
    assert estimate.is_active is True
    assert estimate.created_at == now
    assert estimate.updated_at == now
