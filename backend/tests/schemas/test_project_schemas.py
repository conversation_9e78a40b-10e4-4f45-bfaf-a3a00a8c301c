import pytest
from datetime import datetime
from pydantic import ValidationError

from app.schemas.project import (
    ProjectBase,
    ProjectCreate,
    ProjectUpdate,
    Project
)


def test_project_base_valid():
    """Test valid ProjectBase creation"""
    data = {
        "name": "E-commerce Platform"
    }
    
    project = ProjectBase(**data)
    
    assert project.name == "E-commerce Platform"
    assert project.description is None
    assert project.software_type is None
    assert project.technology_stack is None


def test_project_base_with_optional_fields():
    """Test ProjectBase with optional fields specified"""
    data = {
        "name": "E-commerce Platform",
        "description": "An online shopping platform",
        "software_type": "Web Application",
        "technology_stack": "React, FastAPI, PostgreSQL"
    }
    
    project = ProjectBase(**data)
    
    assert project.name == "E-commerce Platform"
    assert project.description == "An online shopping platform"
    assert project.software_type == "Web Application"
    assert project.technology_stack == "React, FastAPI, PostgreSQL"


def test_project_base_invalid_missing_name():
    """Test ProjectBase with missing required name field"""
    data = {
        "description": "An online shopping platform"
    }
    
    with pytest.raises(ValidationError):
        ProjectBase(**data)


def test_project_create():
    """Test ProjectCreate schema"""
    data = {
        "name": "E-commerce Platform",
        "description": "An online shopping platform",
        "software_type": "Web Application",
        "technology_stack": "React, FastAPI, PostgreSQL"
    }
    
    project = ProjectCreate(**data)
    
    assert project.name == "E-commerce Platform"
    assert project.description == "An online shopping platform"
    assert project.software_type == "Web Application"
    assert project.technology_stack == "React, FastAPI, PostgreSQL"


def test_project_update_empty():
    """Test ProjectUpdate with no fields specified"""
    project = ProjectUpdate()
    
    assert project.name is None
    assert project.description is None
    assert project.software_type is None
    assert project.technology_stack is None
    assert project.requirements_text is None


def test_project_update_with_fields():
    """Test ProjectUpdate with fields specified"""
    data = {
        "name": "Updated E-commerce Platform",
        "description": "Updated description",
        "requirements_text": "User authentication, product listing, shopping cart"
    }
    
    project = ProjectUpdate(**data)
    
    assert project.name == "Updated E-commerce Platform"
    assert project.description == "Updated description"
    assert project.requirements_text == "User authentication, product listing, shopping cart"
    assert project.software_type is None
    assert project.technology_stack is None


def test_project_schema():
    """Test Project schema (full model)"""
    now = datetime.utcnow()
    
    data = {
        "id": 1,
        "user_id": 2,
        "name": "E-commerce Platform",
        "description": "An online shopping platform",
        "software_type": "Web Application",
        "technology_stack": "React, FastAPI, PostgreSQL",
        "requirements_text": "User authentication, product listing, shopping cart",
        "requirements_file_path": "/uploads/requirements.pdf",
        "created_at": now,
        "updated_at": now
    }
    
    project = Project(**data)
    
    assert project.id == 1
    assert project.user_id == 2
    assert project.name == "E-commerce Platform"
    assert project.description == "An online shopping platform"
    assert project.software_type == "Web Application"
    assert project.technology_stack == "React, FastAPI, PostgreSQL"
    assert project.requirements_text == "User authentication, product listing, shopping cart"
    assert project.requirements_file_path == "/uploads/requirements.pdf"
    assert project.created_at == now
    assert project.updated_at == now


def test_project_schema_minimal():
    """Test Project schema with minimal required fields"""
    now = datetime.utcnow()
    
    data = {
        "id": 1,
        "user_id": 2,
        "name": "E-commerce Platform",
        "created_at": now
    }
    
    project = Project(**data)
    
    assert project.id == 1
    assert project.user_id == 2
    assert project.name == "E-commerce Platform"
    assert project.description is None
    assert project.software_type is None
    assert project.technology_stack is None
    assert project.requirements_text is None
    assert project.requirements_file_path is None
    assert project.created_at == now
    assert project.updated_at is None
