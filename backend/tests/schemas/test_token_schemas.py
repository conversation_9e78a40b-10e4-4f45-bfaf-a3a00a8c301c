import pytest
from pydantic import ValidationError

from app.schemas.token import Token, TokenPayload


def test_token_valid():
    """Test valid Token creation"""
    data = {
        "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c",
        "token_type": "bearer"
    }
    token = Token(**data)
    assert token.access_token == "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c"
    assert token.token_type == "bearer"


def test_token_missing_fields():
    """Test Token with missing required fields"""
    # Missing access_token
    with pytest.raises(ValidationError):
        Token(token_type="bearer")
    
    # Missing token_type
    with pytest.raises(ValidationError):
        Token(access_token="some_token")


def test_token_payload_valid():
    """Test valid TokenPayload creation"""
    data = {"user_id": 123}
    payload = TokenPayload(**data)
    assert payload.user_id == 123


def test_token_payload_empty():
    """Test TokenPayload with no fields specified"""
    payload = TokenPayload()
    assert payload.user_id is None


def test_token_payload_with_none():
    """Test TokenPayload with explicit None"""
    payload = TokenPayload(user_id=None)
    assert payload.user_id is None
