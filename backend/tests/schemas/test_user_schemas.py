import pytest
from datetime import datetime
from pydantic import ValidationError

from app.schemas.user import (
    UserBase,
    UserCreate,
    UserLogin,
    UserGoogleAuth,
    User,
    PasswordChange,
    EmailVerification,
    ResendVerification,
    UserRoleRate,
    CurrencyType,
    UserSettings,
    UserSettingsUpdate
)


def test_user_base_valid():
    """Test valid UserBase creation"""
    data = {"email": "<EMAIL>"}
    user = UserBase(**data)
    assert user.email == "<EMAIL>"


def test_user_base_invalid_email():
    """Test UserBase with invalid email"""
    data = {"email": "not-an-email"}
    with pytest.raises(ValidationError):
        UserBase(**data)


def test_user_create_valid():
    """Test valid UserCreate creation"""
    data = {
        "email": "<EMAIL>",
        "password": "Password123!",  # Added special character to meet new requirements
        "password_confirm": "Password123!"  # Added special character to meet new requirements
    }
    user = UserCreate(**data)
    assert user.email == "<EMAIL>"
    assert user.password == "Password123!"
    assert user.password_confirm == "Password123!"


def test_user_create_password_too_short():
    """Test UserCreate with password that's too short"""
    data = {
        "email": "<EMAIL>",
        "password": "short",  # Less than 8 characters
        "password_confirm": "short"
    }
    with pytest.raises(ValidationError):
        UserCreate(**data)


def test_user_login_valid():
    """Test valid UserLogin creation"""
    data = {
        "email": "<EMAIL>",
        "password": "Password123"
    }
    user = UserLogin(**data)
    assert user.email == "<EMAIL>"
    assert user.password == "Password123"


def test_user_google_auth_valid():
    """Test valid UserGoogleAuth creation"""
    data = {
        "email": "<EMAIL>",
        "google_id": "google_12345"
    }
    user = UserGoogleAuth(**data)
    assert user.email == "<EMAIL>"
    assert user.google_id == "google_12345"


def test_user_valid():
    """Test valid User creation"""
    now = datetime.utcnow()
    data = {
        "id": 1,
        "email": "<EMAIL>",
        "is_active": True,
        "created_at": now
    }
    user = User(**data)
    assert user.id == 1
    assert user.email == "<EMAIL>"
    assert user.is_active is True
    assert user.is_verified is False  # Default value
    assert user.created_at == now


def test_password_change_valid():
    """Test valid PasswordChange creation"""
    data = {
        "current_password": "OldPassword123!",  # Added special character to meet new requirements
        "new_password": "NewPassword123!",  # Added special character to meet new requirements
        "confirm_password": "NewPassword123!"  # Added special character to meet new requirements
    }
    pwd_change = PasswordChange(**data)
    assert pwd_change.current_password == "OldPassword123!"
    assert pwd_change.new_password == "NewPassword123!"
    assert pwd_change.confirm_password == "NewPassword123!"


def test_password_change_new_password_too_short():
    """Test PasswordChange with new password that's too short"""
    data = {
        "current_password": "OldPassword123",
        "new_password": "short",  # Less than 8 characters
        "confirm_password": "short"
    }
    with pytest.raises(ValidationError):
        PasswordChange(**data)


def test_email_verification_valid():
    """Test valid EmailVerification creation"""
    data = {
        "email": "<EMAIL>",
        "code": "123456"
    }
    verification = EmailVerification(**data)
    assert verification.email == "<EMAIL>"
    assert verification.code == "123456"


def test_resend_verification_valid():
    """Test valid ResendVerification creation"""
    data = {"email": "<EMAIL>"}
    resend = ResendVerification(**data)
    assert resend.email == "<EMAIL>"


def test_user_role_rate_valid():
    """Test valid UserRoleRate creation"""
    data = {
        "role": "Developer",
        "rate": 500.0
    }
    role_rate = UserRoleRate(**data)
    assert role_rate.role == "Developer"
    assert role_rate.rate == 500.0


def test_currency_type_enum():
    """Test CurrencyType enum"""
    assert CurrencyType.USD.value == "USD"
    assert CurrencyType.VND.value == "VND"
    assert CurrencyType.JPY.value == "JPY"
    
    # Test creating from string
    assert CurrencyType("USD") == CurrencyType.USD
    assert CurrencyType("VND") == CurrencyType.VND
    assert CurrencyType("JPY") == CurrencyType.JPY
    
    # Test invalid currency
    with pytest.raises(ValueError):
        CurrencyType("INVALID")


def test_user_settings_valid():
    """Test valid UserSettings creation"""
    data = {
        "user_id": 1
    }
    settings = UserSettings(**data)
    assert settings.user_id == 1
    assert settings.currency == "USD"  # Default value
    assert settings.role_rates == {}  # Default value


def test_user_settings_with_all_fields():
    """Test UserSettings with all fields specified"""
    data = {
        "id": 1,
        "user_id": 2,
        "currency": "JPY",
        "role_rates": {
            "Developer": 500.0,
            "Designer": 450.0,
            "PM": 600.0
        }
    }
    settings = UserSettings(**data)
    assert settings.id == 1
    assert settings.user_id == 2
    assert settings.currency == "JPY"
    assert settings.role_rates == {
        "Developer": 500.0,
        "Designer": 450.0,
        "PM": 600.0
    }


def test_user_settings_update_empty():
    """Test UserSettingsUpdate with no fields specified"""
    settings_update = UserSettingsUpdate()
    assert settings_update.currency is None
    assert settings_update.role_rates is None


def test_user_settings_update_with_fields():
    """Test UserSettingsUpdate with fields specified"""
    data = {
        "currency": "VND",
        "role_rates": {
            "Developer": 550.0,
            "Designer": 500.0
        }
    }
    settings_update = UserSettingsUpdate(**data)
    assert settings_update.currency == CurrencyType.VND
    assert settings_update.role_rates == {
        "Developer": 550.0,
        "Designer": 500.0
    }
