import pytest
from pydantic import Validation<PERSON>rror
import re
from app.schemas.user import <PERSON><PERSON><PERSON><PERSON>, Pass<PERSON><PERSON><PERSON><PERSON>, User<PERSON>ase
from typing import Dict, Any


def create_user_data(overrides: Dict[str, Any] = None) -> Dict[str, Any]:
    """Helper function to create user data for tests"""
    data = {
        "email": "<EMAIL>",
        "password": "Test1234!",
        "password_confirm": "Test1234!"
    }
    if overrides:
        data.update(overrides)
    return data

def test_user_create_valid_password():
    """Test that a valid password passes validation"""
    # Create a valid user
    data = create_user_data()
    user = UserCreate(**data)
    assert user.password == "Test1234!"
    assert user.password_confirm == "Test1234!"
    
def test_user_create_password_too_short():
    """Test that a password that's too short fails validation"""
    with pytest.raises(ValidationError) as excinfo:
        data = create_user_data({"password": "Short1!", "password_confirm": "Short1!"})
        UserCreate(**data)
    
    # Check the error message
    error_detail = str(excinfo.value)
    assert "8 characters" in error_detail or "length" in error_detail
    
def test_user_create_password_no_uppercase():
    """Test that a password without uppercase fails validation"""
    with pytest.raises(ValidationError) as excinfo:
        data = create_user_data({"password": "test1234!", "password_confirm": "test1234!"})
        UserCreate(**data)
    
    # Check the error message
    error_detail = str(excinfo.value)
    assert "uppercase" in error_detail
    
def test_user_create_password_no_lowercase():
    """Test that a password without lowercase fails validation"""
    with pytest.raises(ValidationError) as excinfo:
        data = create_user_data({"password": "TEST1234!", "password_confirm": "TEST1234!"})
        UserCreate(**data)
    
    # Check the error message
    error_detail = str(excinfo.value)
    assert "lowercase" in error_detail
    
def test_user_create_password_no_number():
    """Test that a password without a number fails validation"""
    with pytest.raises(ValidationError) as excinfo:
        data = create_user_data({"password": "TestTest!", "password_confirm": "TestTest!"})
        UserCreate(**data)
    
    # Check the error message
    error_detail = str(excinfo.value)
    assert "number" in error_detail
    
def test_user_create_password_no_special_char():
    """Test that a password without a special character fails validation"""
    with pytest.raises(ValidationError) as excinfo:
        data = create_user_data({"password": "Test12345", "password_confirm": "Test12345"})
        UserCreate(**data)
    
    # Check the error message
    error_detail = str(excinfo.value)
    assert "special character" in error_detail
    
def test_user_create_passwords_dont_match():
    """Test that passwords that don't match fail validation"""
    with pytest.raises(ValidationError) as excinfo:
        data = create_user_data({"password": "Test1234!", "password_confirm": "Test5678@"})
        UserCreate(**data)
    
    # Check the error message
    error_detail = str(excinfo.value)
    assert "match" in error_detail


def create_password_change_data(overrides: Dict[str, Any] = None) -> Dict[str, Any]:
    """Helper function to create password change data for tests"""
    data = {
        "current_password": "OldPass123!",
        "new_password": "NewTest456@",
        "confirm_password": "NewTest456@"
    }
    if overrides:
        data.update(overrides)
    return data

def test_password_change_valid():
    """Test that a valid password change passes validation"""
    # Create a valid password change
    data = create_password_change_data()
    change = PasswordChange(**data)
    assert change.new_password == "NewTest456@"
    assert change.confirm_password == "NewTest456@"
    
def test_password_change_too_short():
    """Test that a new password that's too short fails validation"""
    with pytest.raises(ValidationError) as excinfo:
        data = create_password_change_data({"new_password": "Short1!", "confirm_password": "Short1!"})
        PasswordChange(**data)
    
    # Check the error message
    error_detail = str(excinfo.value)
    assert "8 characters" in error_detail or "length" in error_detail
    
def test_password_change_no_uppercase():
    """Test that a new password without uppercase fails validation"""
    with pytest.raises(ValidationError) as excinfo:
        data = create_password_change_data({"new_password": "test1234!", "confirm_password": "test1234!"})
        PasswordChange(**data)
    
    # Check the error message
    error_detail = str(excinfo.value)
    assert "uppercase" in error_detail
    
def test_password_change_no_lowercase():
    """Test that a new password without lowercase fails validation"""
    with pytest.raises(ValidationError) as excinfo:
        data = create_password_change_data({"new_password": "TEST1234!", "confirm_password": "TEST1234!"})
        PasswordChange(**data)
    
    # Check the error message
    error_detail = str(excinfo.value)
    assert "lowercase" in error_detail
    
def test_password_change_no_number():
    """Test that a new password without a number fails validation"""
    with pytest.raises(ValidationError) as excinfo:
        data = create_password_change_data({"new_password": "TestTest!", "confirm_password": "TestTest!"})
        PasswordChange(**data)
    
    # Check the error message
    error_detail = str(excinfo.value)
    assert "number" in error_detail
    
def test_password_change_no_special_char():
    """Test that a new password without a special character fails validation"""
    with pytest.raises(ValidationError) as excinfo:
        data = create_password_change_data({"new_password": "Test12345", "confirm_password": "Test12345"})
        PasswordChange(**data)
    
    # Check the error message
    error_detail = str(excinfo.value)
    assert "special character" in error_detail
    
def test_password_change_passwords_dont_match():
    """Test that new passwords that don't match fail validation"""
    with pytest.raises(ValidationError) as excinfo:
        data = create_password_change_data({"new_password": "Test1234!", "confirm_password": "Test5678@"})
        PasswordChange(**data)
    
    # Check the error message
    error_detail = str(excinfo.value)
    assert "match" in error_detail
