import pytest
import pytest_asyncio
import asyncio
import os
from typing import AsyncGenerator, Generator
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import NullPool
from sqlalchemy import text

from app.core.config import settings
from app.db.session import Base, get_db
from app.main import app
from app.models.user import User, UserSettings

# Sử dụng file database để tất cả các test đều có thể chia sẻ database
TEST_DB_FILE = "test_db.sqlite"
# Xóa file database cũ nếu tồn tại
if os.path.exists(TEST_DB_FILE):
    os.remove(TEST_DB_FILE)

TEST_DATABASE_URL = f"sqlite+aiosqlite:///{TEST_DB_FILE}"

# Tạo engine và session maker cho database test
engine = create_async_engine(
    TEST_DATABASE_URL, 
    future=True,
    # Connect args để SQLite hỗ trợ foreign keys
    connect_args={"check_same_thread": False}
)
TestingSessionLocal = sessionmaker(
    engine, 
    class_=AsyncSession, 
    expire_on_commit=False,
    autocommit=False,
    autoflush=False
)

# Override dependency get_db
async def override_get_db() -> AsyncGenerator[AsyncSession, None]:
    async with TestingSessionLocal() as session:
        yield session

app.dependency_overrides[get_db] = override_get_db

@pytest.fixture(scope="session")
def event_loop() -> Generator[asyncio.AbstractEventLoop, None, None]:
    """Create event loop for pytest-asyncio"""
    policy = asyncio.get_event_loop_policy()
    loop = policy.new_event_loop()
    asyncio.set_event_loop(loop)
    yield loop
    loop.close()

@pytest_asyncio.fixture(scope="session", autouse=True)
async def setup_database() -> None:
    """Tạo bảng trước mỗi test session và tự động chạy cho tất cả các tests"""
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)
        await conn.run_sync(Base.metadata.create_all)
    
    # Kiểm tra bảng đã được tạo
    async with TestingSessionLocal() as session:
        result = await session.execute(text("SELECT name FROM sqlite_master WHERE type='table' AND name='users'"))
        tables = result.scalars().all()
        assert len(tables) > 0, "Tables were not created properly"
        
        # Kiểm tra tất cả các cột trong bảng users
        result = await session.execute(text("PRAGMA table_info(users)"))
        columns = result.fetchall()
        column_names = [column[1] for column in columns]
        
        # Đảm bảo có tất cả các cột cần thiết
        required_columns = ["id", "email", "hashed_password", "is_active", 
                            "created_at", "updated_at", "is_verified", 
                            "verification_code", "verification_code_expires_at"]
        for column in required_columns:
            assert column in column_names, f"Column '{column}' missing in users table"

@pytest_asyncio.fixture
async def db_session() -> AsyncSession:
    """Tạo một session mới cho mỗi test và dùng transaction để rollback"""
    # Đảm bảo có event loop đang chạy
    try:
        loop = asyncio.get_event_loop()
        if loop.is_closed():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
    except RuntimeError:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
    
    connection = await engine.connect()
    transaction = await connection.begin()
    
    session = TestingSessionLocal(bind=connection)
    
    try:
        yield session
    finally:
        await session.close()
        await transaction.rollback()
        await connection.close()

@pytest_asyncio.fixture
async def client() -> AsyncClient:
    """Tạo test client với database đã được setup"""
    # Use setup_database fixture chạy tự động (do autouse=True)
    async with AsyncClient(app=app, base_url="http://test") as ac:
        yield ac

@pytest_asyncio.fixture
async def create_test_user(db_session: AsyncSession) -> User:
    """Tạo một user test"""
    # Kiểm tra xem user đã tồn tại chưa
    result = await db_session.execute(text("SELECT id FROM users WHERE email = :email"), {"email": "<EMAIL>"})
    existing_user = result.scalar_one_or_none()
    
    if existing_user:
        # Nếu đã tồn tại, lấy user đó - CHÚ Ý: chỉ lấy các cột cần thiết để tránh lỗi
        result = await db_session.execute(
            text("""SELECT id, email, hashed_password, is_active, created_at, 
                is_verified, verification_code, verification_code_expires_at 
                FROM users WHERE id = :id"""), 
            {"id": existing_user}
        )
        user_data = result.mappings().one()
        
        # Tạo đối tượng User chỉ với dữ liệu từ database
        user = User(
            id=user_data["id"],
            email=user_data["email"],
            hashed_password=user_data["hashed_password"],
            is_active=user_data["is_active"],
            created_at=user_data["created_at"],
            is_verified=user_data["is_verified"],
            verification_code=user_data["verification_code"],
            verification_code_expires_at=user_data["verification_code_expires_at"]
        )
        return user
    
    # Nếu chưa tồn tại, tạo mới
    user = User(
        email="<EMAIL>",
        hashed_password="$2b$12$qkAActWnF.2Z3YA1SHg9BeBKa4Vm3IfwzfYwfJXCARVYwEqrxDwLK",  # password: test123
        is_active=True,
        is_verified=True  # Đảm bảo người dùng đã được xác minh
    )
    db_session.add(user)
    await db_session.commit()
    await db_session.refresh(user)
    
    # Kiểm tra xem user settings đã tồn tại chưa
    result = await db_session.execute(text("SELECT id FROM user_settings WHERE user_id = :user_id"), {"user_id": user.id})
    existing_settings = result.scalar_one_or_none()
    
    if not existing_settings:
        # Nếu chưa tồn tại, tạo mới
        user_settings = UserSettings(
            user_id=user.id,
            currency="USD",
            role_rates={
                "PM": 200,
                "Developer": 150,
                "Designer": 150,
                "Tester": 150
            }
        )
        db_session.add(user_settings)
        await db_session.commit()
    
    return user

@pytest_asyncio.fixture
async def authenticated_client(client: AsyncClient, create_test_user: User) -> AsyncClient:
    """Tạo client đã được xác thực với token"""
    try:
        # Thay vì gọi API login, tạo trực tiếp token giả lập
        from app.services.auth_service import create_access_token
        from datetime import timedelta
        from app.core.config import settings
        
        # Tạo token với user_id của create_test_user
        access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        token = create_access_token(user_id=create_test_user.id, expires_delta=access_token_expires)
        
        # Thiết lập header xác thực
        client.headers.update({"Authorization": f"Bearer {token}"})
        
        return client
    except Exception as e:
        print(f"Error creating authenticated client: {e}")
        # Return unauthenticated client as fallback
        return client 