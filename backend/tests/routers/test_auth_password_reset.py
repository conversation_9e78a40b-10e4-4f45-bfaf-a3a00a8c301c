import pytest
from unittest.mock import patch, Mock
from fastapi.testclient import <PERSON><PERSON><PERSON>
from fastapi import status

from app.main import app
from app.schemas.user import ResetPasswordRequest, ResetPassword


client = TestClient(app)


class TestPasswordResetRoutes:
    
    @patch('app.routers.auth_routes.create_password_reset_token')
    def test_reset_password_request_success_with_locale(self, mock_create_token):
        """Test successful password reset request with locale"""
        from unittest.mock import ANY
        mock_create_token.return_value = "mock_token_123"

         # Test data
        request_data = {"email": "<EMAIL>", "locale": "vi"}

         # Make request
        response = client.post("/api/v1/auth/reset-password-request", json=request_data)

         # Assertions
        assert response.status_code == status.HTTP_200_OK
        assert response.json() == {
            "message": "If your email is registered, you will receive a reset link"
        }
        mock_create_token.assert_called_once_with(ANY, "<EMAIL>", "vi")

    @patch('app.routers.auth_routes.create_password_reset_token')
    def test_reset_password_request_success_default_locale(self, mock_create_token):
        """Test successful password reset request without locale (should default to en)"""
        from unittest.mock import ANY
        mock_create_token.return_value = "mock_token_123"
        request_data = {"email": "<EMAIL>"}
        response = client.post("/api/v1/auth/reset-password-request", json=request_data)
        assert response.status_code == status.HTTP_200_OK
        assert response.json() == {
            "message": "If your email is registered, you will receive a reset link"
        }
        mock_create_token.assert_called_once_with(ANY, "<EMAIL>", "en")

    @patch('app.routers.auth_routes.create_password_reset_token')
    def test_reset_password_request_service_error(self, mock_create_token):
        """Test password reset request when service throws error"""
        # Mock the service function to raise an exception
        mock_create_token.side_effect = Exception("Service error")
        
        # Test data
        request_data = {"email": "<EMAIL>"}
        
        # Make request
        response = client.post("/api/v1/auth/reset-password-request", json=request_data)
        
        # Assertions - should still return success for security
        assert response.status_code == status.HTTP_200_OK
        assert response.json() == {
            "message": "If your email is registered, you will receive a reset link"
        }

    def test_reset_password_request_invalid_email(self):
        """Test password reset request with invalid email format"""
        # Test data with invalid email
        request_data = {"email": "invalid-email"}
        
        # Make request
        response = client.post("/api/v1/auth/reset-password-request", json=request_data)
        
        # Assertions
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    def test_reset_password_request_missing_email(self):
        """Test password reset request without email"""
        # Test data without email
        request_data = {}
        
        # Make request
        response = client.post("/api/v1/auth/reset-password-request", json=request_data)
        
        # Assertions
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    @patch('app.routers.auth_routes.reset_user_password')
    def test_reset_password_success(self, mock_reset_password):
        """Test successful password reset"""
        # Mock the service function
        mock_reset_password.return_value = True
        
        # Test data
        request_data = {
            "token": "valid_token_123",
            "new_password": "NewPassword123!"
        }
        
        # Make request
        response = client.post("/api/v1/auth/reset-password", json=request_data)
        
        # Assertions
        assert response.status_code == status.HTTP_200_OK
        assert response.json() == {"message": "Password reset successfully"}
        mock_reset_password.assert_called_once_with(
            mock_reset_password.call_args[0][0],  # db session
            "valid_token_123",
            "NewPassword123!"
        )

    @patch('app.routers.auth_routes.reset_user_password')
    def test_reset_password_invalid_token(self, mock_reset_password):
        """Test password reset with invalid token"""
        # Mock the service function to return False
        mock_reset_password.return_value = False
        
        # Test data
        request_data = {
            "token": "invalid_token",
            "new_password": "NewPassword123!"
        }
        
        # Make request
        response = client.post("/api/v1/auth/reset-password", json=request_data)
        
        # Assertions
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert response.json() == {"detail": "Invalid or expired reset token"}

    @patch('app.routers.auth_routes.reset_user_password')
    def test_reset_password_service_error(self, mock_reset_password):
        """Test password reset when service throws error"""
        # Mock the service function to raise an exception
        mock_reset_password.side_effect = Exception("Service error")
        
        # Test data
        request_data = {
            "token": "valid_token_123",
            "new_password": "NewPassword123!"
        }
        
        # Make request
        response = client.post("/api/v1/auth/reset-password", json=request_data)
        
        # Assertions
        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        assert response.json() == {"detail": "Password reset failed"}

    def test_reset_password_weak_password(self):
        """Test password reset with weak password"""
        # Test data with weak password
        request_data = {
            "token": "valid_token_123",
            "new_password": "weak"
        }
        
        # Make request
        response = client.post("/api/v1/auth/reset-password", json=request_data)
        
        # Assertions
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    def test_reset_password_missing_fields(self):
        """Test password reset with missing fields"""
        # Test data without required fields
        request_data = {"token": "valid_token_123"}
        
        # Make request
        response = client.post("/api/v1/auth/reset-password", json=request_data)
        
        # Assertions
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    def test_reset_password_empty_token(self):
        """Test password reset with empty token"""
        # Test data with empty token
        request_data = {
            "token": "",
            "new_password": "NewPassword123!"
        }
        
        # Make request
        response = client.post("/api/v1/auth/reset-password", json=request_data)
        
        # Assertions
        assert response.status_code == status.HTTP_400_BAD_REQUEST

    def test_reset_password_no_uppercase(self):
        """Test password reset with password lacking uppercase"""
        # Test data with password without uppercase
        request_data = {
            "token": "valid_token_123",
            "new_password": "newpassword123!"
        }
        
        # Make request
        response = client.post("/api/v1/auth/reset-password", json=request_data)
        
        # Assertions
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    def test_reset_password_no_special_char(self):
        """Test password reset with password lacking special character"""
        # Test data with password without special character
        request_data = {
            "token": "valid_token_123",
            "new_password": "NewPassword123"
        }
        
        # Make request
        response = client.post("/api/v1/auth/reset-password", json=request_data)
        
        # Assertions
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    def test_reset_password_no_number(self):
        """Test password reset with password lacking number"""
        # Test data with password without number
        request_data = {
            "token": "valid_token_123",
            "new_password": "NewPassword!"
        }
        
        # Make request
        response = client.post("/api/v1/auth/reset-password", json=request_data)
        
        # Assertions
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY 