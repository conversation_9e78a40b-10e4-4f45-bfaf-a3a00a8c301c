import pytest
from unittest.mock import patch, MagicMock
from datetime import datetime, timedelta, timezone
from fastapi import HTTPException

from app.services.email_verification_service import (
    generate_verification_code,
    get_db_connection,
    create_verification_code,
    verify_email_code,
    resend_verification_code,
    send_verification_email
)


def test_generate_verification_code():
    """Test that verification codes are generated with the correct length and format"""
    # Test default length (6)
    code = generate_verification_code()
    assert len(code) == 6
    assert code.isdigit()
    
    # Test custom length
    code = generate_verification_code(length=8)
    assert len(code) == 8
    assert code.isdigit()


@patch('psycopg2.connect')
def test_get_db_connection(mock_connect):
    """Test database connection function"""
    # Setup mock connection
    mock_conn = MagicMock()
    mock_connect.return_value = mock_conn
    
    # Call the function
    conn = get_db_connection()
    
    # Verify connection was created with correct parameters
    mock_connect.assert_called_once()
    assert conn == mock_conn


@patch('app.services.email_verification_service.get_db_connection')
@patch('app.services.email_verification_service.generate_verification_code')
@patch('app.services.email_verification_service.datetime')
def test_create_verification_code(mock_datetime, mock_generate_code, mock_get_db):
    """Test creating a verification code"""
    # Setup mocks
    mock_code = "123456"
    mock_generate_code.return_value = mock_code
    
    # Create a current time that will be used for comparison
    current_time = datetime.now(timezone.utc)
    # Mock datetime.now to return this current_time
    mock_datetime.now.return_value = current_time
    # Set up timezone.utc correctly
    mock_datetime.timezone.utc = timezone.utc
    
    mock_conn = MagicMock()
    mock_cursor = MagicMock()
    mock_conn.cursor.return_value = mock_cursor
    mock_get_db.return_value = mock_conn
    
    # Call the function
    user_id = 1
    result = create_verification_code(user_id)
    
    # Verify the code was generated and saved to the database
    mock_generate_code.assert_called_once()
    mock_get_db.assert_called_once()
    mock_cursor.execute.assert_called_once()
    mock_conn.commit.assert_called_once()
    mock_conn.close.assert_called_once()
    
    # Verify the function returns the generated code
    assert result == mock_code


@patch('app.services.email_verification_service.get_db_connection')
@patch('app.services.email_verification_service.datetime')
def test_verify_email_code_success(mock_datetime, mock_get_db):
    """Test successful email verification"""
    # Setup mocks
    mock_conn = MagicMock()
    mock_cursor = MagicMock()
    mock_conn.cursor.return_value = mock_cursor
    mock_get_db.return_value = mock_conn
    
    # Create a current time that will be used for comparison
    current_time = datetime.now(timezone.utc)
    # Mock datetime.now to return this current_time
    mock_datetime.now.return_value = current_time
    # Set up timezone.utc correctly
    mock_datetime.timezone.utc = timezone.utc
    
    # Mock user data with valid verification code
    user_id = 1
    verification_code = "123456"
    # Create a future date that won't be expired (5 minutes in the future)
    expires_at = current_time + timedelta(minutes=5)
    # Ensure expires_at has tzinfo attribute for comparison
    expires_at = expires_at.replace(tzinfo=timezone.utc)
    mock_cursor.fetchone.return_value = (user_id, verification_code, expires_at)
    
    # Call the function
    email = "<EMAIL>"
    result = verify_email_code(email, verification_code)
    
    # Verify database interactions
    assert mock_cursor.execute.call_count == 2
    mock_conn.commit.assert_called_once()
    mock_conn.close.assert_called_once()
    
    # Verify the function returns True for successful verification
    assert result is True


@patch('app.services.email_verification_service.get_db_connection')
@patch('app.services.email_verification_service.datetime')
def test_verify_email_code_invalid_code(mock_datetime, mock_get_db):
    """Test verification with invalid code"""
    # Setup mocks
    mock_conn = MagicMock()
    mock_cursor = MagicMock()
    mock_conn.cursor.return_value = mock_cursor
    mock_get_db.return_value = mock_conn
    
    # Create a current time that will be used for comparison
    current_time = datetime.now(timezone.utc)
    # Mock datetime.now to return this current_time
    mock_datetime.now.return_value = current_time
    # Set up timezone.utc correctly
    mock_datetime.timezone.utc = timezone.utc
    
    # Mock user data with different verification code
    user_id = 1
    verification_code = "123456"
    # Create a future date that won't be expired (5 minutes in the future)
    expires_at = current_time + timedelta(minutes=5)
    # Ensure expires_at has tzinfo attribute for comparison
    expires_at = expires_at.replace(tzinfo=timezone.utc)
    mock_cursor.fetchone.return_value = (user_id, verification_code, expires_at)
    
    # Call the function with a different code
    email = "<EMAIL>"
    result = verify_email_code(email, "654321")
    
    # Verify database interactions
    mock_cursor.execute.assert_called_once()
    mock_conn.commit.assert_not_called()
    mock_conn.close.assert_called_once()
    
    # Verify the function returns False for invalid code
    assert result is False


@patch('app.services.email_verification_service.get_db_connection')
@patch('app.services.email_verification_service.datetime')
def test_verify_email_code_expired(mock_datetime, mock_get_db):
    """Test verification with expired code"""
    # Setup mocks
    mock_conn = MagicMock()
    mock_cursor = MagicMock()
    mock_conn.cursor.return_value = mock_cursor
    mock_get_db.return_value = mock_conn
    
    # Create a current time that will be used for comparison
    current_time = datetime.now(timezone.utc)
    # Mock datetime.now to return this current_time
    mock_datetime.now.return_value = current_time
    # Set up timezone.utc correctly
    mock_datetime.timezone.utc = timezone.utc
    
    # Mock user data with expired verification code
    user_id = 1
    verification_code = "123456"
    # Create an expired datetime with timezone info (5 minutes in the past)
    expires_at = current_time - timedelta(minutes=5)  # Expired
    # Ensure expires_at has tzinfo attribute for comparison
    expires_at = expires_at.replace(tzinfo=timezone.utc)
    mock_cursor.fetchone.return_value = (user_id, verification_code, expires_at)
    
    # Call the function
    email = "<EMAIL>"
    result = verify_email_code(email, verification_code)
    
    # Verify database interactions
    mock_cursor.execute.assert_called_once()
    mock_conn.commit.assert_not_called()
    mock_conn.close.assert_called_once()
    
    # Verify the function returns False for expired code
    assert result is False


@patch('app.services.email_verification_service.get_db_connection')
def test_verify_email_code_user_not_found(mock_get_db):
    """Test verification when user is not found"""
    # Setup mocks
    mock_conn = MagicMock()
    mock_cursor = MagicMock()
    mock_conn.cursor.return_value = mock_cursor
    mock_get_db.return_value = mock_conn
    
    # Mock no user found
    mock_cursor.fetchone.return_value = None
    
    # Call the function and expect an exception
    email = "<EMAIL>"
    with pytest.raises(HTTPException) as excinfo:
        verify_email_code(email, "123456")
    
    # Verify the exception details
    assert excinfo.value.status_code == 404
    assert "User not found" in excinfo.value.detail
    
    # Verify database interactions
    mock_cursor.execute.assert_called_once()
    mock_conn.commit.assert_not_called()
    mock_conn.close.assert_called_once()


@patch('app.services.email_verification_service.create_verification_code')
@patch('app.services.email_verification_service.get_db_connection')
def test_resend_verification_code_success(mock_get_db, mock_create_code):
    """Test successfully resending verification code"""
    # Setup mocks
    mock_conn = MagicMock()
    mock_cursor = MagicMock()
    mock_conn.cursor.return_value = mock_cursor
    mock_get_db.return_value = mock_conn
    
    # Mock user data with unverified email
    user_id = 1
    is_verified = False
    mock_cursor.fetchone.return_value = (user_id, is_verified)
    
    # Mock code creation
    new_code = "654321"
    mock_create_code.return_value = new_code
    
    # Call the function
    email = "<EMAIL>"
    result = resend_verification_code(email)
    
    # Verify database interactions
    mock_cursor.execute.assert_called_once()
    mock_create_code.assert_called_once_with(user_id)
    mock_conn.close.assert_called_once()
    
    # Verify the function returns the new code
    assert result == new_code


@patch('app.services.email_verification_service.get_db_connection')
def test_resend_verification_code_already_verified(mock_get_db):
    """Test resending code for already verified email"""
    # Setup mocks
    mock_conn = MagicMock()
    mock_cursor = MagicMock()
    mock_conn.cursor.return_value = mock_cursor
    mock_get_db.return_value = mock_conn
    
    # Mock user data with verified email
    user_id = 1
    is_verified = True
    mock_cursor.fetchone.return_value = (user_id, is_verified)
    
    # Call the function and expect an exception
    email = "<EMAIL>"
    with pytest.raises(HTTPException) as excinfo:
        resend_verification_code(email)
    
    # Verify the exception details
    assert excinfo.value.status_code == 400
    assert "Email is already verified" in excinfo.value.detail
    
    # Verify database interactions
    mock_cursor.execute.assert_called_once()
    mock_conn.close.assert_called_once()


@patch('app.services.email_verification_service.get_db_connection')
def test_resend_verification_code_user_not_found(mock_get_db):
    """Test resending code when user is not found"""
    # Setup mocks
    mock_conn = MagicMock()
    mock_cursor = MagicMock()
    mock_conn.cursor.return_value = mock_cursor
    mock_get_db.return_value = mock_conn
    
    # Mock no user found
    mock_cursor.fetchone.return_value = None
    
    # Call the function and expect an exception
    email = "<EMAIL>"
    with pytest.raises(HTTPException) as excinfo:
        resend_verification_code(email)
    
    # Verify the exception details
    assert excinfo.value.status_code == 404
    assert "User not found" in excinfo.value.detail
    
    # Verify database interactions
    mock_cursor.execute.assert_called_once()
    mock_conn.close.assert_called_once()


@patch('app.services.email_verification_service.email_service')
def test_send_verification_email_success(mock_email_service):
    """Test successfully sending verification email"""
    # Setup mock
    mock_email_service.send_verification_email.return_value = True
    
    # Call the function
    email = "<EMAIL>"
    code = "123456"
    result = send_verification_email(email, code)
    
    # Verify email service was called correctly
    mock_email_service.send_verification_email.assert_called_once_with(email, code, "en")
    
    # Verify the function returns True for success
    assert result is True


@patch('app.services.email_verification_service.email_service')
def test_send_verification_email_with_language(mock_email_service):
    """Test sending verification email with specific language"""
    # Setup mock
    mock_email_service.send_verification_email.return_value = True
    
    # Call the function with language
    email = "<EMAIL>"
    code = "123456"
    language = "vi"
    result = send_verification_email(email, code, language)
    
    # Verify email service was called correctly with the language
    mock_email_service.send_verification_email.assert_called_once_with(email, code, language)
    
    # Verify the function returns True for success
    assert result is True


@patch('app.services.email_verification_service.email_service')
def test_send_verification_email_failure(mock_email_service):
    """Test handling failure when sending verification email"""
    # Setup mock to raise an exception
    mock_email_service.send_verification_email.side_effect = Exception("Email sending failed")
    
    # Call the function
    email = "<EMAIL>"
    code = "123456"
    result = send_verification_email(email, code)
    
    # Verify email service was called
    mock_email_service.send_verification_email.assert_called_once()
    
    # Verify the function returns False for failure
    assert result is False
