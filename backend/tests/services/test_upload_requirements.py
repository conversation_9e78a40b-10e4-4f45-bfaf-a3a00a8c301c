import pytest
import os
import shutil
from unittest.mock import patch, MagicMock
from fastapi import UploadFile
from sqlalchemy.ext.asyncio import AsyncSession

from app.services.project_service import upload_requirements_file
from app.models.project import Project
from app.core.config import settings

pytestmark = pytest.mark.asyncio


async def test_upload_requirements_file(db_session: AsyncSession):
    """Test uploading requirements file for a project"""
    # Create a test project
    project = Project(
        name="Upload Test Project",
        user_id=1  # Assuming test user with ID 1 exists
    )
    db_session.add(project)
    await db_session.commit()
    await db_session.refresh(project)
    
    # Create a mock file
    mock_file = MagicMock(spec=UploadFile)
    mock_file.filename = "requirements.txt"
    mock_file.file = MagicMock()
    
    # Mock the file operations
    with patch("builtins.open", MagicMock()), \
         patch("os.makedirs", MagicMock()), \
         patch("shutil.copyfileobj", MagicMock()):
        
        # Upload the file
        updated_project = await upload_requirements_file(
            db_session, project.id, 1, mock_file
        )
        
        # Verify the project was updated
        assert updated_project is not None
        assert updated_project.id == project.id
        assert updated_project.requirements_file_path == f"{settings.UPLOAD_DIR}/1/{project.id}_requirements.txt"


async def test_upload_requirements_file_nonexistent_project(db_session: AsyncSession):
    """Test uploading requirements file for a nonexistent project"""
    # Create a mock file
    mock_file = MagicMock(spec=UploadFile)
    mock_file.filename = "requirements.txt"
    
    # Try to upload to a nonexistent project
    result = await upload_requirements_file(
        db_session, 9999, 1, mock_file
    )
    
    # Verify the result is None
    assert result is None
