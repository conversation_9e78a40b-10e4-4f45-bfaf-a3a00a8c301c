import pytest
from unittest.mock import patch, MagicMock, call
import threading
import queue
import time
from botocore.exceptions import ClientError

from app.services.email_service import EmailService


@patch('boto3.client')
def test_email_service_initialization(mock_boto_client):
    """Test the initialization of the EmailService class"""
    # Create a mock for the boto3 client
    mock_ses_client = MagicMock()
    mock_boto_client.return_value = mock_ses_client
    
    # Initialize the email service
    email_service = EmailService()
    
    # Verify boto3 client was created with the correct parameters
    mock_boto_client.assert_called_once()
    assert mock_boto_client.call_args[0][0] == 'ses'
    
    # Verify that the worker thread was started
    assert email_service.worker_thread.is_alive()
    assert email_service.worker_thread.daemon is True
    
    # Clean up the thread to avoid interference with other tests
    email_service.email_queue.put(None)  # Signal to stop the thread
    email_service.worker_thread.join(timeout=1.0)


@patch('boto3.client')
def test_send_verification_email_queuing(mock_boto_client):
    """Test that send_verification_email correctly queues the email task"""
    # Create a mock for the boto3 client
    mock_ses_client = MagicMock()
    mock_boto_client.return_value = mock_ses_client
    
    # Initialize the email service with a mock queue
    email_service = EmailService()
    email_service.email_queue = MagicMock(spec=queue.Queue)
    
    # Call the method to send a verification email
    result = email_service.send_verification_email(
        recipient="<EMAIL>",
        code="123456",
        language="en"
    )
    
    # Verify that the task was put in the queue
    email_service.email_queue.put.assert_called_once_with(
        ("<EMAIL>", "123456", "en")
    )
    assert result is True
    
    # Clean up the thread
    email_service.worker_thread.join(timeout=1.0)


@patch('boto3.client')
def test_send_verification_email_exception_handling(mock_boto_client):
    """Test that send_verification_email handles exceptions correctly"""
    # Create a mock for the boto3 client
    mock_ses_client = MagicMock()
    mock_boto_client.return_value = mock_ses_client
    
    # Initialize the email service with a mock queue that raises an exception
    email_service = EmailService()
    email_service.email_queue = MagicMock(spec=queue.Queue)
    email_service.email_queue.put.side_effect = Exception("Queue error")
    
    # Call the method to send a verification email
    result = email_service.send_verification_email(
        recipient="<EMAIL>",
        code="123456",
        language="en"
    )
    
    # Verify that the method returns False when an exception occurs
    assert result is False
    
    # Clean up the thread
    email_service.worker_thread.join(timeout=1.0)


@patch('boto3.client')
@patch('time.sleep')  # Mock sleep to speed up the test
def test_process_email_queue(mock_sleep, mock_boto_client):
    """Test the _process_email_queue method with a mocked queue"""
    # Create a mock for the boto3 client
    mock_ses_client = MagicMock()
    mock_boto_client.return_value = mock_ses_client
    
    # Initialize the email service
    email_service = EmailService()
    
    # Replace the _send_email_internal method with a mock
    email_service._send_email_internal = MagicMock()
    
    # Create a test queue and replace the service's queue
    test_queue = queue.Queue()
    email_service.email_queue = test_queue
    
    # Create a new thread that will process the queue
    process_thread = threading.Thread(target=email_service._process_email_queue)
    process_thread.daemon = True
    process_thread.start()
    
    # Add tasks to the queue
    test_queue.put(("<EMAIL>", "111111", "en"))
    test_queue.put(("<EMAIL>", "222222", "vi"))
    
    # Give some time for the thread to process the queue
    time.sleep(0.5)
    
    # Add None to stop the thread
    test_queue.put(None)
    process_thread.join(timeout=1.0)
    
    # Verify that _send_email_internal was called for each task
    assert email_service._send_email_internal.call_count == 2
    email_service._send_email_internal.assert_has_calls([
        call("<EMAIL>", "111111", "en"),
        call("<EMAIL>", "222222", "vi")
    ])
    
    # Clean up the original thread
    email_service.worker_thread.join(timeout=1.0)


@patch('boto3.client')
def test_send_email_internal_success(mock_boto_client):
    """Test the _send_email_internal method when successful"""
    # Create a mock for the boto3 client with a successful response
    mock_ses_client = MagicMock()
    mock_ses_client.send_email.return_value = {"MessageId": "test-message-id"}
    mock_boto_client.return_value = mock_ses_client
    
    # Initialize the email service
    email_service = EmailService()
    
    # Call the internal method directly
    response = email_service._send_email_internal(
        recipient="<EMAIL>",
        code="123456",
        language="en"
    )
    
    # Verify that the SES client was called with the correct parameters
    mock_ses_client.send_email.assert_called_once()
    call_args = mock_ses_client.send_email.call_args[1]
    
    assert call_args["Source"] == email_service.sender
    assert call_args["Destination"]["ToAddresses"] == ["<EMAIL>"]
    assert "Verify Your Email - Hapoest" in call_args["Message"]["Subject"]["Data"]
    assert "123456" in call_args["Message"]["Body"]["Html"]["Data"]
    
    # Verify the response
    assert response == {"MessageId": "test-message-id"}
    
    # Clean up the thread
    email_service.email_queue.put(None)
    email_service.worker_thread.join(timeout=1.0)


@patch('boto3.client')
def test_send_email_internal_client_error(mock_boto_client):
    """Test the _send_email_internal method when a ClientError occurs"""
    # Create a mock for the boto3 client that raises a ClientError
    mock_ses_client = MagicMock()
    mock_ses_client.send_email.side_effect = ClientError(
        {"Error": {"Message": "Test error message"}},
        "SendEmail"
    )
    mock_boto_client.return_value = mock_ses_client
    
    # Initialize the email service
    email_service = EmailService()
    
    # Call the internal method directly
    response = email_service._send_email_internal(
        recipient="<EMAIL>",
        code="123456",
        language="en"
    )
    
    # Verify that the method returns None when a ClientError occurs
    assert response is None
    
    # Clean up the thread
    email_service.email_queue.put(None)
    email_service.worker_thread.join(timeout=1.0)


@patch('boto3.client')
def test_send_email_with_different_languages(mock_boto_client):
    """Test sending emails with different language options"""
    # Create a mock for the boto3 client
    mock_ses_client = MagicMock()
    mock_boto_client.return_value = mock_ses_client
    
    # Initialize the email service
    email_service = EmailService()
    
    # Test with different languages
    languages = ["en", "vi", "ja", "fr"]  # fr is not supported, should default to en
    expected_subjects = {
        "en": "Verify Your Email - Hapoest",
        "vi": "Xác minh Email của bạn - Hapoest",
        "ja": "メールアドレスの確認 - Hapoest",
        "fr": "Verify Your Email - Hapoest"  # Should default to English
    }
    
    for lang in languages:
        # Reset the mock
        mock_ses_client.send_email.reset_mock()
        
        # Call the internal method directly
        email_service._send_email_internal(
            recipient=f"test_{lang}@example.com",
            code="123456",
            language=lang
        )
        
        # Verify that the SES client was called with the correct subject for the language
        mock_ses_client.send_email.assert_called_once()
        call_args = mock_ses_client.send_email.call_args[1]
        assert expected_subjects[lang] in call_args["Message"]["Subject"]["Data"]
    
    # Clean up the thread
    email_service.email_queue.put(None)
    email_service.worker_thread.join(timeout=1.0)
