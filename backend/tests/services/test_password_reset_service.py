import pytest
from unittest.mock import patch, Mock, AsyncMock
from datetime import datetime, timezone, timedelta
from sqlalchemy.ext.asyncio import AsyncSession

from app.services.password_reset_service import (
    generate_reset_token,
    create_password_reset_token,
    verify_reset_token,
    reset_user_password,
    cleanup_expired_tokens
)
from app.models.user import User


class TestPasswordResetService:
    
    def test_generate_reset_token(self):
        """Test reset token generation"""
        token = generate_reset_token()
        
        assert len(token) == 64
        assert isinstance(token, str)
        assert token.isalnum()  # Should only contain letters and numbers
        
        # Test that two tokens are different
        token2 = generate_reset_token()
        assert token != token2

    @pytest.mark.asyncio
    async def test_create_password_reset_token_success(self):
        """Test successful password reset token creation"""
        # Mock database session
        mock_db = AsyncMock(spec=AsyncSession)
        
        # Mock user data
        mock_user = Mock()
        mock_user.id = 1
        mock_user.email = "<EMAIL>"
        
        # Mock database queries
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = mock_user
        mock_db.execute.return_value = mock_result
        
        # Mock email service
        with patch('app.services.password_reset_service.send_password_reset_email') as mock_email:
            mock_email.return_value = True
            
            # Test the function
            token = await create_password_reset_token(mock_db, "<EMAIL>")
            
            # Assertions
            assert token is not None
            assert len(token) == 64
            mock_db.execute.assert_called()
            mock_db.commit.assert_called()
            mock_email.assert_called_once_with("<EMAIL>", token)

    @pytest.mark.asyncio
    async def test_create_password_reset_token_user_not_found(self):
        """Test password reset token creation when user doesn't exist"""
        # Mock database session
        mock_db = AsyncMock(spec=AsyncSession)
        
        # Mock empty result
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = None
        mock_db.execute.return_value = mock_result
        
        # Test the function
        token = await create_password_reset_token(mock_db, "<EMAIL>")
        
        # Assertions
        assert token is None
        mock_db.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_create_password_reset_token_email_fails(self):
        """Test password reset token creation when email sending fails"""
        # Mock database session
        mock_db = AsyncMock(spec=AsyncSession)
        
        # Mock user data
        mock_user = Mock()
        mock_user.id = 1
        mock_user.email = "<EMAIL>"
        
        # Mock database queries
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = mock_user
        mock_db.execute.return_value = mock_result
        
        # Mock email service to fail
        with patch('app.services.password_reset_service.send_password_reset_email') as mock_email:
            mock_email.return_value = False
            
            # Test the function
            token = await create_password_reset_token(mock_db, "<EMAIL>")
            
            # Assertions
            assert token is None
            # Should have called execute multiple times (find user, create token, then remove token)
            assert mock_db.execute.call_count >= 2
            assert mock_db.commit.call_count == 2
            mock_email.assert_called_once()

    @pytest.mark.asyncio
    async def test_verify_reset_token_valid(self):
        """Test verifying a valid reset token"""
        # Mock database session
        mock_db = AsyncMock(spec=AsyncSession)
        
        # Mock user data with valid token
        mock_user = Mock()
        mock_user.id = 1
        mock_user.email = "<EMAIL>"
        mock_user.reset_password_token = "valid_token"
        mock_user.reset_password_expires_at = datetime.now(timezone.utc) + timedelta(hours=1)
        
        # Mock database query
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = mock_user
        mock_db.execute.return_value = mock_result
        
        # Test the function
        user = await verify_reset_token(mock_db, "valid_token")
        
        # Assertions
        assert user == mock_user
        mock_db.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_verify_reset_token_expired(self):
        """Test verifying an expired reset token"""
        # Mock database session
        mock_db = AsyncMock(spec=AsyncSession)
        
        # Mock empty result (expired token won't be found)
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = None
        mock_db.execute.return_value = mock_result
        
        # Test the function
        user = await verify_reset_token(mock_db, "expired_token")
        
        # Assertions
        assert user is None
        mock_db.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_reset_user_password_success(self):
        """Test successful password reset"""
        # Mock database session
        mock_db = AsyncMock(spec=AsyncSession)
        
        # Mock user data
        mock_user = Mock()
        mock_user.id = 1
        mock_user.email = "<EMAIL>"
        
        # Mock verify_reset_token to return user
        with patch('app.services.password_reset_service.verify_reset_token') as mock_verify:
            mock_verify.return_value = mock_user
            
            # Mock password hashing
            with patch('app.services.password_reset_service.get_password_hash') as mock_hash:
                mock_hash.return_value = "hashed_new_password"
                
                # Test the function
                success = await reset_user_password(mock_db, "valid_token", "new_password")
                
                # Assertions
                assert success is True
                mock_verify.assert_called_once_with(mock_db, "valid_token")
                mock_hash.assert_called_once_with("new_password")
                mock_db.execute.assert_called_once()
                mock_db.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_reset_user_password_invalid_token(self):
        """Test password reset with invalid token"""
        # Mock database session
        mock_db = AsyncMock(spec=AsyncSession)
        
        # Mock verify_reset_token to return None
        with patch('app.services.password_reset_service.verify_reset_token') as mock_verify:
            mock_verify.return_value = None
            
            # Test the function
            success = await reset_user_password(mock_db, "invalid_token", "new_password")
            
            # Assertions
            assert success is False
            mock_verify.assert_called_once_with(mock_db, "invalid_token")

    @pytest.mark.asyncio
    async def test_cleanup_expired_tokens(self):
        """Test cleanup of expired tokens"""
        # Mock database session
        mock_db = AsyncMock(spec=AsyncSession)
        
        # Mock update result
        mock_result = Mock()
        mock_result.rowcount = 3  # 3 expired tokens cleaned up
        mock_db.execute.return_value = mock_result
        
        # Test the function
        count = await cleanup_expired_tokens(mock_db)
        
        # Assertions
        assert count == 3
        mock_db.execute.assert_called_once()
        mock_db.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_cleanup_expired_tokens_error(self):
        """Test cleanup when database error occurs"""
        # Mock database session
        mock_db = AsyncMock(spec=AsyncSession)
        
        # Mock database error
        mock_db.execute.side_effect = Exception("Database error")
        
        # Test the function
        count = await cleanup_expired_tokens(mock_db)
        
        # Assertions
        assert count == 0
        mock_db.execute.assert_called_once()
        mock_db.rollback.assert_called_once()

    @pytest.mark.asyncio
    async def test_create_password_reset_token_database_error(self):
        """Test password reset token creation when database error occurs"""
        # Mock database session
        mock_db = AsyncMock(spec=AsyncSession)
        
        # Mock database error
        mock_db.execute.side_effect = Exception("Database error")
        
        # Test the function
        token = await create_password_reset_token(mock_db, "<EMAIL>")
        
        # Assertions
        assert token is None
        mock_db.rollback.assert_called_once()

    @pytest.mark.asyncio
    async def test_reset_user_password_database_error(self):
        """Test password reset when database error occurs"""
        # Mock database session
        mock_db = AsyncMock(spec=AsyncSession)
        
        # Mock user data
        mock_user = Mock()
        mock_user.id = 1
        
        # Mock verify_reset_token to return user
        with patch('app.services.password_reset_service.verify_reset_token') as mock_verify:
            mock_verify.return_value = mock_user
            
            # Mock password hashing
            with patch('app.services.password_reset_service.get_password_hash') as mock_hash:
                mock_hash.return_value = "hashed_new_password"
                
                # Mock database error
                mock_db.execute.side_effect = Exception("Database error")
                
                # Test the function
                success = await reset_user_password(mock_db, "valid_token", "new_password")
                
                # Assertions
                assert success is False
                mock_db.rollback.assert_called_once() 