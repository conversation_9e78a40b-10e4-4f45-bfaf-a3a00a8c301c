import pytest
from sqlalchemy.ext.asyncio import AsyncSession

from app.services.project_service import search_projects
from app.models.project import Project
from app.models.estimate import Estimate

pytestmark = pytest.mark.asyncio


async def test_search_projects_by_name(db_session: AsyncSession):
    """Test searching projects by name"""
    # Create test projects with different names
    project1 = Project(
        name="Search Test Project",
        description="A project for testing search",
        user_id=1
    )
    
    project2 = Project(
        name="Another Project",
        description="This one doesn't have the search term",
        user_id=1
    )
    
    db_session.add_all([project1, project2])
    await db_session.commit()
    
    # Search for projects with 'search' in the name
    result = await search_projects(db_session, 1, "search", 0, 10)
    
    # Verify the result
    assert "items" in result
    assert "total" in result
    assert result["total"] >= 1
    
    # Check if our specific project is in the results
    found = False
    for item in result["items"]:
        if item["name"] == "Search Test Project":
            found = True
            break
    
    assert found, "Project with 'search' in name not found in search results"


async def test_search_projects_by_description(db_session: AsyncSession):
    """Test searching projects by description"""
    # Create a project with the search term in description
    project = Project(
        name="Description Test",
        description="This description contains searchable content",
        user_id=1
    )
    
    db_session.add(project)
    await db_session.commit()
    
    # Search for projects with 'searchable' in the description
    result = await search_projects(db_session, 1, "searchable", 0, 10)
    
    # Verify the result
    assert "items" in result
    assert "total" in result
    assert result["total"] >= 1
    
    # Check if our specific project is in the results
    found = False
    for item in result["items"]:
        if item["name"] == "Description Test":
            found = True
            break
    
    assert found, "Project with 'searchable' in description not found in search results"


async def test_search_projects_by_requirements(db_session: AsyncSession):
    """Test searching projects by requirements text"""
    # Create a project with the search term in requirements
    project = Project(
        name="Requirements Test",
        description="A test project",
        requirements_text="These are searchable requirements for testing",
        user_id=1
    )
    
    db_session.add(project)
    await db_session.commit()
    
    # Search for projects with 'searchable' in the requirements
    result = await search_projects(db_session, 1, "searchable", 0, 10)
    
    # Verify the result
    assert "items" in result
    assert "total" in result
    assert result["total"] >= 1
    
    # Check if our specific project is in the results
    found = False
    for item in result["items"]:
        if item["name"] == "Requirements Test":
            found = True
            break
    
    assert found, "Project with 'searchable' in requirements not found in search results"


async def test_search_projects_with_estimates(db_session: AsyncSession):
    """Test searching projects with estimates to check min/max calculation"""
    # Create a project
    project = Project(
        name="Estimate Search Test",
        description="A project with estimates for search testing",
        user_id=1
    )
    
    db_session.add(project)
    await db_session.commit()
    await db_session.refresh(project)
    
    # Add estimates to the project
    estimate1 = Estimate(
        name="Estimate 1",
        project_id=project.id,
        total_cost=1000.0
    )
    
    estimate2 = Estimate(
        name="Estimate 2",
        project_id=project.id,
        total_cost=2000.0
    )
    
    db_session.add_all([estimate1, estimate2])
    await db_session.commit()
    
    # Search for the project
    result = await search_projects(db_session, 1, "estimate", 0, 10)
    
    # Verify the result
    assert "items" in result
    assert "total" in result
    assert result["total"] >= 1
    
    # Find our project in the results
    project_result = None
    for item in result["items"]:
        if item["name"] == "Estimate Search Test":
            project_result = item
            break
    
    assert project_result is not None, "Project not found in search results"
    
    # Check estimate min/max values
    assert project_result["estimate_min"] == 1000.0
    assert project_result["estimate_max"] == 2000.0


async def test_search_projects_empty_term(db_session: AsyncSession):
    """Test searching projects with empty search term"""
    # Create a project
    project = Project(
        name="Empty Search Test",
        user_id=1
    )
    
    db_session.add(project)
    await db_session.commit()
    
    # Search with empty term (should return all projects)
    result = await search_projects(db_session, 1, "", 0, 10)
    
    # Verify the result
    assert "items" in result
    assert "total" in result
    assert result["total"] >= 1
    
    # Check pagination metadata
    assert "page" in result
    assert "pages" in result
    assert "size" in result
    assert result["page"] == 1
    assert result["size"] == 10
