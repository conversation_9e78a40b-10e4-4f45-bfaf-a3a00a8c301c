import pytest
from unittest.mock import patch, MagicMock, ANY
import json
import os
import re
from io import BytesIO
from datetime import datetime
from fastapi import HTTPException

from app.services.ai_service import (
    _fix_common_json_errors,
    generate_project_estimate,
    create_pdf_report
)

@pytest.fixture
def mock_gemini_response():
    """Mock response from Gemini AI"""
    return {
        "development_items": [
            {"category": "Backend", "name": "User Authentication", "effort": 3},
            {"category": "Frontend", "name": "UI Components", "effort": 4},
            {"category": "Testing", "name": "Unit Tests", "effort": 2}
        ],
        "summary_items": [
            {"category": "Development", "effort": 7},
            {"category": "Testing", "effort": 2},
            {"category": "Project Management", "effort": 1}
        ],
        "grand_total": 10
    }

def test_fix_common_json_errors():
    """Test the JSON error fixing function"""
    # Test fixing missing quotes around keys
    json_with_unquoted_keys = '{name: "Test", value: 123}'
    fixed_json = _fix_common_json_errors(json_with_unquoted_keys)
    parsed_json = json.loads(fixed_json)
    assert parsed_json["name"] == "Test"
    assert parsed_json["value"] == 123
    
    # Test fixing single quotes
    json_with_single_quotes = "{'name': 'Test', 'value': 123}"
    fixed_json = _fix_common_json_errors(json_with_single_quotes)
    parsed_json = json.loads(fixed_json)
    assert parsed_json["name"] == "Test"
    assert parsed_json["value"] == 123
    
    # Test fixing Python boolean values
    json_with_python_booleans = '{"active": True, "verified": False}'
    fixed_json = _fix_common_json_errors(json_with_python_booleans)
    parsed_json = json.loads(fixed_json)
    assert parsed_json["active"] is True
    assert parsed_json["verified"] is False
    
    # Test fixing None to null
    json_with_none = '{"value": None}'
    fixed_json = _fix_common_json_errors(json_with_none)
    parsed_json = json.loads(fixed_json)
    assert parsed_json["value"] is None

@pytest.mark.asyncio
@patch("google.generativeai.GenerativeModel")
async def test_generate_project_estimate(mock_generative_model, mock_gemini_response):
    """Test generating a project estimate using the AI service"""
    # Setup mock
    mock_model_instance = MagicMock()
    mock_generative_model.return_value = mock_model_instance
    
    mock_response = MagicMock()
    mock_response.text = json.dumps(mock_gemini_response)
    mock_model_instance.generate_content.return_value = mock_response
    
    # Test function with sufficiently long requirements (over 50 chars)
    requirements = "Create a web application with user authentication, user profile management, " \
                  "product listing, shopping cart, and payment processing. The application should " \
                  "have responsive design and support multiple languages."
    result = await generate_project_estimate(requirements, "Web Application", "React, FastAPI")
    
    # Assertions
    assert mock_model_instance.generate_content.called
    assert "development_items" in result
    assert "summary_items" in result
    assert "grand_total" in result
    assert result["grand_total"] == 10
    assert len(result["development_items"]) == 3
    assert len(result["summary_items"]) == 3
    
    # Verify the prompt includes the requirements, software type, and technology stack
    call_args = mock_model_instance.generate_content.call_args[0][0]
    prompt_text = str(call_args)
    assert requirements in prompt_text
    assert "Web Application" in prompt_text
    assert "React, FastAPI" in prompt_text

@pytest.mark.asyncio
async def test_generate_project_estimate_validation():
    """Test validation in generate_project_estimate function"""
    # Test with empty requirements
    with pytest.raises(ValueError, match="The project requirements are too short"):
        await generate_project_estimate("")
    
    # Test with very short requirements
    with pytest.raises(ValueError, match="The project requirements are too short"):
        await generate_project_estimate("Short requirement")
        
    # Test with None requirements
    with pytest.raises(ValueError, match="The project requirements are too short"):
        await generate_project_estimate(None)

@pytest.mark.asyncio
@patch("google.generativeai.GenerativeModel")
async def test_generate_project_estimate_api_error(mock_generative_model):
    """Test handling API errors in generate_project_estimate function"""
    # Setup mock to raise an exception
    mock_model_instance = MagicMock()
    mock_generative_model.return_value = mock_model_instance
    mock_model_instance.generate_content.side_effect = Exception("API Error")
    
    # Test with valid requirements but API error
    requirements = "This is a sufficiently long requirement text that should pass validation " \
                  "but will trigger an API error when sent to the model."
    
    # Không sử dụng match regex để tránh treo test
    with pytest.raises(Exception):
        await generate_project_estimate(requirements, "Web Application", "Python, React")


@pytest.mark.asyncio
@patch("google.generativeai.GenerativeModel")
async def test_generate_project_estimate_malformed_response(mock_generative_model):
    """Test handling malformed responses in generate_project_estimate function"""
    # Setup mock with invalid JSON response
    mock_model_instance = MagicMock()
    mock_generative_model.return_value = mock_model_instance
    
    mock_response = MagicMock()
    mock_response.text = "This is not valid JSON and should cause an error"
    mock_model_instance.generate_content.return_value = mock_response
    
    # Test with valid requirements but malformed response
    requirements = "This is a sufficiently long requirement text that should pass validation " \
                  "but will receive a malformed response."
    
    # Không sử dụng match regex để tránh treo test
    with pytest.raises(Exception):
        await generate_project_estimate(requirements, "Web Application", "Python, React")


@pytest.mark.asyncio
@patch("app.services.ai_service.SimpleDocTemplate")
async def test_create_pdf_report(mock_simple_doc_template):
    """Test creating a PDF report"""
    # Setup mocks
    mock_doc_instance = MagicMock()
    mock_simple_doc_template.return_value = mock_doc_instance
    
    # Test data
    estimate_data = {
        "development_items": [
            {"feature": "User Authentication", "description": "Login and registration", "backend_effort": 3.0, "frontend_effort": 2.0, "unit_test_effort": 1.0, "priority": "High", "is_selected": True},
            {"feature": "UI Components", "description": "Reusable components", "backend_effort": 1.0, "frontend_effort": 4.0, "unit_test_effort": 1.0, "priority": "Medium", "is_selected": True}
        ],
        "summary_items": [
            {"category": "Development", "effort": 7.0, "description": "Backend and frontend", "role": "Developer", "day_rate": 500.0, "cost": 3500.0, "is_selected": True},
            {"category": "Testing", "effort": 2.0, "description": "QA testing", "role": "Tester", "day_rate": 400.0, "cost": 800.0, "is_selected": True}
        ],
        "grand_total": 9.0,
        "total_cost": 4300.0,
        "day_rate": 500.0,
        "currency": "USD"
    }
    
    # Test function
    pdf_buffer = await create_pdf_report(estimate_data, "Test Project", "en")
    
    # Assertions
    assert mock_simple_doc_template.called
    assert mock_doc_instance.build.called
    assert isinstance(pdf_buffer, BytesIO)


@pytest.mark.asyncio
@patch("app.services.ai_service.SimpleDocTemplate")
async def test_create_pdf_report_with_different_languages(mock_simple_doc_template):
    """Test creating a PDF report with different languages"""
    # Setup mocks
    mock_doc_instance = MagicMock()
    mock_simple_doc_template.return_value = mock_doc_instance
    
    # Test data
    estimate_data = {
        "development_items": [
            {"feature": "User Authentication", "description": "Login and registration", "backend_effort": 3.0, "frontend_effort": 2.0, "unit_test_effort": 1.0, "priority": "High", "is_selected": True}
        ],
        "summary_items": [
            {"category": "Development", "effort": 5.0, "description": "Backend and frontend", "role": "Developer", "day_rate": 500.0, "cost": 2500.0, "is_selected": True}
        ],
        "grand_total": 5.0,
        "total_cost": 2500.0,
        "day_rate": 500.0,
        "currency": "USD"
    }
    
    # Test with different languages
    languages = ["en", "vi", "ja"]
    for language in languages:
        # Reset mock for each language test
        mock_doc_instance.reset_mock()
        
        # Create PDF with the current language
        pdf_buffer = await create_pdf_report(estimate_data, "Test Project", language)
        
        # Assertions
        assert mock_simple_doc_template.called
        assert mock_doc_instance.build.called
        assert isinstance(pdf_buffer, BytesIO)


@pytest.mark.asyncio
@patch("app.services.ai_service.SimpleDocTemplate")
async def test_create_pdf_report_with_different_currencies(mock_simple_doc_template):
    """Test creating a PDF report with different currencies"""
    # Setup mocks
    mock_doc_instance = MagicMock()
    mock_simple_doc_template.return_value = mock_doc_instance
    
    # Test with different currencies
    currencies = ["USD", "EUR", "JPY", "GBP", "VND"]
    
    for currency in currencies:
        # Reset mock for each currency test
        mock_doc_instance.reset_mock()
        
        # Test data with current currency
        estimate_data = {
            "development_items": [
                {"feature": "Feature 1", "description": "Description 1", "backend_effort": 1.0, "frontend_effort": 1.0, "unit_test_effort": 0.5, "priority": "High", "is_selected": True}
            ],
            "summary_items": [
                {"category": "Development", "effort": 2.5, "description": "Dev work", "role": "Developer", "day_rate": 500.0, "cost": 1250.0, "is_selected": True}
            ],
            "grand_total": 2.5,
            "total_cost": 1250.0,
            "day_rate": 500.0,
            "currency": currency
        }
        
        # Create PDF with the current currency
        pdf_buffer = await create_pdf_report(estimate_data, "Test Project", "en")
        
        # Assertions
        assert mock_simple_doc_template.called
        assert mock_doc_instance.build.called
        assert isinstance(pdf_buffer, BytesIO)


@pytest.mark.asyncio
@patch("app.services.ai_service.SimpleDocTemplate")
async def test_create_pdf_report_error_handling(mock_simple_doc_template):
    """Test error handling in PDF report creation"""
    # Setup mock to raise an exception during build
    mock_doc_instance = MagicMock()
    mock_doc_instance.build.side_effect = Exception("PDF generation error")
    mock_simple_doc_template.return_value = mock_doc_instance
    
    # Test data
    estimate_data = {
        "development_items": [
            {"feature": "Feature 1", "description": "Description 1", "backend_effort": 1.0, "frontend_effort": 1.0, "unit_test_effort": 0.5, "priority": "High", "is_selected": True}
        ],
        "summary_items": [
            {"category": "Development", "effort": 2.5, "description": "Dev work", "role": "Developer", "day_rate": 500.0, "cost": 1250.0, "is_selected": True}
        ],
        "grand_total": 2.5,
        "total_cost": 1250.0,
        "day_rate": 500.0,
        "currency": "USD"
    }
    
    # Test function and expect an exception
    with pytest.raises(ValueError, match="Error generating PDF"):
        await create_pdf_report(estimate_data, "Test Project", "en")
        
    # Verify the mock was called
    assert mock_simple_doc_template.called
    assert mock_doc_instance.build.called
