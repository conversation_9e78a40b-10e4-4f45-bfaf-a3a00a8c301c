import pytest
from sqlalchemy.ext.asyncio import AsyncSession

from app.services.project_service import get_project
from app.models.project import Project

pytestmark = pytest.mark.asyncio


async def test_get_project_existing(db_session: AsyncSession):
    """Test getting a project by ID without user check"""
    # Create a test project
    project = Project(
        name="Get Project Test",
        description="Testing get_project function",
        user_id=1,
        software_type="Web App",
        technology_stack="Python, React"
    )
    db_session.add(project)
    await db_session.commit()
    await db_session.refresh(project)
    
    # Get the project
    result = await get_project(db_session, project.id)
    
    # Verify the result
    assert result is not None
    assert result["id"] == project.id
    assert result["name"] == "Get Project Test"
    assert result["description"] == "Testing get_project function"
    assert result["user_id"] == 1
    assert result["software_type"] == "Web App"
    assert result["technology_stack"] == "Python, React"
    assert result["requirements_text"] is None
    assert result["requirements_file_path"] is None
    assert result["created_at"] is not None
    assert result["updated_at"] is None


async def test_get_project_nonexistent(db_session: AsyncSession):
    """Test getting a nonexistent project"""
    # Try to get a nonexistent project
    result = await get_project(db_session, 9999)
    
    # Verify the result is None
    assert result is None
