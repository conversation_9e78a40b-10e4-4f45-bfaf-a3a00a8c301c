import pytest
from pytest_mock import MockFixture
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from app.models.user import User, UserSettings
from app.core.deps import get_current_user
from app.services.auth_service import verify_password, get_password_hash

pytestmark = pytest.mark.asyncio

async def test_verify_password():
    """Test xác thực mật khẩu"""
    # Tạo hash mới từ một mật khẩu biết trước
    plain_password = "testpassword123"
    hashed_password = get_password_hash(plain_password)
    
    # Kiểm tra xác thực đúng
    assert verify_password(plain_password, hashed_password) is True
    
    # Kiểm tra xác thực sai
    assert verify_password("wrongpassword", hashed_password) is False

async def test_get_password_hash():
    """Test tạo hash từ mật khẩu"""
    password = "mypassword"
    hashed = get_password_hash(password)
    
    # Kiểm tra hash có đúng format
    assert hashed.startswith("$2b$")
    
    # <PERSON><PERSON><PERSON> tra có thể xác thực được với mật khẩu gốc
    assert verify_password(password, hashed) is True
    
    # Kiểm tra mật khẩu khác không xác thực được
    assert verify_password("wrongpassword", hashed) is False

async def test_get_current_user(db_session: AsyncSession, mocker: MockFixture):
    """Test hàm get_current_user"""
    # Tạo user test
    user = User(
        email="<EMAIL>", 
        hashed_password=get_password_hash("password"),
        is_active=True
    )
    db_session.add(user)
    await db_session.commit()
    await db_session.refresh(user)
    
    # Mock jwt.decode thay vì decode_token
    mocker.patch("jose.jwt.decode", return_value={"sub": str(user.id)})
    
    # Gọi hàm get_current_user
    current_user = await get_current_user(db_session, "fake_token")
    
    # Kiểm tra kết quả
    assert current_user.id == user.id
    assert current_user.email == "<EMAIL>"

async def test_get_user_by_email(db_session: AsyncSession):
    """Test lấy user theo email"""
    # Tạo user test
    email = "<EMAIL>"
    user = User(
        email=email,
        hashed_password="hashed_password",
        is_active=True
    )
    db_session.add(user)
    await db_session.commit()
    
    # Truy vấn user theo email
    stmt = select(User).where(User.email == email)
    result = await db_session.execute(stmt)
    found_user = result.scalars().first()
    
    # Kiểm tra kết quả
    assert found_user is not None
    assert found_user.email == email 