import pytest
from unittest.mock import patch, MagicMock
from sqlalchemy.ext.asyncio import AsyncSession
from datetime import datetime

from app.models.user import User
from app.models.project import Project
from app.models.estimate import Estimate
from app.services.estimate_service import (
    create_estimate,
    get_estimates_by_project,
    get_estimate_by_id,
    update_estimate,
    delete_estimate,
    generate_estimate_from_requirements
)


pytestmark = pytest.mark.asyncio


async def create_test_project(db_session: AsyncSession, user_id: int = 1) -> Project:
    """Helper function to create a test project"""
    # Create a project for testing
    project = Project(
        name="Test Project for Estimates",
        description="A project for testing estimate services",
        user_id=user_id,
        software_type="Web Application",
        technology_stack="Python, React",
        requirements_text="Create a web application with user authentication, user profile management, product listing, and payment processing."
    )
    db_session.add(project)
    await db_session.commit()
    await db_session.refresh(project)
    return project


async def test_create_estimate_service(db_session: AsyncSession):
    """Test creating an estimate using the service function"""
    # Create a test project first
    project = await create_test_project(db_session)
    
    # Prepare estimate data
    estimate_data = {
        "development_items": [
            {
                "feature": "User Authentication",
                "description": "Login and registration",
                "backend_effort": 3.0,
                "frontend_effort": 2.0,
                "unit_test_effort": 1.0,
                "priority": "High",
                "is_selected": True
            },
            {
                "feature": "Product Listing",
                "description": "Display products with filtering",
                "backend_effort": 2.0,
                "frontend_effort": 3.0,
                "unit_test_effort": 1.0,
                "priority": "Medium",
                "is_selected": True
            }
        ],
        "summary_items": [
            {
                "category": "Development",
                "effort": 5.0,
                "description": "Backend and frontend",
                "role": "Developer",
                "day_rate": 500.0,
                "cost": 2500.0,
                "is_selected": True
            },
            {
                "category": "Testing",
                "effort": 2.0,
                "description": "QA testing",
                "role": "Tester",
                "day_rate": 400.0,
                "cost": 800.0,
                "is_selected": True
            }
        ],
        "grand_total": 7.0,
        "total_cost": 3300.0,
        "day_rate": 500.0,
        "currency": "USD"
    }
    
    # Create the estimate
    estimate = await create_estimate(db_session, project.id, estimate_data)
    
    # Verify the estimate was created correctly
    assert estimate is not None
    assert estimate.project_id == project.id
    assert estimate.development_items == estimate_data["development_items"]
    assert estimate.summary_items == estimate_data["summary_items"]
    assert estimate.grand_total == estimate_data["grand_total"]
    assert estimate.total_cost == estimate_data["total_cost"]
    assert estimate.day_rate == estimate_data["day_rate"]
    assert estimate.currency == estimate_data["currency"]
    assert estimate.is_active is True
    
    return estimate.id


async def test_get_estimates_by_project_service(db_session: AsyncSession):
    """Test getting estimates for a project using the service function"""
    # Create a test project
    project = await create_test_project(db_session)
    
    # Create multiple estimates for the project
    estimate_data_1 = {
        "development_items": [
            {"feature": "Feature 1", "description": "Description 1", "backend_effort": 1.0, "frontend_effort": 1.0, "unit_test_effort": 0.5, "priority": "High", "is_selected": True}
        ],
        "summary_items": [
            {"category": "Development", "effort": 2.5, "description": "Dev work", "role": "Developer", "day_rate": 500.0, "cost": 1250.0, "is_selected": True}
        ],
        "grand_total": 2.5,
        "total_cost": 1250.0,
        "day_rate": 500.0
    }
    
    estimate_data_2 = {
        "development_items": [
            {"feature": "Feature 2", "description": "Description 2", "backend_effort": 2.0, "frontend_effort": 2.0, "unit_test_effort": 1.0, "priority": "Medium", "is_selected": True}
        ],
        "summary_items": [
            {"category": "Development", "effort": 5.0, "description": "Dev work", "role": "Developer", "day_rate": 500.0, "cost": 2500.0, "is_selected": True}
        ],
        "grand_total": 5.0,
        "total_cost": 2500.0,
        "day_rate": 500.0
    }
    
    await create_estimate(db_session, project.id, estimate_data_1)
    await create_estimate(db_session, project.id, estimate_data_2)
    
    # Get estimates for the project
    estimates = await get_estimates_by_project(db_session, project.id)
    
    # Verify the estimates were retrieved correctly
    assert len(estimates) >= 2  # At least the two we just created
    assert isinstance(estimates, list)
    assert all(isinstance(estimate, dict) for estimate in estimates)
    
    # Verify the structure of the returned estimates
    for estimate in estimates:
        assert "id" in estimate
        assert "name" in estimate
        assert "project_id" in estimate
        assert "development_items" in estimate
        assert "summary_items" in estimate
        assert "grand_total" in estimate
        assert "total_cost" in estimate
        assert "day_rate" in estimate
        assert "currency" in estimate
        assert "is_active" in estimate
        assert "created_at" in estimate
        assert estimate["project_id"] == project.id


async def test_get_estimate_by_id_service(db_session: AsyncSession):
    """Test getting an estimate by ID using the service function"""
    # Create a test estimate
    estimate_id = await test_create_estimate_service(db_session)
    
    # Get the estimate by ID
    estimate = await get_estimate_by_id(db_session, estimate_id)
    
    # Verify the estimate was retrieved correctly
    assert estimate is not None
    assert isinstance(estimate, dict)
    assert estimate["id"] == estimate_id
    assert "name" in estimate
    assert "project_id" in estimate
    assert "development_items" in estimate
    assert "summary_items" in estimate
    assert "grand_total" in estimate
    assert "total_cost" in estimate
    assert "day_rate" in estimate
    assert "currency" in estimate
    assert "is_active" in estimate
    assert "created_at" in estimate


async def test_update_estimate_service(db_session: AsyncSession):
    """Test updating an estimate using the service function"""
    # Create a test estimate
    estimate_id = await test_create_estimate_service(db_session)
    
    # Prepare update data
    update_data = {
        "name": "Updated Estimate Name",
        "grand_total": 10.0,
        "total_cost": 5000.0,
        "day_rate": 600.0,
        "currency": "EUR"
    }
    
    # Update the estimate
    updated_estimate = await update_estimate(db_session, estimate_id, update_data)
    
    # Verify the estimate was updated correctly
    assert updated_estimate is not None
    assert isinstance(updated_estimate, dict)
    assert updated_estimate["id"] == estimate_id
    assert updated_estimate["name"] == "Updated Estimate Name"
    assert updated_estimate["grand_total"] == 10.0
    assert updated_estimate["total_cost"] == 5000.0
    assert updated_estimate["day_rate"] == 600.0
    assert updated_estimate["currency"] == "EUR"
    
    # Verify the update by getting the estimate again
    estimate = await get_estimate_by_id(db_session, estimate_id)
    assert estimate["name"] == "Updated Estimate Name"


async def test_delete_estimate_service(db_session: AsyncSession):
    """Test deleting an estimate using the service function"""
    # Create a test estimate
    estimate_id = await test_create_estimate_service(db_session)
    
    # Delete the estimate
    result = await delete_estimate(db_session, estimate_id)
    
    # Verify the estimate was deleted
    assert result is True
    
    # Verify the estimate no longer exists
    estimate = await get_estimate_by_id(db_session, estimate_id)
    assert estimate is None


@patch("app.services.estimate_service.generate_project_estimate")
async def test_generate_estimate_from_requirements_service(mock_generate_project_estimate, db_session: AsyncSession):
    """Test generating an estimate from requirements using the service function"""
    # Create a test user
    user = User(
        email="<EMAIL>",
        hashed_password="hashed_password",
        is_active=True,
        is_verified=True
    )
    db_session.add(user)
    await db_session.commit()
    await db_session.refresh(user)
    
    # Create a test project with requirements
    project = await create_test_project(db_session, user.id)
    
    # Mock the AI service response
    mock_ai_response = {
        "development_items": [
            {"feature": "User Authentication", "description": "Login and registration", "backend_effort": 3.0, "frontend_effort": 2.0, "unit_test_effort": 1.0, "priority": "High", "is_selected": True},
            {"feature": "Product Listing", "description": "Display products with filtering", "backend_effort": 2.0, "frontend_effort": 3.0, "unit_test_effort": 1.0, "priority": "Medium", "is_selected": True}
        ],
        "summary_items": [
            {"category": "Development", "effort": 5.0, "description": "Backend and frontend", "role": "Developer", "day_rate": 500.0, "cost": 2500.0, "is_selected": True},
            {"category": "Testing", "effort": 2.0, "description": "QA testing", "role": "Tester", "day_rate": 400.0, "cost": 800.0, "is_selected": True}
        ],
        "grand_total": 7.0,
        "total_cost": 3300.0,
        "day_rate": 500.0,
        "currency": "USD"
    }
    mock_generate_project_estimate.return_value = mock_ai_response
    
    # Generate the estimate
    generated_estimate = await generate_estimate_from_requirements(db_session, project.id, user.id)
    
    # Verify the estimate was generated correctly
    assert generated_estimate is not None
    assert isinstance(generated_estimate, dict)
    assert "id" in generated_estimate
    assert "name" in generated_estimate
    assert "project_id" in generated_estimate
    assert "development_items" in generated_estimate
    assert "summary_items" in generated_estimate
    assert "grand_total" in generated_estimate
    assert "total_cost" in generated_estimate
    assert "day_rate" in generated_estimate
    assert "currency" in generated_estimate
    assert "is_active" in generated_estimate
    assert "created_at" in generated_estimate
    assert generated_estimate["project_id"] == project.id
    assert generated_estimate["grand_total"] == 7.0
    assert generated_estimate["total_cost"] == 3300.0
    assert generated_estimate["day_rate"] == 500.0
    assert generated_estimate["currency"] == "USD"
    
    # Verify the AI service was called with the correct parameters
    mock_generate_project_estimate.assert_called_once_with(
        requirements=project.requirements_text,
        software_type=project.software_type,
        technology_stack=project.technology_stack
    )


async def test_generate_estimate_from_requirements_no_project(db_session: AsyncSession):
    """Test generating an estimate for a non-existent project"""
    # Try to generate an estimate for a non-existent project
    generated_estimate = await generate_estimate_from_requirements(db_session, 9999, 1)
    
    # Verify no estimate was generated
    assert generated_estimate is None


async def test_generate_estimate_from_requirements_no_requirements(db_session: AsyncSession):
    """Test generating an estimate for a project with no requirements"""
    # Create a test user
    user = User(
        email="<EMAIL>",
        hashed_password="hashed_password",
        is_active=True,
        is_verified=True
    )
    db_session.add(user)
    await db_session.commit()
    await db_session.refresh(user)
    
    # Create a project with no requirements
    project = Project(
        name="Project Without Requirements",
        description="A project with no requirements",
        user_id=user.id,
        software_type="Web Application",
        technology_stack="Python, React"
        # No requirements_text
    )
    db_session.add(project)
    await db_session.commit()
    await db_session.refresh(project)
    
    # Try to generate an estimate
    generated_estimate = await generate_estimate_from_requirements(db_session, project.id, user.id)
    
    # Verify no estimate was generated
    assert generated_estimate is None

@pytest.mark.skip(reason="Issues with AI service mocking")
async def test_generate_estimate_from_requirements_exception(db_session: AsyncSession):
    """Test generating an estimate when the AI service raises an exception"""
    # Create a test user
    user = User(
        email="<EMAIL>",
        hashed_password="hashed_password",
        is_active=True,
        is_verified=True
    )
    db_session.add(user)
    await db_session.commit()
    await db_session.refresh(user)
    
    # Create a test project with requirements
    project = Project(
        name="AI Error Test Project",
        description="A project for testing AI service errors",
        user_id=user.id,
        software_type="Web Application",
        technology_stack="Python, React",
        requirements_text="Create a web application with user authentication."
    )
    db_session.add(project)
    await db_session.commit()
    await db_session.refresh(project)
    
    # Mock the database query to return our project
    mock_result = MagicMock()
    mock_result.scalars().first.return_value = project
    
    with patch.object(db_session, 'execute', return_value=mock_result):
        # Mock the AI service to raise an exception
        with patch('app.services.ai_service.generate_project_estimate', side_effect=Exception("AI service error")):
            # Try to generate the estimate
            generated_estimate = await generate_estimate_from_requirements(db_session, project.id, user.id)
            
            # Verify no estimate was generated due to the exception
            assert generated_estimate is None


async def test_get_estimate_by_id_nonexistent(db_session: AsyncSession):
    """Test getting a nonexistent estimate by ID"""
    from app.services.estimate_service import get_estimate_by_id
    
    # Try to get a nonexistent estimate
    result = await get_estimate_by_id(db_session, 9999)
    
    # Verify the result is None
    assert result is None


async def test_update_estimate_nonexistent(db_session: AsyncSession):
    """Test updating a nonexistent estimate"""
    from app.services.estimate_service import update_estimate
    
    # Try to update a nonexistent estimate
    update_data = {"name": "Updated Name"}
    result = await update_estimate(db_session, 9999, update_data)
    
    # Verify the result is None
    assert result is None


async def test_delete_estimate_nonexistent(db_session: AsyncSession):
    """Test deleting a nonexistent estimate"""
    from app.services.estimate_service import delete_estimate
    
    # Try to delete a nonexistent estimate
    result = await delete_estimate(db_session, 9999)
    
    # Verify the result is False
    assert result is False
