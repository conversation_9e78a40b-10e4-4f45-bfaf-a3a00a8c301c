"""add updated_at column to users table

Revision ID: 3612ba02b5e5
Revises: 
Create Date: 2025-05-05 17:19:35.830806

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '3612ba02b5e5'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('estimates', 'name',
               existing_type=sa.VARCHAR(),
               nullable=True)
    op.alter_column('estimates', 'project_id',
               existing_type=sa.INTEGER(),
               nullable=True)
    op.alter_column('estimates', 'development_items',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               type_=sa.JSON(),
               existing_nullable=True)
    op.alter_column('estimates', 'summary_items',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               type_=sa.JSON(),
               existing_nullable=True)
    op.create_index(op.f('ix_estimates_id'), 'estimates', ['id'], unique=False)
    op.create_index(op.f('ix_estimates_name'), 'estimates', ['name'], unique=False)
    op.drop_constraint('estimates_project_id_fkey', 'estimates', type_='foreignkey')
    op.create_foreign_key(None, 'estimates', 'projects', ['project_id'], ['id'])
    op.add_column('users', sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True))
    op.add_column('users', sa.Column('is_verified', sa.Boolean(), nullable=True))
    op.add_column('users', sa.Column('verification_code', sa.String(), nullable=True))
    op.add_column('users', sa.Column('verification_code_expires_at', sa.DateTime(timezone=True), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('users', 'verification_code_expires_at')
    op.drop_column('users', 'verification_code')
    op.drop_column('users', 'is_verified')
    op.drop_column('users', 'updated_at')
    op.drop_constraint(None, 'estimates', type_='foreignkey')
    op.create_foreign_key('estimates_project_id_fkey', 'estimates', 'projects', ['project_id'], ['id'], ondelete='CASCADE')
    op.drop_index(op.f('ix_estimates_name'), table_name='estimates')
    op.drop_index(op.f('ix_estimates_id'), table_name='estimates')
    op.alter_column('estimates', 'summary_items',
               existing_type=sa.JSON(),
               type_=postgresql.JSONB(astext_type=sa.Text()),
               existing_nullable=True)
    op.alter_column('estimates', 'development_items',
               existing_type=sa.JSON(),
               type_=postgresql.JSONB(astext_type=sa.Text()),
               existing_nullable=True)
    op.alter_column('estimates', 'project_id',
               existing_type=sa.INTEGER(),
               nullable=False)
    op.alter_column('estimates', 'name',
               existing_type=sa.VARCHAR(),
               nullable=False)
    # ### end Alembic commands ###
