"""Add password reset fields to users table

Revision ID: ead660066203
Revises: 3612ba02b5e5
Create Date: 2025-06-04 15:27:05.790298

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'ead660066203'
down_revision: Union[str, None] = '3612ba02b5e5'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('users', sa.Column('reset_password_token', sa.String(), nullable=True))
    op.add_column('users', sa.Column('reset_password_expires_at', sa.DateTime(timezone=True), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('users', 'reset_password_expires_at')
    op.drop_column('users', 'reset_password_token')
    # ### end Alembic commands ###
