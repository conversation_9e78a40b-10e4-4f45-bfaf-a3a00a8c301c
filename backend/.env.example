# Database
DATABASE_URL=postgresql+asyncpg://postgres:postgres@localhost:5432/hapoest
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
POSTGRES_DB=hapoest

# JWT Authentication
SECRET_KEY=hapoest-secret-key-for-development
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=10080

# Google OAuth
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# Google Gemini API
GOOGLE_API_KEY=

# Frontend
NEXT_PUBLIC_API_URL=http://localhost:8000/api/v1

# AWS Bedrock credentials
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_REGION=us-east-1 