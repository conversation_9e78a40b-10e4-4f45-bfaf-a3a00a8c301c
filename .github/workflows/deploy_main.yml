name: Deploy Demo

on:
  push:
    branches: [main]
  workflow_dispatch:

concurrency: 
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

env:
  DEPLOY_PATH: /var/www/demo

jobs:
  build-frontend:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      - name: Cache Docker layers for frontend
        uses: actions/cache@v4
        with:
          path: /tmp/.buildx-cache-frontend
          key: ${{ runner.os }}-buildx-frontend-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-buildx-frontend-
      - name: Build Frontend Docker image
        uses: docker/build-push-action@v6
        with:
          context: ./frontend
          platforms: linux/amd64
          push: false
          build-args: |
            NEXT_PUBLIC_API_URL=https://estimate.haposoft.com/api/v1
            NEXT_PUBLIC_GOOGLE_CLIENT_ID=722197383084-vsp58s62vrj1md0atq539qunn28a184b.apps.googleusercontent.com
          tags: hapoest-demo-frontend:latest
          outputs: type=docker,dest=frontend.tar
          cache-from: type=local,src=/tmp/.buildx-cache-frontend
          cache-to: type=local,dest=/tmp/.buildx-cache-frontend-new,mode=max
      - name: Move frontend cache
        run: |
          rm -rf /tmp/.buildx-cache-frontend
          mv /tmp/.buildx-cache-frontend-new /tmp/.buildx-cache-frontend
      - name: Save build artifact
        uses: actions/upload-artifact@v4
        with:
          name: build-artifact-frontend
          path: frontend.tar
          retention-days: 1

  build-backend:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      - name: Cache Docker layers for backend
        uses: actions/cache@v4
        with:
          path: /tmp/.buildx-cache-backend
          key: ${{ runner.os }}-buildx-backend-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-buildx-backend-
      - name: Build Backend Docker image
        uses: docker/build-push-action@v6
        with:
          context: ./backend
          platforms: linux/amd64
          push: false
          tags: hapoest-demo-backend:latest
          outputs: type=docker,dest=backend.tar
          cache-from: type=local,src=/tmp/.buildx-cache-backend
          cache-to: type=local,dest=/tmp/.buildx-cache-backend-new,mode=max
      - name: Move backend cache
        run: |
          rm -rf /tmp/.buildx-cache-backend
          mv /tmp/.buildx-cache-backend-new /tmp/.buildx-cache-backend
      - name: Save build artifact
        uses: actions/upload-artifact@v4
        with:
          name: build-artifact-backend
          path: backend.tar
          retention-days: 1

  deploy:
    runs-on: ubuntu-latest
    needs: [build-frontend, build-backend]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Download frontend build artifacts
        uses: actions/download-artifact@v4
        with:
          name: build-artifact-frontend
          path: .
      - name: Download backend build artifacts
        uses: actions/download-artifact@v4
        with:
          name: build-artifact-backend
          path: .
      - name: Deploy to server
        uses: easingthemes/ssh-deploy@main
        with:
          SSH_PRIVATE_KEY: ${{ secrets.SSH_PRIVATE_KEY_PROD }}
          REMOTE_HOST: ${{ secrets.REMOTE_HOST_PROD }}
          REMOTE_USER: ${{ secrets.REMOTE_USER_PROD }}
          ARGS: "-vW --no-compress"
          SOURCE: "docker-compose.demo.yml frontend.tar backend.tar"
          TARGET: ${{ env.DEPLOY_PATH }}
          SCRIPT_AFTER: |
            cd "${{ env.DEPLOY_PATH }}"
            docker load -i frontend.tar
            docker load -i backend.tar
            rm frontend.tar backend.tar
            docker compose -f docker-compose.demo.yml -p hapoest-demo up -d
            docker image prune -f || true
      - name: Google Chat Notification
        run: |
          APP_URL="https://estimate.haposoft.com"
          GOOGLE_CHAT_WEBHOOK_URL="https://chat.googleapis.com/v1/spaces/AAQA8ty0idU/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=LhJTC_JlBG3CZMV4fQ7Jocy69SDd42CnmTd4KY4o7Ts"
          curl -X POST -H "Content-Type: application/json" \
              -d '{
                    "text": "🚀 Server '"$APP_URL"' has been successfully deployed!"
                  }' "$GOOGLE_CHAT_WEBHOOK_URL"
