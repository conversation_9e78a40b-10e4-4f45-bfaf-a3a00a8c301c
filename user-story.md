# User Stories for HapoEst Project

## Project Management

As a user, I want to:
- Create new projects with basic information (name, description, software type, technology stack)
- View a list of all my projects
- View detailed information about each project
- Update project information using intuitive edit buttons
- Edit project information, description, and requirements directly from the project details page
- Search for projects by name, description, or requirements text
- Delete projects I no longer need

## Requirements Management

As a user, I want to:
- Input project requirements as text
- Upload requirements documents (PDF)
- View and edit requirements for each project
- Track changes to requirements

## Estimation

As a user, I want to:
- Generate AI-powered estimates based on project requirements
- Generate AI estimates directly from the project details page with a single click
- View detailed breakdowns of estimates including:
  - Backend development effort
  - Frontend development effort
  - Unit testing effort
- See unit rates for each development item in the PDF export
- Edit and adjust the generated estimates
- View summary of total effort and costs
- Export estimates as professionally formatted PDF reports that match my current view
- Save and track multiple versions of estimates
- See a loading overlay during AI estimate generation to prevent accidental interactions

## User Interface

As a user, I want:
- A clean, modern interface using Tailwind CSS
- Responsive design that works on all devices
- Clear navigation between different sections
- Intuitive forms for data entry
- Real-time updates and feedback
- Professional PDF export with consistent styling

## Internationalization

As a user, I want:
- Switch between multiple languages (English, Vietnamese, Japanese)
- See all content in my preferred language
- Maintain language preference across sessions

## Security

As a user, I want:
- Secure login with email/password
- Protected access to my projects and estimates
- Secure API endpoints
- Token-based authentication
- Password reset functionality

# User Stories - Hệ Thống Ước Tính Dự Án Phần Mềm

**Ngày tạo:** 2025-04-02
**Cập nhật lần cuối:** 2025-05-08

**Mục đích:** Tài liệu này mô tả các User Story (Câu chuyện người dùng) cho việc phát triển Hệ thống Ước tính Dự án Phần mềm, dựa trên các yêu cầu đã được cung cấp. Các story này sẽ định hướng cho quá trình phát triển và kiểm thử.

---

## Phần 1: Xác thực Người dùng

**US001: Đăng ký tài khoản bằng Email**
* **As a** new user,
* **I want to** register for an account using my email address and a password,
* **So that** I can access the system and use the project estimation features.

* **Acceptance Criteria (AC):**
    * Hiển thị màn hình đăng ký với các trường: Email, Mật khẩu, Xác nhận Mật khẩu.
    * Validate định dạng email.
    * Validate mật khẩu (độ dài tối thiểu, độ phức tạp nếu cần).
    * Validate mật khẩu xác nhận phải trùng khớp.
    * Thông báo lỗi rõ ràng nếu validation thất bại.
    * Khi đăng ký thành công, lưu thông tin user vào database (PostgreSQL).
    * Thông báo đăng ký thành công và chuyển hướng người dùng đến màn hình đăng nhập hoặc dashboard.
    * Mật khẩu phải được hash trước khi lưu vào CSDL.

**US002: Đăng nhập bằng Email**
* **As a** registered user,
* **I want to** log in using my email and password,
* **So that** I can access my account and the estimation tool.

* **AC:**
    * Hiển thị màn hình đăng nhập với các trường: Email, Mật khẩu.
    * Validate định dạng email.
    * Khi nhấn nút "Đăng nhập", hệ thống kiểm tra thông tin đăng nhập với CSDL.
    * Nếu thông tin chính xác, tạo phiên làm việc (session/token) và chuyển hướng đến trang chính của ứng dụng.
    * Nếu thông tin không chính xác, hiển thị thông báo lỗi "Email hoặc mật khẩu không đúng".
    * Cung cấp link/button "Quên mật khẩu?".

**US003: Đăng nhập/Đăng ký bằng Google (SNS)**
* **As a** user,
* **I want to** be able to sign up or log in using my existing Google account,
* **So that** I can access the system quickly without needing to create or remember a separate password.

* **AC:**
    * Hiển thị nút "Đăng nhập bằng Google" / "Đăng ký bằng Google" trên màn hình đăng nhập và đăng ký.
    * Khi nhấn nút, chuyển hướng đến quy trình xác thực của Google.
    * Sau khi Google xác thực thành công, nhận thông tin người dùng (email, tên, id...).
    * Nếu email từ Google chưa tồn tại trong hệ thống, tự động tạo tài khoản mới liên kết với Google ID.
    * Nếu email đã tồn tại, thực hiện đăng nhập cho người dùng đó.
    * Tạo phiên làm việc và chuyển hướng đến trang chính của ứng dụng.

**US004: Đặt lại mật khẩu**
* **As a** user who forgot my password,
* **I want to** request a password reset via email,
* **So that** I can regain access to my account.

* **AC:**
    * Hiển thị màn hình "Quên mật khẩu" yêu cầu nhập email đã đăng ký.
    * Validate định dạng email.
    * Nếu email tồn tại trong hệ thống, gửi email chứa link/mã đặt lại mật khẩu đến địa chỉ đó.
    * Link/mã đặt lại mật khẩu phải có thời hạn hiệu lực.
    * Khi người dùng truy cập link đặt lại, hiển thị màn hình nhập mật khẩu mới và xác nhận mật khẩu mới.
    * Validate mật khẩu mới (như khi đăng ký).
    * Cập nhật mật khẩu mới (đã hash) vào CSDL.
    * Thông báo đặt lại mật khẩu thành công và yêu cầu người dùng đăng nhập lại.

**US004.1: Forgot Password Page**
* **As a** user who forgot my password,
* **I want to** access a dedicated forgot password page at `/forgot-password`,
* **So that** I can initiate the password reset process easily from the login page.

* **AC:**
    * Trang đăng nhập có link "Forgot Password?" dẫn đến `/forgot-password`.
    * Trang forgot password có giao diện tương tự trang đăng nhập với branding nhất quán.
    * Form chỉ yêu cầu nhập email address.
    * Sau khi submit thành công, hiển thị thông báo xác nhận và hướng dẫn kiểm tra email.
    * Có link "Back to Login" để quay lại trang đăng nhập.
    * Hỗ trợ đa ngôn ngữ (EN, VI, JP).

**US004.2: Password Reset Email**
* **As a** user requesting password reset,
* **I want to** receive a secure reset link via email,
* **So that** I can reset my password securely.

* **AC:**
    * Email được gửi bằng AWS SES với template responsive.
    * Email chứa button "Reset Password" và link backup.
    * Reset token có thời hạn 1 giờ.
    * Link dẫn đến `/reset-password?token={token}`.
    * Email content được localize theo ngôn ngữ người dùng.
    * Nếu email không tồn tại, vẫn trả về thông báo thành công (security).

**US004.3: Password Reset Page**
* **As a** user with a valid reset token,
* **I want to** set a new password on the reset password page,
* **So that** I can regain access to my account with a new secure password.

* **AC:**
    * Trang `/reset-password` validate reset token từ URL parameter.
    * Nếu token invalid/expired, hiển thị lỗi và link về forgot password.
    * Form có 2 trường: "New Password" và "Confirm New Password".
    * Validate password strength (8+ chars, uppercase, lowercase, number, special char).
    * Real-time validation và hiển thị trạng thái password match.
    * Sau khi reset thành công, hiển thị thông báo và auto-redirect về login sau 3 giây.
    * Hỗ trợ đa ngôn ngữ với translations đầy đủ.

---

## Phần 2: Nhập Yêu Cầu và Ước Tính Dự Án

**US005: Nhập yêu cầu dự án bằng Text Input**
* **As a** logged-in user,
* **I want to** input my project requirements (like software type, technology stack, scale, features description) directly into a text area, referencing the layout similar to `sample.tsx`,
* **So that** the system can analyze this information to generate an estimate.

* **AC:**
    * Hiển thị màn hình nhập liệu với các trường cần thiết (loại phần mềm, công nghệ, quy mô...).
    * Có một vùng nhập text lớn (`textarea`) để người dùng mô tả chi tiết yêu cầu.
    * Giao diện tuân thủ thiết kế tham khảo từ `sample.tsx`.
    * Nút "Submit" hoặc "Analyze" để gửi yêu cầu đi phân tích.

**US006: Nhập yêu cầu dự án bằng cách tải lên file PDF**
* **As a** logged-in user,
* **I want to** upload a PDF file containing my project requirements,
* **So that** the system can extract and analyze the content from the PDF to generate an estimate.

* **AC:**
    * Trên màn hình nhập liệu, có tùy chọn để tải lên file.
    * Giới hạn chỉ cho phép tải lên file định dạng PDF.
    * Có thể có giới hạn kích thước file (cần xác định).
    * Hiển thị tiến trình tải lên (nếu file lớn).
    * Sau khi tải lên thành công, file được gửi đến backend để xử lý.
    * Nút "Submit" hoặc "Analyze" để gửi yêu cầu đi phân tích (có thể tích hợp với việc upload).

**US007: AI phân tích và tạo ước tính**
* **As a** logged-in user,
* **I want** the system to use an AI model (Gemini Flash) to analyze the project requirements I provided (either text or PDF) and historical data (nếu có),
* **And** I want to be able to trigger this analysis directly from the project details page,
* **So that** I receive a proposed estimate for the project's time (effort/công số), required resources (nguồn lực - có thể quy đổi ra effort), and potentially cost.

* **AC:**
    * Sau khi người dùng submit yêu cầu (US005, US006), backend nhận dữ liệu (text hoặc nội dung trích xuất từ PDF).
    * Backend gọi API của mô hình Gemini Flash, truyền vào dữ liệu yêu cầu và các tham số cần thiết.
    * AI xử lý yêu cầu, dựa trên kiến thức được huấn luyện (và dữ liệu lịch sử nếu được tích hợp) để đưa ra các con số ước tính.
    * Backend nhận kết quả từ AI.
    * Hiển thị trạng thái đang xử lý cho người dùng.

---

## Phần 3: Hiển thị và Tinh chỉnh Kết quả Ước Tính

**US008: Hiển thị kết quả ước tính chi tiết (Development Tab)**
* **As a** logged-in user,
* **I want to** view the detailed estimation results in a "Development" tab, showing effort breakdown by category (backend, frontend, unit test) with totals for each,
* **So that** I can understand the specific effort distribution for the core development tasks.

* **AC:**
    * Sau khi AI xử lý xong (US007), hiển thị màn hình kết quả.
    * Trong quá trình AI xử lý, hiển thị một màn hình loading overlay để ngăn người dùng tương tác với hệ thống.
    * Màn hình có 2 tab: "Development" và "Summary". Tab "Development" được chọn mặc định.
    * Tab "Development" hiển thị một bảng với các cột: Hạng mục (Backend, Frontend, Unit Test), Công số ước tính (ví dụ: man-days, man-hours).
    * Có thể có nhiều dòng chi tiết hơn trong từng hạng mục nếu AI trả về.
    * Dưới bảng, hiển thị dòng tổng cộng công số cho từng hạng mục (Tổng Backend, Tổng Frontend, Tổng Unit Test).
    * Dữ liệu hiển thị phải khớp với kết quả trả về từ AI/backend.

**US009: Hiển thị kết quả ước tính tổng hợp (Summary Tab)**
* **As a** logged-in user,
* **I want to** view a summarized estimation in a "Summary" tab, including total development effort and estimates for other phases like requirements definition, UI/UX design, integration testing, UAT support, along with a grand total,
* **So that** I get a high-level overview of the estimated effort for the entire project lifecycle.

* **AC:**
    * Trên màn hình kết quả, người dùng có thể chuyển sang tab "Summary".
    * Tab "Summary" hiển thị danh sách các hạng mục:
        * Total Development Effort (lấy từ tổng của tab Development)
        * Requirements Define (ước tính từ AI)
        * UI/UX Design (ước tính từ AI)
        * Integration Test (ước tính từ AI)
        * UAT Support (ước tính từ AI)
    * Mỗi hạng mục có công số ước tính tương ứng.
    * Hiển thị dòng "Grand Total" tính tổng công số của tất cả các hạng mục trong tab Summary.
    * Dữ liệu hiển thị phải khớp với kết quả trả về từ AI/backend.

**US010: Tinh chỉnh kết quả ước tính**
* **As a** logged-in user,
* **I want to** be able to modify the estimated numbers (effort/công số) presented in both the Development and Summary tabs,
* **So that** I can refine the estimate based on my specific knowledge or project constraints, and see the totals update automatically.

* **AC:**
    * Các ô chứa giá trị công số ước tính trong cả hai tab phải cho phép chỉnh sửa (editable fields).
    * Khi người dùng thay đổi một giá trị, các giá trị tổng liên quan (tổng hạng mục, tổng development, grand total) phải được tự động tính toán lại và cập nhật trên giao diện.
    * Thay đổi chỉ diễn ra ở phía frontend, không nhất thiết phải lưu lại ngay lập tức trừ khi có nút "Save Changes". *(Cần làm rõ yêu cầu lưu trữ bản chỉnh sửa)*

**US011: Xuất kết quả ước tính ra file PDF**
* **As a** logged-in user,
* **I want to** export the estimation results (both detailed Development view and Summary view) into a PDF file,
* **So that** I can share the estimate easily, archive it, or perform further analysis offline.

* **AC:**
    * Có nút "Export to PDF" trên màn hình hiển thị kết quả.
    * Khi nhấn nút, hệ thống tạo một file PDF.
    * File PDF chứa nội dung của tab "Development" và tab "Summary".
    * PDF bao gồm cả thông tin đơn giá (Unit Rate) cho mỗi hạng mục phát triển.
    * PDF hiển thị đúng các ký tự đặc biệt của tất cả các ngôn ngữ được hỗ trợ.
    * Dữ liệu trong file PDF phải khớp với dữ liệu đang hiển thị trên màn hình (bao gồm cả các thay đổi nếu người dùng đã tinh chỉnh - US010).
    * Trình duyệt tự động tải file PDF về máy người dùng.
    * Template cụ thể của file PDF sẽ được định nghĩa sau, nhưng cấu trúc cơ bản phải có đủ các hạng mục và tổng số.

**US012: Liên hệ để chỉnh sửa/tư vấn thêm về ước tính**
* **As a** logged-in user,
* **I want to** see a clear Call-to-Action (CTA) button or link on the results page if I'm not satisfied with the estimate,
* **So that** I can easily request further clarification or a manual review/adjustment from the service provider.

* **AC:**
    * Hiển thị một nút/link dễ thấy với nội dung như "Liên hệ tư vấn", "Yêu cầu chỉnh sửa", "Không hài lòng với kết quả?".
    * Khi nhấn vào CTA này, hệ thống chuyển hướng người dùng đến trang liên hệ, mở một biểu mẫu liên hệ, hoặc hiển thị thông tin liên lạc (email/số điện thoại). *(Cần xác định hành động cụ thể)*

---

## Phần 4: Yêu cầu Kỹ thuật (Non-Functional / Constraints)

* **NFR001: Công nghệ Backend:** Hệ thống backend phải được xây dựng bằng Python với framework FastAPI, sử dụng PostgreSQL làm cơ sở dữ liệu. Quản lý thư viện Python bằng `requirements.txt`.
* **NFR002: Công nghệ Frontend:** Hệ thống frontend phải được xây dựng bằng Next.JS và sử dụng TailwindCSS để styling.
* **NFR003: Deployment:** Ứng dụng phải có thể được đóng gói và chạy bằng Docker, sử dụng `Dockerfile` và `docker-compose.yml` để quản lý các container (frontend, backend, database).
* **NFR004: Phiên bản:** Sử dụng các phiên bản ổn định mới nhất của các công nghệ được liệt kê (Python, FastAPI, PostgreSQL, Next.JS, Node.js, TailwindCSS, Docker) tại thời điểm bắt đầu phát triển.
* **NFR005: AI Model:** Chức năng phân tích và ước tính phải sử dụng Google Gemini API với mô hình Gemini Flash để đảm bảo tính linh hoạt và hiệu suất cao. Hệ thống được thiết kế để dễ dàng chuyển đổi giữa các mô hình khác nhau theo yêu cầu.
* **NFR006: Đa ngôn ngữ:** Hệ thống phải hỗ trợ đầy đủ 3 ngôn ngữ (Tiếng Anh, Tiếng Việt, Tiếng Nhật) với khả năng mở rộng thêm ngôn ngữ trong tương lai. Sử dụng next-intl cho frontend và có cơ chế quản lý translation rõ ràng.

---

**Lưu ý:**
* Các User Story này nên được ưu tiên hóa (prioritized) cho các sprint/iteration.
* Giao diện người dùng cần tham khảo `sample.tsx` như đã đề cập trong yêu cầu ban đầu, đặc biệt cho màn hình nhập liệu (US005).
* Chi tiết về cách AI đưa ra ước tính (dựa vào prompt, dữ liệu lịch sử cụ thể nào) cần được làm rõ trong quá trình thiết kế kỹ thuật.
* Template file PDF (US011) cần được thống nhất và thiết kế chi tiết hơn.
* Hành động cụ thể khi người dùng nhấn CTA liên hệ (US012) cần được quyết định.

## Phần 5: Tìm kiếm và Chỉnh sửa Dự án

**US016: Tìm kiếm dự án**
* **As a** logged-in user,
* **I want to** search for projects by name, description, or requirements text,
* **So that** I can quickly find specific projects in a large list.

* **AC:**
    * Có thanh tìm kiếm trên trang danh sách dự án.
    * Khi nhập từ khóa và nhấn tìm kiếm, hệ thống hiển thị các dự án có tên, mô tả hoặc yêu cầu chứa từ khóa đó.
    * Tìm kiếm không phân biệt chữ hoa/thường.
    * Kết quả tìm kiếm được hiển thị ngay lập tức khi người dùng nhập.
    * Có nút xóa để xóa từ khóa tìm kiếm và hiển thị lại toàn bộ dự án.

**US017: Chỉnh sửa thông tin dự án**
* **As a** logged-in user,
* **I want to** edit the basic information of my projects (name, description, software type, technology stack),
* **So that** I can keep project information up-to-date.

* **AC:**
    * Trên trang chi tiết dự án, có nút "Edit" bên cạnh mỗi phần thông tin (Project Information, Project Description, Project Requirements).
    * Khi nhấn nút "Edit", hiển thị dialog cho phép chỉnh sửa thông tin tương ứng.
    * Sau khi chỉnh sửa và lưu, thông tin được cập nhật ngay lập tức trên giao diện và trong CSDL.
    * Hiển thị thông báo thành công khi cập nhật thành công.

**US018: Tạo ước tính nhanh từ trang chi tiết dự án**
* **As a** logged-in user,
* **I want to** generate an AI estimate directly from the project details page,
* **So that** I can quickly get an estimate without navigating to the estimates list page.

* **AC:**
    * Trên trang chi tiết dự án, có nút "AI Estimate" bên cạnh nút "Edit" trong phần Project Requirements.
    * Nút này chỉ được kích hoạt khi dự án có yêu cầu (requirements text).
    * Khi nhấn nút, hiển thị màn hình loading overlay và bắt đầu quá trình tạo ước tính bằng AI.
    * Sau khi quá trình hoàn tất, chuyển hướng đến trang xem trước ước tính.
    * Người dùng có thể xem và chỉnh sửa ước tính trước khi lưu.

**US019: Giao diện loading khi tạo ước tính AI**
* **As a** logged-in user,
* **I want to** see a loading overlay when the system is generating an AI estimate,
* **So that** I know the system is working and avoid accidental interactions during processing.

* **AC:**
    * Khi bắt đầu quá trình tạo ước tính AI, hiển thị một overlay phủ toàn màn hình với hiệu ứng loading.
    * Overlay hiển thị thông báo rõ ràng về việc đang tạo ước tính và yêu cầu người dùng chờ đợi.
    * Overlay ngăn người dùng tương tác với các phần khác của ứng dụng trong quá trình xử lý.
    * Sau khi quá trình hoàn tất, overlay tự động biến mất và chuyển hướng đến trang kết quả.

## Phần 6: Cài đặt Người dùng

**US013: Thay đổi mật khẩu tài khoản**
* **As a** logged-in user,
* **I want to** change my account password,
* **So that** I can maintain the security of my account.

* **AC:**
    * Có màn hình "Cài đặt" với tab "Thay đổi mật khẩu".
    * Hiển thị form với các trường: Mật khẩu hiện tại, Mật khẩu mới, Xác nhận mật khẩu mới.
    * Validate mật khẩu hiện tại bằng cách kiểm tra với mật khẩu đã lưu trong CSDL.
    * Validate mật khẩu mới phải đáp ứng các yêu cầu an toàn (độ dài tối thiểu 8 ký tự).
    * Validate mật khẩu mới và xác nhận mật khẩu mới phải trùng khớp.
    * Khi submit form thành công, cập nhật mật khẩu mới (đã hash) vào CSDL.
    * Hiển thị thông báo thành công khi cập nhật mật khẩu thành công.

**US014: Cài đặt đơn vị tiền tệ mặc định**
* **As a** logged-in user,
* **I want to** select my preferred currency (USD, VND, JPY) for displaying costs,
* **So that** I can view project estimates in my preferred currency format.

* **AC:**
    * Có màn hình "Cài đặt" với tab "Đơn vị tiền tệ".
    * Hiển thị các lựa chọn đơn vị tiền tệ: USD, VND, JPY.
    * Đơn vị tiền tệ đã được chọn trước đó (nếu có) được hiển thị mặc định.
    * Người dùng có thể chọn một đơn vị tiền tệ và lưu lại.
    * Sau khi lưu thành công, cài đặt đơn vị tiền tệ được cập nhật trong CSDL.
    * Đơn vị tiền tệ được chọn sẽ ảnh hưởng đến cách hiển thị chi phí trong toàn bộ ứng dụng.

**US015: Thiết lập đơn giá vai trò**
* **As a** logged-in user,
* **I want to** set the day rates for different roles in a project (PM, Developer, Designer, etc.),
* **So that** I can customize cost calculations based on my organization's specific rate card.

* **AC:**
    * Có màn hình "Cài đặt" với tab "Đơn giá vai trò".
    * Hiển thị danh sách các vai trò phổ biến trong dự án: PM, Developer, Designer, Tester, Backend, Frontend, UI/UX Designer, QA, DevOps, BA, Architect...
    * Mỗi vai trò có một trường nhập liệu để người dùng nhập đơn giá ngày (day rate).
    * Các giá trị đơn giá đã được thiết lập trước đó (nếu có) được hiển thị mặc định.
    * Người dùng có thể cập nhật một hoặc nhiều đơn giá và lưu lại.
    * Sau khi lưu thành công, đơn giá cho các vai trò được cập nhật trong CSDL.
    * Đơn giá này sẽ được sử dụng trong việc tính toán chi phí trong các ước tính dự án.