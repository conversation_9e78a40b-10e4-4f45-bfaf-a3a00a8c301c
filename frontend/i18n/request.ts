import {getRequestConfig} from 'next-intl/server';
import {locales} from './settings';

export default getRequestConfig(({locale}) => {
  const currentLocale = locale || 'en';
  // Validate the locale
  if (!locales.includes(currentLocale as any)) {
    throw new Error(`Unsupported locale: ${locale}`);
  }

  return {
    locale: currentLocale,
    messages: import(`../messages/${currentLocale}/index.json`)
  };
}); 