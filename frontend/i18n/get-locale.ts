import { defaultLocale, locales } from './settings';

// Helper to safely extract locale from params
export function getLocale(params: { locale?: string }) {
  // Get locale from params or use default
  const locale = params?.locale || defaultLocale;
  
  // Validate locale
  const isValidLocale = locales.includes(locale as any);
  if (!isValidLocale) {
    return defaultLocale;
  }
  
  return locale;
} 