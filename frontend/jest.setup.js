// Thê<PERSON> các phần mở rộng custom cho Jest
import '@testing-library/jest-dom';

// Mock fetch
import { enableFetchMocks } from 'jest-fetch-mock';
enableFetchMocks();

// Mock next/router
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    pathname: '/',
    locale: 'en',
    push: jest.fn(),
    prefetch: jest.fn(() => Promise.resolve()),
  }),
  useParams: () => ({
    locale: 'en',
    id: '1',
  }),
}));

// Mock next-intl hook
jest.mock('next-intl', () => ({
  useTranslations: () => (key) => key,
}));

// Mock localStorage
Object.defineProperty(window, 'localStorage', {
  value: {
    getItem: jest.fn(() => null),
    setItem: jest.fn(() => null),
    removeItem: jest.fn(() => null),
  },
  writable: true,
}); 