{"app": {"title": "<PERSON><PERSON><PERSON>", "description": "AI-powered software project estimation and planning platform"}, "common": {"loading": "Processing...", "error": "System Error", "success": "Operation Completed", "save": "Save Changes", "pleaseWait": "Processing your request...", "saving": "Saving Changes...", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "create": "Create", "submit": "Confirm", "back": "Back", "next": "Next", "previous": "Previous", "language": "Language", "or": "Or", "backToList": "Back to List", "backToProject": "Back to Project"}, "nav": {"home": "Dashboard", "projects": "Projects", "settings": "Settings", "logout": "Sign Out", "login": "Sign In", "register": "Register", "account": "Account Management", "accountOptions": "Account", "profile": "Profile"}, "settings": {"title": "Settings", "account": "Account", "preferences": "Preferences", "currency": "<PERSON><PERSON><PERSON>", "currencyUpdated": "Currency settings updated", "settingsUpdated": "Configuration saved successfully", "passwordUpdated": "Password updated successfully", "passwordsDoNotMatch": "Password confirmation does not match", "passwordError": "Password update failed", "passwordReset": "Forgot Password?", "currentPassword": "Current Password", "newPassword": "New Password", "confirmPassword": "Confirm New Password", "changePassword": "Update Password", "confirmNewPassword": "Re-enter New Password", "saveSettings": "Save Configuration", "roleRates": "Role-based Rates", "roleRatesDescription": "Configure daily rates by project role", "roleRatesUpdated": "Role rates configuration saved", "currencyTitle": "Select Currency", "currencyDescription": "Default currency for all estimates", "USD": "US Dollar ($)", "VND": "<PERSON> Dong (₫)", "JPY": "Japanese Yen (¥)", "setupGuide": {"title": "Complete your settings", "description": "Please set up your currency and role rates before creating projects. This will ensure accurate cost calculations.", "goToSettings": "Go to Settings"}, "settingsSetupGuide": "Important Settings Guide", "settingsRequiredInfo": "These settings are required for accurate project estimates", "currencyImportance": "Currency selection affects how costs are displayed in all estimates", "roleRatesImportance": "Role rates are used to calculate the total cost of your project estimates", "roleRatesExample": "Example: If a Developer costs $500/day and works for 10 days, the cost will be $5,000", "setupComplete": "Setup Complete", "setupIncomplete": "Setup Incomplete", "setupRolesTooltip": "Please set rates for at least the main roles (<PERSON><PERSON><PERSON>, Designer, PM) to get accurate estimates", "ratesSuggestion": "Suggested rates (you can customize)", "applySuggested": "Apply Suggested Rates", "passwordMatch": "Password match"}, "auth": {"loginTitle": "Sign in to continue", "registerTitle": "Account Registration", "loginSubtitle": "Enter your email and password to sign in", "registerSubtitle": "Complete the registration form to create your account", "login": "Sign In", "register": "Register", "email": "Email Address", "password": "Password", "confirmPassword": "Confirm Password", "forgotPassword": "Reset Password", "loginSuccess": "Sign In successful", "loginFailed": "Sign In failed", "registerSuccess": "Account created successfully", "registerFailed": "Account creation failed", "passwordsDoNotMatch": "Password confirmation does not match", "passwordsMatch": "Passwords match", "invalidEmail": "Please enter a valid email address", "noAccount": "Don't have an account?", "createAccount": "Register Now", "alreadyAccount": "Existing user?", "orLoginWith": "Or authenticate using", "googleLogin": "Sign in with Google", "googleRegister": "Register with Google", "passwordRequirements": "Password must meet criteria below", "passwordRules": {"title": "Password Policy", "length": "Minimum 8 characters", "uppercase": "At least one uppercase letter (A-Z)", "lowercase": "At least one lowercase letter (a-z)", "number": "At least one numeric digit (0-9)", "special": "At least one special character (!@#$%^&*)"}, "agreeToTerms": "I accept the terms of service and privacy policy", "mustAgreeTerms": "You must accept the terms to continue", "emailSentDescription": "A verification code has been sent to your registered email address", "verificationCode": "Verification Code", "resendCode": "Resend Code", "verify": "Verify Account", "registrationSuccess": "Account registration complete", "registrationFailed": "Registration unsuccessful", "redirectingToLogin": "Redirecting to login...", "emailVerified": "Email verification successful", "emailSent": "Verification code sent", "verifyEmail": "Email Verification", "verifyEmailSubtitle": "Enter the verification code sent to your email", "serverError": "System error. Please try again later.", "forgotPasswordTitle": "Reset Your Password", "forgotPasswordSubtitle": "Enter your email address and we'll send you a link to reset your password", "forgotPasswordDescription": "Don't worry, we'll help you recover your account access quickly and securely", "sendResetLink": "Send Reset Link", "resetLinkSent": "Reset link has been sent to {email}", "resetPasswordFailed": "This reset link is invalid or has already been used. Please try requesting a new one.", "didntReceiveEmail": "Didn't receive the email?", "tryAgain": "Try again", "backToLogin": "Back to Login", "resetPasswordTitle": "Create New Password", "resetPasswordSubtitle": "Enter your new password below", "newPassword": "New Password", "confirmNewPassword": "Confirm New Password", "resetPassword": "Reset Password", "resetPasswordSuccess": "Password reset successfully", "invalidResetToken": "Invalid or expired reset link", "checkYourEmail": "Check your email for reset instructions", "invalidPassword": "The configured password is invalid", "incorrectLoginInformation": "Incorrect email or password. Please check again"}, "projects": {"title": "Project Management", "newProject": "New Project", "createProject": "Create New Project", "createNew": "Create Project", "noProjects": "No projects available", "createFirstProject": "Create your first project", "setupNeeded": "Initial configuration required before creating projects", "setupSettings": "Configure System Settings", "settingsRequired": "Please configure currency and role rates for accurate project estimation", "goToSettings": "Access Settings", "projectName": "Project Title", "description": "Project Overview", "softwareType": "Application Type", "techStack": "Technology Stack", "createdAt": "Creation Date", "updatedAt": "Last Modified", "lastUpdated": "Last Modified", "projectInfo": "Project Details", "projectDescription": "Project Scope", "projectRequirements": "Requirements Specification", "projectEstimation": "Effort Estimation", "noDescription": "No description available", "noRequirements": "No requirements defined", "requirementsNeeded": "Requirements must be defined before estimation", "addRequirementsFirst": "Please define project requirements before generating estimates. Navigate to project details and select 'Edit Requirements'.", "goToProjectDetails": "View Project Details", "fileRequirement": "Requirements Document", "alreadyEstimated": "Estimation complete", "notYetEstimated": "Pending estimation", "createEstimate": "Generate Estimate", "viewEstimate": "View Estimation Details", "deleteProject": "Remove Project", "confirmDelete": "Confirm project deletion", "projectDoesNotExist": "Project not found", "aiEstimate": "AI-Powered Estimation", "generatingAIEstimate": "Processing AI-based estimation...", "cannotLoadProject": "Unable to load project data", "backToList": "Return to Project List", "generateEstimate": "Calculate Estimate", "uploadRequirements": "Upload Requirements Document", "requirementsPlaceholder": "Enter detailed project specifications, features, and technical requirements...", "allowedFileTypes": "Supported formats: PDF, DOCX, TXT (Max: 10MB)", "exportPDF": "Export as PDF", "exportExcel": "Export to Excel", "pdfExported": "PDF export completed", "pdfExportFailed": "PDF export failed", "excelExported": "Excel export completed", "excelExportFailed": "Excel export failed", "havingEstimate": "Estimation complete", "createFailed": "Project creation failed", "showing": "Displaying", "of": "of", "projects": "projects", "editRequirements": "Modify Requirements", "searchPlaceholder": "Search projects by name, description, or requirements", "showingEstimates": "Viewing {count} of {total} estimates", "titleConfirmDelete": "This action cannot be undone.", "confirmProjectDelete": "Are you sure you want to delete {project_name}?", "totalProjects": "Total"}, "estimate": {"title": "Project Estimate", "summary": "Summary", "details": "Details", "timeline": "Timeline", "cost": "Cost", "risks": "Risks", "hoursTotal": "Total Hours", "costTotal": "Total Cost", "phaseBreakdown": "Phase Breakdown", "planning": "Planning", "design": "Design", "development": "Development", "testing": "Testing", "deployment": "Deployment", "maintenance": "Maintenance", "feedback": "Not satisfied with the estimate results?", "contactSupport": "Contact Support", "deleteEstimate": "Delete Estimate", "deleteConfirmTitle": "Confirm Estimate Deletion", "deleteConfirmDescription": "Are you sure you want to delete this estimate? This action cannot be undone.", "deleteEstimateConfirm": "Are you sure you want to delete this estimate? This action cannot be undone.", "showingEstimates": "Showing {count} of {total} estimates", "deleteError": "Could not delete estimate. Please try again.", "previewTitle": "Estimate Preview", "previewSubtitle": "Preview and adjust your estimate", "previewDescription": "You can adjust the numbers and select which features to include in the MVP. MUST HAVE features cannot be deselected.", "editPreview": "Edit Preview", "feature": "Feature", "description": "Description", "backendEffort": "Backend (man days)", "frontendEffort": "Frontend (man days)", "unitTestEffort": "Unit Test (man days)", "priority": "Priority", "includeInMVP": "Include in MVP", "category": "Category", "effort": "<PERSON><PERSON><PERSON>", "grandTotal": "Grand Total", "estimateSaved": "Estimate saved successfully", "confirm": "Confirm", "saveError": "Could not save estimate. Please try again.", "mustHave": "Must Have", "niceToHave": "Nice to Have", "select": "Select", "dayRate": "Day Rate", "totalCost": "Total Cost", "manDays": "Man Days", "day": "Day", "totalManDays": "Total Man Days", "estimateResults": "Estimate Results", "role": "Role", "roleRates": "Role Rates", "roles": {"PM": "Project Manager", "Developer": "Developer", "Designer": "Designer", "Tester": "Tester", "Backend": "Backend Developer", "Frontend": "Frontend Developer", "UI": "UI Designer", "UX": "UX Designer", "QA": "QA Engineer", "DevOps": "DevOps Engineer", "BA": "Business Analyst", "Architect": "Solution Architect"}, "setRoleDayRates": "Set Role Day Rates", "summaryCategories": {"requirementsDefinition": "Requirements Definition", "development": "Development", "uiUxDesign": "UI/UX Design", "integrationTest": "Integration Test", "uatSupport": "UAT Support", "projectManagement": "Project Management"}, "summaryDescriptions": {"totalDevelopment": "Total development effort", "requirementsAnalysis": "Requirements gathering and analysis", "uiUxDesign": "User interface and experience design", "integrationTesting": "Integration and system testing", "uatSupport": "User acceptance testing support", "projectManagement": "Overall project management activities"}, "summaryDescriptionsDetail": {"requirementsAnalysis": "Requirements gathering and analysis ({percent}% of development effort)", "uiUxDesign": "User interface and experience design ({percent}% of development effort)", "totalDevelopment": "Total development effort (Backend: {backend}, Frontend: {frontend}, Testing: {testing})", "integrationTesting": "Integration and system testing ({percent}% of development effort)", "uatSupport": "User acceptance testing support ({percent}% of development effort)", "projectManagement": "Project management overhead ({percent}% of all efforts)"}, "excelLabels": {"projectName": "Project Name", "estimateName": "Estimate Name", "creationDate": "Creation Date"}, "estimates": "Estimates", "estimatesList": "Estimates List", "estimateDetails": "Estimate Details", "editEstimate": "Edit Estimate", "createNewEstimate": "Create New Estimate", "selectEstimate": "Select Estimate", "noEstimates": "No estimates found for this project", "name": "Name", "currency": "<PERSON><PERSON><PERSON><PERSON>", "createdAt": "Created At", "totalEffort": "Total Effort", "estimateInfo": "Estimate Information", "developmentItems": "Development Items", "summaryItems": "Summary Items", "include": "Include", "backToDetails": "Back to Details", "saving": "Saving...", "generateFromRequirements": "Generate from Requirements", "generatingEstimate": "Generating Estimate from Requirements"}, "landing": {"hero": {"title": "AI-Driven Software Project Estimation Platform", "subtitle": "Transform your project planning with intelligent estimation technology that delivers precise effort and cost breakdowns across all development disciplines", "tryNow": "Start Free Trial", "getStarted": "Access Dashboard", "learnMore": "Platform Overview"}, "features": {"title": "Enterprise-Grade Estimation Solutions", "subtitle": "Harnessing artificial intelligence and industry benchmarks to deliver data-driven project estimations", "feature1": {"title": "Intelligent Analysis Engine", "description": "Advanced NLP algorithms parse project requirements to generate precise estimates using historical data and industry standards"}, "feature2": {"title": "Collaborative Workflows", "description": "Streamline stakeholder collaboration with shared estimation workspaces and real-time feedback mechanisms"}, "feature3": {"title": "Rapid Estimation", "description": "Reduce estimation time from weeks to minutes with our automated analysis and reporting system"}, "feature4": {"title": "Resource Optimization", "description": "Leverage detailed cost breakdowns to optimize team allocation and budget planning"}, "feature5": {"title": "Professional Reporting", "description": "Generate comprehensive estimation reports with detailed breakdowns of development phases and resource requirements"}, "feature6": {"title": "Data-Driven Accuracy", "description": "Continuous learning system that improves estimation precision through machine learning and real project outcomes"}}, "cta": {"title": "Ready to revolutionize your project estimation process?", "subtitle": "Join industry leaders who rely on our platform for precise project planning and resource allocation", "button": "Begin Your Free Trial", "cta_contact_haposoft": "Contact Haposoft"}}, "footer": {"description": "Hapoest delivers AI-powered software development estimation and project planning solutions for enterprise teams", "allRightsReserved": "All Rights Reserved.", "links": {"title": "Navigation", "about": "About Us", "features": "Platform Features", "pricing": "Pricing Plans", "blog": "Blog"}, "legal": {"title": "Legal & Compliance", "terms": "Terms of Service", "privacy": "Privacy Policy", "cookies": "<PERSON><PERSON>"}, "contact": {"title": "Contact Information", "email": "<EMAIL>", "phone": "+84-85-645-9898", "address": "7F Mitec Tower, Duong Dinh Nghe Street, Cau Giay District, Hanoi, Vietnam"}}}