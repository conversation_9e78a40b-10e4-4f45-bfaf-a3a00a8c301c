import { renderHook, act } from '@testing-library/react';
import useUserSettings from '@/lib/hooks/useUserSettings';
import fetchMock from 'jest-fetch-mock';

describe('useUserSettings hook', () => {
  beforeEach(() => {
    fetchMock.resetMocks();
    Object.defineProperty(window, 'localStorage', {
      value: {
        getItem: jest.fn(),
        setItem: jest.fn(),
        removeItem: jest.fn(),
      },
      writable: true,
    });
  });

  it('should return default settings when user is not logged in', async () => {
    // Mock localStorage to return null token
    jest.spyOn(window.localStorage, 'getItem').mockReturnValue(null);

    const { result } = renderHook(() => useUserSettings('en'));

    // Wait for the async operation to complete
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 0));
    });

    // Check if default English settings are returned
    expect(result.current.userSettings).toEqual({
      id: 0,
      user_id: 0,
      currency: 'USD',
      role_rates: {
        PM: 200,
        Developer: 150,
        Designer: 150,
        Tester: 150,
        Backend: 150,
        Frontend: 150,
        UI: 150,
        UX: 150,
        QA: 150,
        DevOps: 180,
        BA: 200,
        Architect: 250
      }
    });
  });

  it('should return Vietnamese default settings when locale is vi', async () => {
    // Mock localStorage to return null token
    jest.spyOn(window.localStorage, 'getItem').mockReturnValue(null);

    const { result } = renderHook(() => useUserSettings('vi'));

    // Wait for the async operation to complete
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 0));
    });

    // Check if default Vietnamese settings are returned
    expect(result.current.userSettings?.currency).toBe('VND');
    expect(result.current.userSettings?.role_rates?.Developer).toBe(3000000);
  });

  it('should fetch user settings when user is logged in', async () => {
    // Mock localStorage to return a token
    jest.spyOn(window.localStorage, 'getItem').mockReturnValue('fake-token');

    // Mock API response
    fetchMock.mockResponseOnce(JSON.stringify({
      id: 1,
      user_id: 123,
      currency: 'USD',
      role_rates: {
        PM: 300,
        Developer: 250,
        Designer: 200,
        Tester: 180
      }
    }));

    const { result } = renderHook(() => useUserSettings('en'));

    // Wait for the async operation to complete
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 0));
    });

    // Check if API response is returned
    expect(result.current.userSettings?.id).toBe(1);
    expect(result.current.userSettings?.user_id).toBe(123);
    expect(result.current.userSettings?.role_rates?.Developer).toBe(250);
  });

  it('should use default settings when API returns empty role_rates', async () => {
    // Mock localStorage to return a token
    jest.spyOn(window.localStorage, 'getItem').mockReturnValue('fake-token');

    // Mock API response with empty role_rates
    fetchMock.mockResponseOnce(JSON.stringify({
      id: 1,
      user_id: 123,
      currency: 'USD',
      role_rates: {}
    }));

    const { result } = renderHook(() => useUserSettings('en'));

    // Wait for the async operation to complete
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 0));
    });

    // Check if default role_rates are used
    expect(result.current.userSettings?.id).toBe(1);
    expect(result.current.userSettings?.role_rates?.Developer).toBe(150);
  });

  it('should return the correct currency symbol', async () => {
    const { result } = renderHook(() => useUserSettings('en'));

    // Check currency symbols
    expect(result.current.getCurrencySymbol('USD')).toBe('$');
    expect(result.current.getCurrencySymbol('VND')).toBe('₫');
    expect(result.current.getCurrencySymbol('JPY')).toBe('¥');
  });
}); 