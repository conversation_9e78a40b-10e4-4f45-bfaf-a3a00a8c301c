import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import ProjectDetailPage from '@/app/[locale]/projects/[id]/page';

// Mock the necessary hooks and components
jest.mock('next-intl', () => ({
  useTranslations: () => (key: string) => key
}));

jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn()
  }),
  useParams: () => ({
    locale: 'en',
    id: '1'
  })
}));

// Mock LoadingOverlay component
jest.mock('@/components/ui/loading-overlay', () => ({
  LoadingOverlay: () => <div data-testid="loading-overlay">Loading Overlay</div>
}));

// Mock fetch
global.fetch = jest.fn();

describe('ProjectDetailPage', () => {
  beforeEach(() => {
    // Mock localStorage
    Object.defineProperty(window, 'localStorage', {
      value: {
        getItem: jest.fn(() => 'mock-token'),
        setItem: jest.fn(),
        removeItem: jest.fn()
      },
      writable: true
    });
    
    // Mock successful project fetch
    (global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: () => Promise.resolve({
        id: '1',
        name: 'Test Project',
        description: 'Test description',
        software_type: 'Web App',
        technology_stack: 'React, FastAPI',
        requirements_text: 'Test requirements',
        created_at: '2025-01-01T00:00:00Z',
        updated_at: '2025-01-02T00:00:00Z'
      })
    });
    
    // Mock successful estimates fetch
    (global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: () => Promise.resolve([
        {
          id: 1,
          name: 'Test Estimate',
          project_id: 1,
          grand_total: 10,
          created_at: '2025-01-03T00:00:00Z'
        }
      ])
    });
  });

  it('renders project details correctly', async () => {
    render(<ProjectDetailPage />);
    
    // Wait for data to load
    await waitFor(() => {
      expect(screen.getByText('Test Project')).toBeInTheDocument();
    });
    
    expect(screen.getByText('Test description')).toBeInTheDocument();
    expect(screen.getByText('Web App')).toBeInTheDocument();
    expect(screen.getByText('React, FastAPI')).toBeInTheDocument();
    expect(screen.getByText('Test requirements')).toBeInTheDocument();
  });

  it('shows edit buttons for project sections', async () => {
    render(<ProjectDetailPage />);
    
    // Wait for data to load
    await waitFor(() => {
      expect(screen.getByText('Test Project')).toBeInTheDocument();
    });
    
    // Check for edit buttons
    const editButtons = screen.getAllByText('common.edit');
    expect(editButtons.length).toBeGreaterThanOrEqual(3); // At least 3 edit buttons (info, description, requirements)
  });

  it('shows AI Estimate button when project has requirements', async () => {
    render(<ProjectDetailPage />);
    
    // Wait for data to load
    await waitFor(() => {
      expect(screen.getByText('Test Project')).toBeInTheDocument();
    });
    
    // Check for AI Estimate button
    expect(screen.getByText('projects.aiEstimate')).toBeInTheDocument();
  });

  it('opens edit dialog when edit button is clicked', async () => {
    render(<ProjectDetailPage />);
    
    // Wait for data to load
    await waitFor(() => {
      expect(screen.getByText('Test Project')).toBeInTheDocument();
    });
    
    // Find and click the first edit button (project info)
    const editButtons = screen.getAllByText('common.edit');
    fireEvent.click(editButtons[0]);
    
    // Check if dialog is opened
    await waitFor(() => {
      expect(screen.getByText('projects.editProjectInfo')).toBeInTheDocument();
    });
  });

  it('calls AI estimate endpoint when AI Estimate button is clicked', async () => {
    // Reset fetch mock
    (global.fetch as jest.Mock).mockReset();
    
    // Mock project and estimates fetch
    (global.fetch as jest.Mock)
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({
          id: '1',
          name: 'Test Project',
          requirements_text: 'Test requirements'
        })
      })
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve([])
      });
    
    // Mock AI estimate endpoint
    (global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: () => Promise.resolve({
        estimate: {
          development_items: [],
          summary_items: [],
          grand_total: 10,
          name: 'AI Estimate',
          currency: 'USD'
        }
      })
    });
    
    render(<ProjectDetailPage />);
    
    // Wait for data to load
    await waitFor(() => {
      expect(screen.getByText('Test Project')).toBeInTheDocument();
    });
    
    // Find and click AI Estimate button
    const aiButton = screen.getByText('projects.aiEstimate');
    fireEvent.click(aiButton);
    
    // Check if loading overlay is shown
    await waitFor(() => {
      expect(screen.getByTestId('loading-overlay')).toBeInTheDocument();
    });
    
    // Check if fetch was called with the correct endpoint
    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('/projects/1/estimates/generate-from-requirements/preview'),
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            'Authorization': 'Bearer mock-token'
          })
        })
      );
    });
  });
});
