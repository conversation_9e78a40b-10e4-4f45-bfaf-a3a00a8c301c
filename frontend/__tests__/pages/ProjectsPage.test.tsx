import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import ProjectsPage from '@/app/[locale]/projects/page';

// Mock the necessary hooks and components
jest.mock('next-intl', () => ({
  useTranslations: () => (key: string) => key
}));

jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn()
  }),
  useParams: () => ({
    locale: 'en'
  })
}));

// Mock fetch
global.fetch = jest.fn();

describe('ProjectsPage', () => {
  beforeEach(() => {
    // Mock localStorage
    Object.defineProperty(window, 'localStorage', {
      value: {
        getItem: jest.fn(() => 'mock-token'),
        setItem: jest.fn(),
        removeItem: jest.fn()
      },
      writable: true
    });
    
    // Mock successful projects fetch
    (global.fetch as jest.Mock).mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({
        items: [
          {
            id: '1',
            name: 'Test Project 1',
            description: 'First test project',
            created_at: '2025-01-01T00:00:00Z'
          },
          {
            id: '2',
            name: 'Test Project 2',
            description: 'Second test project',
            created_at: '2025-01-02T00:00:00Z'
          }
        ],
        total: 2
      })
    });
  });

  it('renders project list correctly', async () => {
    render(<ProjectsPage />);
    
    // Wait for data to load
    await waitFor(() => {
      expect(screen.getByText('Test Project 1')).toBeInTheDocument();
    });
    
    expect(screen.getByText('Test Project 2')).toBeInTheDocument();
    expect(screen.getByText('First test project')).toBeInTheDocument();
    expect(screen.getByText('Second test project')).toBeInTheDocument();
  });

  it('shows search input', async () => {
    render(<ProjectsPage />);
    
    // Check for search input
    const searchInput = screen.getByPlaceholderText('projects.searchPlaceholder');
    expect(searchInput).toBeInTheDocument();
  });

  it('calls search endpoint when search term is entered', async () => {
    // Reset fetch mock
    (global.fetch as jest.Mock).mockReset();
    
    // Mock search results
    (global.fetch as jest.Mock).mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({
        items: [
          {
            id: '1',
            name: 'Test Project 1',
            description: 'First test project',
            created_at: '2025-01-01T00:00:00Z'
          }
        ],
        total: 1
      })
    });
    
    render(<ProjectsPage />);
    
    // Find search input and enter search term
    const searchInput = screen.getByPlaceholderText('projects.searchPlaceholder');
    fireEvent.change(searchInput, { target: { value: 'First' } });
    
    // Check if fetch was called with the correct endpoint
    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('/projects/search?search_term=First'),
        expect.objectContaining({
          headers: expect.objectContaining({
            'Authorization': 'Bearer mock-token'
          })
        })
      );
    });
  });

  it('clears search when clear button is clicked', async () => {
    // Reset fetch mock
    (global.fetch as jest.Mock).mockReset();
    
    // Mock initial projects fetch and search results
    (global.fetch as jest.Mock)
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({
          items: [
            { id: '1', name: 'Test Project 1', description: 'First test project' },
            { id: '2', name: 'Test Project 2', description: 'Second test project' }
          ],
          total: 2
        })
      })
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({
          items: [
            { id: '1', name: 'Test Project 1', description: 'First test project' }
          ],
          total: 1
        })
      })
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({
          items: [
            { id: '1', name: 'Test Project 1', description: 'First test project' },
            { id: '2', name: 'Test Project 2', description: 'Second test project' }
          ],
          total: 2
        })
      });
    
    render(<ProjectsPage />);
    
    // Wait for initial data to load
    await waitFor(() => {
      expect(screen.getByText('Test Project 1')).toBeInTheDocument();
      expect(screen.getByText('Test Project 2')).toBeInTheDocument();
    });
    
    // Find search input and enter search term
    const searchInput = screen.getByPlaceholderText('projects.searchPlaceholder');
    fireEvent.change(searchInput, { target: { value: 'First' } });
    
    // Wait for search results
    await waitFor(() => {
      expect(screen.queryByText('Test Project 2')).not.toBeInTheDocument();
    });
    
    // Find and click clear button
    const clearButton = screen.getByLabelText('projects.clearSearch');
    fireEvent.click(clearButton);
    
    // Check if all projects are shown again
    await waitFor(() => {
      expect(screen.getByText('Test Project 1')).toBeInTheDocument();
      expect(screen.getByText('Test Project 2')).toBeInTheDocument();
    });
  });
});
