import React from 'react';
import { render, screen } from '@testing-library/react';
import { LoadingOverlay } from '@/components/ui/loading-overlay';

// Mock the useTranslations hook
jest.mock('next-intl', () => ({
  useTranslations: () => (key: string) => {
    const translations: Record<string, string> = {
      'common.generatingAIEstimate': 'Generating AI Estimate',
      'common.pleaseWait': 'Please wait, this may take a moment...'
    };
    return translations[key] || key;
  }
}));

describe('LoadingOverlay component', () => {
  it('renders with default message', () => {
    render(<LoadingOverlay />);
    
    expect(screen.getByText('Generating AI Estimate')).toBeInTheDocument();
    expect(screen.getByText('Please wait, this may take a moment...')).toBeInTheDocument();
  });

  it('renders with custom message', () => {
    render(<LoadingOverlay message="Custom loading message" />);
    
    expect(screen.getByText('Custom loading message')).toBeInTheDocument();
    expect(screen.getByText('Please wait, this may take a moment...')).toBeInTheDocument();
  });

  it('has the correct styling', () => {
    render(<LoadingOverlay />);
    
    // Check for overlay styles
    const overlay = screen.getByText('Generating AI Estimate').closest('div');
    expect(overlay).toHaveClass('fixed inset-0 bg-black/50 backdrop-blur-sm z-50');
    
    // Check for spinner
    const spinner = document.querySelector('.animate-spin');
    expect(spinner).toBeInTheDocument();
  });
});
