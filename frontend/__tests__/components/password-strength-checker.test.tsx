import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import { PasswordStrengthChecker, passwordMeetsRequirements } from '../../components/password-strength-checker';

// Mock translation function
const mockT = (key: string, options?: any) => {
  const translations: Record<string, string> = {
    'auth.passwordRules.title': 'Password must have:',
    'auth.passwordRules.length': 'At least 8 characters',
    'auth.passwordRules.uppercase': 'At least one uppercase letter',
    'auth.passwordRules.lowercase': 'At least one lowercase letter',
    'auth.passwordRules.number': 'At least one number',
    'auth.passwordRules.special': 'At least one special character',
  };
  
  return translations[key] || key;
};

describe('PasswordStrengthChecker Component', () => {
  test('renders all password rules', () => {
    render(<PasswordStrengthChecker password="" t={mockT} />);
    
    expect(screen.getByText('Password must have:')).toBeInTheDocument();
    expect(screen.getByText('At least 8 characters')).toBeInTheDocument();
    expect(screen.getByText('At least one uppercase letter')).toBeInTheDocument();
    expect(screen.getByText('At least one lowercase letter')).toBeInTheDocument();
    expect(screen.getByText('At least one number')).toBeInTheDocument();
    expect(screen.getByText('At least one special character')).toBeInTheDocument();
  });
  
  test('shows correct status for empty password', () => {
    render(<PasswordStrengthChecker password="" t={mockT} />);
    
    // All rules should be unchecked (gray X icons)
    const xIcons = screen.getAllByTestId('x-circle');
    expect(xIcons).toHaveLength(5);
  });
  
  test('shows correct status for partially valid password', () => {
    render(<PasswordStrengthChecker password="Test1" t={mockT} />);
    
    // Length rule should still be unchecked
    expect(screen.getByText('At least 8 characters').closest('li')).toHaveClass('text-gray-500');
    
    // Uppercase, lowercase, and number rules should be checked
    expect(screen.getByText('At least one uppercase letter').closest('li')).toHaveClass('text-green-700');
    expect(screen.getByText('At least one lowercase letter').closest('li')).toHaveClass('text-green-700');
    expect(screen.getByText('At least one number').closest('li')).toHaveClass('text-green-700');
    
    // Special character rule should be unchecked
    expect(screen.getByText('At least one special character').closest('li')).toHaveClass('text-gray-500');
  });
  
  test('shows all rules as checked for valid password', () => {
    render(<PasswordStrengthChecker password="StrongP@ss123" t={mockT} />);
    
    // All rules should be checked (green checkmarks)
    const checkIcons = screen.getAllByTestId('check-circle');
    expect(checkIcons).toHaveLength(5);
  });
});

describe('passwordMeetsRequirements Function', () => {
  test('returns false for empty password', () => {
    expect(passwordMeetsRequirements('')).toBe(false);
  });
  
  test('returns false for password too short', () => {
    expect(passwordMeetsRequirements('Abc1!')).toBe(false);
  });
  
  test('returns false for password without uppercase', () => {
    expect(passwordMeetsRequirements('password123!')).toBe(false);
  });
  
  test('returns false for password without lowercase', () => {
    expect(passwordMeetsRequirements('PASSWORD123!')).toBe(false);
  });
  
  test('returns false for password without number', () => {
    expect(passwordMeetsRequirements('Password!')).toBe(false);
  });
  
  test('returns false for password without special character', () => {
    expect(passwordMeetsRequirements('Password123')).toBe(false);
  });
  
  test('returns true for valid password', () => {
    expect(passwordMeetsRequirements('Password123!')).toBe(true);
    expect(passwordMeetsRequirements('StrongP@ss123')).toBe(true);
    expect(passwordMeetsRequirements('C0mpl3x!P@ssw0rd')).toBe(true);
  });
});
