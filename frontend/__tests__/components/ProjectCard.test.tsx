import React from 'react';
import { render, screen } from '@testing-library/react';
import { ProjectCard } from '@/components/project/project-card';

// Mock the useTranslations hook
jest.mock('next-intl', () => ({
  useTranslations: () => (key: string) => key
}));

// Mock the useRouter hook
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn()
  })
}));

describe('ProjectCard component', () => {
  const mockProject = {
    id: '1',
    name: 'Test Project',
    description: 'This is a test project',
    software_type: 'Web Application',
    technology_stack: 'React, FastAPI',
    created_at: '2025-01-01T00:00:00Z',
    updated_at: '2025-01-02T00:00:00Z',
    user_id: 1,
    is_active: true
  };

  it('renders project information correctly', () => {
    render(<ProjectCard project={mockProject} locale="en" />);
    
    expect(screen.getByText('Test Project')).toBeInTheDocument();
    expect(screen.getByText('This is a test project')).toBeInTheDocument();
    expect(screen.getByText('Web Application')).toBeInTheDocument();
    expect(screen.getByText('React, FastAPI')).toBeInTheDocument();
  });

  it('navigates to project details when clicked', () => {
    const { container } = render(<ProjectCard project={mockProject} locale="en" />);
    
    // Find the link element (could be the whole card or a specific button)
    const linkElement = container.querySelector('a');
    expect(linkElement).toHaveAttribute('href', '/en/projects/1');
  });

  it('formats dates correctly', () => {
    render(<ProjectCard project={mockProject} locale="en" />);
    
    // Check for formatted dates (this will depend on your actual implementation)
    // For example, if you're using a date formatting library:
    expect(screen.getByText(/01\/01\/2025/)).toBeInTheDocument();
  });
});
