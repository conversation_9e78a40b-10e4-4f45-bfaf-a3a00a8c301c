import React from 'react';
import { CheckCircle2, XCircle } from 'lucide-react';

interface PasswordStrengthCheckerProps {
  password: string;
  t: (key: string, options?: any) => string;
  showErrors?: boolean;
}

export const PasswordStrengthChecker: React.FC<PasswordStrengthCheckerProps> = ({ password, t, showErrors = false }) => {
  // Define password rules
  const rules = [
    {
      id: 'length',
      label: t('auth.passwordRules.length', { defaultValue: 'At least 8 characters' }),
      test: (pass: string) => pass.length >= 8
    },
    {
      id: 'uppercase',
      label: t('auth.passwordRules.uppercase', { defaultValue: 'At least one uppercase letter' }),
      test: (pass: string) => /[A-Z]/.test(pass)
    },
    {
      id: 'lowercase',
      label: t('auth.passwordRules.lowercase', { defaultValue: 'At least one lowercase letter' }),
      test: (pass: string) => /[a-z]/.test(pass)
    },
    {
      id: 'number',
      label: t('auth.passwordRules.number', { defaultValue: 'At least one number' }),
      test: (pass: string) => /\d/.test(pass)
    },
    {
      id: 'special',
      label: t('auth.passwordRules.special', { defaultValue: 'At least one special character' }),
      test: (pass: string) => /[!@#$%^&*(),.?":{}|<>]/.test(pass)
    }
  ];

  // Check if all rules pass
  const allRulesPassed = rules.every(rule => rule.test(password));

  return (
    <div className="mt-2 space-y-1 text-sm">
      <ul className="space-y-1 pl-1">
        {rules.map(rule => (
          <li key={rule.id} className="flex items-center gap-2">
            {rule.test(password) ? (
              <CheckCircle2 className="h-4 w-4 text-green-500" data-testid="check-circle" />
            ) : (
              <XCircle 
                className={`h-4 w-4 ${showErrors ? 'text-red-600' : 'text-gray-300'}`} 
                data-testid="x-circle" 
              />
            )}
            <span className={rule.test(password) 
              ? 'text-green-700' 
              : showErrors 
                ? 'text-red-600 font-medium' 
                : 'text-gray-500'}>
              {rule.label}
            </span>
          </li>
        ))}
      </ul>
    </div>
  );
};

// Helper function to check if a password meets all requirements
export const passwordMeetsRequirements = (password: string): boolean => {
  return (
    password.length >= 8 &&
    /[A-Z]/.test(password) &&
    /[a-z]/.test(password) &&
    /\d/.test(password) &&
    /[!@#$%^&*(),.?":{}|<>]/.test(password)
  );
};
