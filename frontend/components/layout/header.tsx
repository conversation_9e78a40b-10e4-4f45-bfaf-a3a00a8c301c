"use client"

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { Button } from "@/components/ui/button"
import { getLocaleFromPathname } from '@/lib/locale'
import { 
  User, 
  LogOut, 
  Settings,
  ChevronDown,
  LayoutGrid
} from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

const Header = () => {
  const [locale, setLocale] = useState('en')
  const [isLoggedIn, setIsLoggedIn] = useState(false)
  const [messages, setMessages] = useState<any>(null)
  const pathname = usePathname()

  useEffect(() => {
    const currentLocale = getLocaleFromPathname(pathname) || 'en'
    setLocale(currentLocale)
    
    const loadMessages = async () => {
      try {
        const msgs = await import(`../../messages/${currentLocale}/index.json`)
        setMessages(msgs.default)
      } catch (error) {
        console.error('Failed to load messages:', error)
      }
    }
    
    loadMessages()
    
    const token = typeof window !== "undefined" ? localStorage.getItem("token") : null
    setIsLoggedIn(!!token)
  }, [pathname])

  const handleLogout = () => {
    localStorage.removeItem("token")
    window.location.href = `/${locale}`
  }

  if (!messages) return null

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 px-4 sm:px-6 lg:px-8">
      <div className="flex h-14 items-center justify-between w-full max-w-7xl mx-auto">
        <div className="flex items-center">
          <Link href={`/${locale}`} className="flex items-center">
            <div className="text-blue-600 mr-1 sm:mr-2">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="sm:w-6 sm:h-6">
                <path d="M3 3v18h18"></path>
                <path d="M18 17V9"></path>
                <path d="M13 17V5"></path>
                <path d="M8 17v-3"></path>
              </svg>
            </div>
            <span className="font-bold text-sm sm:text-base">{messages.app.title}</span>
          </Link>
        </div>
        <div className="flex items-center space-x-2 sm:space-x-3">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="flex items-center text-xs sm:text-sm">
                {locale === 'en' ? '🇺🇸' : locale === 'vi' ? '🇻🇳' : '🇯🇵'}
                <span className="hidden sm:inline ml-1">{locale === 'en' ? 'English' : locale === 'vi' ? 'Tiếng Việt' : '日本語'}</span>
                <ChevronDown className="ml-1 sm:ml-2 h-3 w-3 sm:h-4 sm:w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem asChild>
                 <Link
                  href={{
                    pathname: pathname.replace(/^\/[^\/]+/, '/en'),
                    query: typeof window !== 'undefined' ? Object.fromEntries(new URLSearchParams(window.location.search)) : undefined
                  }}
                  className="flex items-center"
                >
                  🇺🇸 English
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                 <Link
                  href={{
                    pathname: pathname.replace(/^\/[^\/]+/, '/vi'),
                    query: typeof window !== 'undefined' ? Object.fromEntries(new URLSearchParams(window.location.search)) : undefined
                  }}
                  className="flex items-center"
                >
                  🇻🇳 Tiếng Việt
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                 <Link
                  href={{
                    pathname: pathname.replace(/^\/[^\/]+/, '/ja'),
                    query: typeof window !== 'undefined' ? Object.fromEntries(new URLSearchParams(window.location.search)) : undefined
                  }}
                  className="flex items-center"
                >
                  🇯🇵 日本語
                </Link>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          {isLoggedIn ? (
            <DropdownMenu>
              <DropdownMenuTrigger asChild className="min-w-full w-full md:min-w-[250px] md:w-[250px]">
                <Button variant="outline" size="sm" className="flex items-center text-xs sm:text-sm">
                  <User className="h-3 w-3 sm:h-4 sm:w-4" />
                  <span className="hidden sm:inline ml-1">{messages.nav.account}</span>
                  <ChevronDown className="ml-1 sm:ml-2 h-3 w-3 sm:h-4 sm:w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>{messages.nav.accountOptions}</DropdownMenuLabel>
                {/* <DropdownMenuItem asChild>
                  <Link href={`/${locale}/profile`} className="flex items-center">
                    <User className="mr-2 h-4 w-4" />
                    {messages.nav.profile}
                  </Link>
                </DropdownMenuItem> */}
                <DropdownMenuItem asChild>
                  <Link href={`/${locale}/settings`} className="flex items-center">
                    <Settings className="mr-2 h-4 w-4" />
                    {messages.nav.settings}
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem asChild>
                  <Link href={`/${locale}/projects`} className="flex items-center">
                    <LayoutGrid className="mr-2 h-4 w-4" />
                    {messages.nav.projects}
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem asChild>
                  <button 
                    className="flex w-full items-center text-red-600"
                    onClick={handleLogout}
                  >
                    <LogOut className="mr-2 h-4 w-4" />
                    {messages.nav.logout}
                  </button>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          ) : (
            <div className="flex gap-1 sm:gap-2">
              <Button variant="outline" size="sm" className="text-xs sm:text-sm px-2 sm:px-3" asChild>
                <Link href={`/${locale}/login`}>
                  {messages.nav.login}
                </Link>
              </Button>
              <Button size="sm" className="text-xs sm:text-sm px-2 sm:px-3" asChild>
                <Link href={`/${locale}/register`}>
                  {messages.nav.register}
                </Link>
              </Button>
            </div>
          )}
        </div>
      </div>
    </header>
  )
}

export default Header 