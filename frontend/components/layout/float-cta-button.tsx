'use client';

import { useTranslations } from 'next-intl';

export default function FloatingCtaButton() {
  const t = useTranslations('landing.cta');
  const label = t('cta_contact_haposoft');

  return (
    <a
      href="mailto:<EMAIL>"
      className="hapofloat-btn"
      aria-label={label}
      target="_blank"
      rel="noopener noreferrer"
    >
      <svg width="28" height="28" fill="currentColor" viewBox="0 0 24 24" style={{ marginRight: 8 }}>
        <path d="M20 4H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 2v.01L12 13 4 6.01V6h16zm-16 12V8.99l7.99 7.99c.39.39 1.02.39 1.41 0L20 8.99V18H4z"/>
      </svg>
      {label}
    </a>
  );
}
