"use client"

import Link from "next/link"
import { Button } from "@/components/ui/button"
import { CalendarIcon, Code2Icon, PackageIcon, ArrowRight } from "lucide-react"
import { useTranslations } from 'next-intl'
import { usePathname } from 'next/navigation'
import { getLocaleFromPathname } from '@/utils/localeStorage'

interface Project {
  id: number
  name: string
  description: string
  software_type: string
  technology_stack: string
  created_at: string
}

interface ProjectListProps {
  projects: Project[]
}

export default function ProjectList({ projects }: ProjectListProps) {
  const t = useTranslations();
  const pathname = usePathname();
  const currentLocale = getLocaleFromPathname(pathname);
  
  return (
    <div className="bg-white border rounded-lg overflow-hidden shadow-sm">
      <ul className="divide-y divide-gray-200">
        {projects.map((project) => {
          // Format date based on locale
          const formattedDate = new Date(project.created_at).toLocaleDateString(
            currentLocale === 'en' ? 'en-US' : 
            currentLocale === 'vi' ? 'vi-VN' : 
            currentLocale === 'ja' ? 'ja-JP' : 'en-US'
          );
          
          return (
            <li key={project.id} className="p-4 hover:bg-gray-50 transition-colors">
              <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-gray-900">{project.name}</h3>
                  <p className="text-gray-600 text-sm mt-1 line-clamp-2">
                    {project.description || t('projects.noDescription')}
                  </p>
                  
                  <div className="flex flex-wrap gap-4 mt-2">
                    {project.software_type && (
                      <div className="flex items-center text-sm text-gray-500">
                        <PackageIcon className="w-4 h-4 mr-2" />
                        <span>{project.software_type}</span>
                      </div>
                    )}
                    
                    {project.technology_stack && (
                      <div className="flex items-center text-sm text-gray-500">
                        <Code2Icon className="w-4 h-4 mr-2" />
                        <span>{project.technology_stack}</span>
                      </div>
                    )}
                    
                    <div className="flex items-center text-sm text-gray-500">
                      <CalendarIcon className="w-4 h-4 mr-2" />
                      <span>{t('projects.createdAt')}: {formattedDate}</span>
                    </div>
                  </div>
                </div>
                
                <div className="flex mt-4 md:mt-0 space-x-2">
                  <Link href={`/${currentLocale}/projects/${project.id}`} passHref>
                    <Button variant="outline" size="sm">
                      {t('projects.projectInfo')}
                    </Button>
                  </Link>
                  
                  <Link href={`/${currentLocale}/projects/${project.id}/estimate`} passHref>
                    <Button size="sm" className="flex items-center">
                      {t('projects.createEstimate')}
                      <ArrowRight className="ml-1 h-4 w-4" />
                    </Button>
                  </Link>
                </div>
              </div>
            </li>
          );
        })}
      </ul>
    </div>
  );
}
