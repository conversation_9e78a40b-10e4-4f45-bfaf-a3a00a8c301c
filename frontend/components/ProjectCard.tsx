"use client"

import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { CalendarIcon, Code2Icon, PackageIcon } from "lucide-react"
import { useTranslations } from 'next-intl'
import { usePathname } from 'next/navigation'
import { getLocaleFromPathname } from '@/utils/localeStorage'

interface ProjectProps {
  project: {
    id: number
    name: string
    description: string
    software_type: string
    technology_stack: string
    created_at: string
  }
}

export default function ProjectCard({ project }: ProjectProps) {
  const t = useTranslations();
  const pathname = usePathname();
  const currentLocale = getLocaleFromPathname(pathname);
  
  // Format date based on locale
  const formattedDate = new Date(project.created_at).toLocaleDateString(
    currentLocale === 'en' ? 'en-US' : 
    currentLocale === 'vi' ? 'vi-VN' : 
    currentLocale === 'ja' ? 'ja-JP' : 'en-US'
  );
  
  return (
    <div className="bg-white border rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow">
      <div className="p-5">
        <div className="flex justify-between items-start mb-4">
          <h3 className="text-lg font-semibold text-gray-900 truncate">{project.name}</h3>
        </div>
        
        <p className="text-gray-600 text-sm mb-4 line-clamp-2">
          {project.description || t('projects.noDescription')}
        </p>
        
        <div className="flex flex-col gap-2 mb-4">
          {project.software_type && (
            <div className="flex items-center text-sm text-gray-500">
              <PackageIcon className="w-4 h-4 mr-2" />
              <span>{project.software_type}</span>
            </div>
          )}
          
          {project.technology_stack && (
            <div className="flex items-center text-sm text-gray-500">
              <Code2Icon className="w-4 h-4 mr-2" />
              <span>{project.technology_stack}</span>
            </div>
          )}
          
          <div className="flex items-center text-sm text-gray-500">
            <CalendarIcon className="w-4 h-4 mr-2" />
            <span>{t('projects.createdAt')}: {formattedDate}</span>
          </div>
        </div>
        
        <div className="flex justify-between">
          <Link href={`/${currentLocale}/projects/${project.id}`} passHref>
            <Button variant="outline" size="sm" className="mr-2">
              {t('projects.projectInfo')}
            </Button>
          </Link>
          
          <Link href={`/${currentLocale}/projects/${project.id}/estimate`} passHref>
            <Button size="sm">
              {t('projects.createEstimate')}
            </Button>
          </Link>
        </div>
      </div>
    </div>
  )
} 