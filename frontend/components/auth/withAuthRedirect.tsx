'use client';

import { useRouter, usePathname } from 'next/navigation';
import { useEffect, useState } from 'react';

// Add loading component
const LoadingScreen = () => (
  <div className="flex items-center justify-center min-h-screen">
    <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-gray-900"></div>
  </div>
);

export function withAuthRedirect(WrappedComponent: React.ComponentType, redirectPath = '/') {
  return function WithAuthRedirect(props: any) {
    const [isLoading, setIsLoading] = useState(true);
    const router = useRouter();
    const pathname = usePathname();
    const locale = pathname?.split('/')[1] || 'en';

    useEffect(() => {
      // Only run on client side
      if (typeof window === 'undefined') {
        setIsLoading(false);
        return;
      }

      const token = localStorage.getItem('token');
      
      if (token) {
        console.log('targetPath', redirectPath);

        // If already on the redirect path, don't do anything
        if (pathname === `/${locale}${redirectPath}` || pathname === redirectPath) {
          setIsLoading(false);
          return;
        }
        
        // Redirect to home with locale
        const targetPath = `/${locale}${redirectPath}`;
        router.replace(targetPath);
      } else {
        setIsLoading(false);
      }
    }, [router, pathname, locale, redirectPath]);

    // Show loading while checking auth
    if (isLoading) {
      return <LoadingScreen />;
    }

    return <WrappedComponent {...props} />;
  };
}
