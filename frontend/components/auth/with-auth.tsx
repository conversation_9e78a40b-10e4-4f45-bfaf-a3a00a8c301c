'use client';

import { useEffect, useState } from 'react';
import { usePathname } from 'next/navigation';
import { getStoredLocale, getLocaleFromPathname, isLoggedIn } from '@/utils/localeStorage';

export default function withAuth(Component: React.ComponentType<any>) {
  return function ProtectedRoute(props: any) {
    const pathname = usePathname();
    const [loading, setLoading] = useState(true);
    
    // Get current locale from path or localStorage
    const pathLocale = getLocaleFromPathname(pathname);
    const storedLocale = getStoredLocale();
    
    // Prefer path locale, fallback to stored locale
    const currentLocale = pathLocale || storedLocale;

    useEffect(() => {
      // Check if user is logged in
      const loggedIn = isLoggedIn();
      
      // If already logged in and on login/register page, redirect to projects
      if ((pathname.includes('/login') || pathname.includes('/register')) && loggedIn) {
        window.location.href = `/${currentLocale}/projects`;
        return;
      }
      
      // If not logged in and not on login/register page, redirect to login
      if (!loggedIn && !pathname.includes('/login') && !pathname.includes('/register')) {
        window.location.href = `/${currentLocale}/login`;
        return;
      }
      
      setLoading(false);
    }, [pathname, currentLocale]);

    if (loading) {
      return <div className="flex items-center justify-center min-h-screen">Loading...</div>;
    }

    return <Component {...props} />;
  };
} 