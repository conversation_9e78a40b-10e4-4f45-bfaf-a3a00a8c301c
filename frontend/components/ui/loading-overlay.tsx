"use client"

import { useTranslations } from "next-intl"

interface LoadingOverlayProps {
  message?: string
}

export function LoadingOverlay({ message }: LoadingOverlayProps) {
  const t = useTranslations()
  
  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center">
      <div className="bg-white p-8 rounded-lg shadow-lg max-w-md w-full text-center">
        <div className="flex flex-col items-center justify-center space-y-4">
          <div className="w-16 h-16 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
          <h3 className="text-xl font-semibold">{message || t('common.loading')}</h3>
          <p className="text-gray-500">{t('common.pleaseWait')}</p>
        </div>
      </div>
    </div>
  )
}
