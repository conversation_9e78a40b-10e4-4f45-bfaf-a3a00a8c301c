"use client"

import { useState, useEffect, useCallback } from "react"
import { Input } from "@/components/ui/input"

// Custom hook for number input validation
const useNumberInput = () => {
  const validateAndFormatNumber = useCallback((value: string | number): string => {
    // Convert to string first
    let stringValue = String(value);
    
    // Remove any non-digit characters except decimal point
    stringValue = stringValue.replace(/[^0-9.]/g, '');
    
    // Ensure only one decimal point
    const parts = stringValue.split('.');
    if (parts.length > 2) {
      stringValue = parts[0] + '.' + parts.slice(1).join('');
    }
    
    // Limit to 9 characters total (including decimal point)
    if (stringValue.length > 9) {
      stringValue = stringValue.substring(0, 9);
    }
    
    return stringValue;
  }, []);

  const parseNumber = useCallback((value: string): number => {
    const parsed = parseFloat(value);
    return isNaN(parsed) || parsed < 0 ? 0 : parsed;
  }, []);

  return { validateAndFormatNumber, parseNumber };
};

// CustomNumberInput component for reusable number inputs
interface CustomNumberInputProps {
  value: number;
  onChange: (value: number) => void;
  onBlur?: (value: number) => void;
  className?: string;
  step?: string;
  placeholder?: string;
  disabled?: boolean;
}

const CustomNumberInput: React.FC<CustomNumberInputProps> = ({
  value,
  onChange,
  onBlur,
  className = "",
  step = "0.1",
  placeholder,
  disabled = false
}) => {
  const { validateAndFormatNumber, parseNumber } = useNumberInput();
  const [displayValue, setDisplayValue] = useState<string>(String(value || ''));

  // Update display value when prop value changes
  useEffect(() => {
    setDisplayValue(String(value || ''));
  }, [value]);

  // Handle key down to prevent letters and +/- signs
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    // Allow keyboard shortcuts (Ctrl+C, Ctrl+V, Ctrl+A, Ctrl+X, Ctrl+Z)
    if (e.ctrlKey || e.metaKey) {
      return; // Allow all Ctrl/Cmd shortcuts
    }

    // Prevent letters (a-z, A-Z) and +/- signs
    if (e.key.length === 1 && (/[a-zA-Z]/.test(e.key) || e.key === '+' || e.key === '-')) {
      // Allow if user has selected text (they want to replace it)
      const input = e.target as HTMLInputElement;
      if (input.selectionStart !== input.selectionEnd) {
        return; // Allow replacement of selected text
      }
      e.preventDefault();
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value;
    const validatedValue = validateAndFormatNumber(inputValue);
    setDisplayValue(validatedValue);

    // Call onChange with parsed number
    const numericValue = parseNumber(validatedValue);
    onChange(numericValue);
  };

  const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    const numericValue = parseNumber(displayValue);
    setDisplayValue(String(numericValue || ''));

    if (onBlur) {
      onBlur(numericValue);
    }
  };

  return (
    <Input
      type="number"
      step={step}
      value={displayValue}
      onChange={handleChange}
      onBlur={handleBlur}
      onKeyDown={handleKeyDown}
      className={className}
      placeholder={placeholder}
      disabled={disabled}
      min="0"
      max="999999999"
    />
  );
};

export { CustomNumberInput };
