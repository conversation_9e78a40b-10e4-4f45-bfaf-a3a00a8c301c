'use client';

import { usePathname } from 'next/navigation';
import Link from 'next/link';
import { useTranslations } from 'next-intl';

type PolicyLink = {
  id: string;
  path: string;
  file: string;
};

const policyLinks: PolicyLink[] = [
  {
    id: 'privacy',
    path: '/privacy-policy',
    file: 'Haposoft_PRIVACY_POLICY.pdf'
  },
  {
    id: 'terms',
    path: '/terms-of-service',
    file: 'Haposoft_TERMS_OF_SERVICE.pdf'
  },
  {
    id: 'cookies',
    path: '/cookie-policy',
    file: 'Haposoft_COOKIE_POLICY.pdf'
  },
];

export default function PolicySidebar() {
  const pathname = usePathname();
  const t = useTranslations('footer.legal');

  return (
    <div className="w-full">
      <div className="sticky top-24 lg:top-28">
        <div className="mb-6 pb-3 border-b border-gray-200">
          <h2 className="text-[15px] lg:text-[16px] font-bold text-gray-900">Terms & Policies</h2>
        </div>
        <nav className="-ml-1">
          <ul className="space-y-3">
            {policyLinks.map((item) => (
              <li key={item.id} className="relative">
                <Link
                  href={item.path}
                  className={`block py-1.5 px-1 text-[14px] lg:text-[15px] transition-colors duration-200 ${
                    pathname.includes(item.path)
                      ? 'text-gray-900 font-medium before:absolute before:left-[-12px] before:top-0 before:h-full before:w-1 before:bg-black before:rounded-r'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  {t(item.id as any)}
                </Link>
              </li>
            ))}
          </ul>
        </nav>
      </div>
    </div>
  );
}
