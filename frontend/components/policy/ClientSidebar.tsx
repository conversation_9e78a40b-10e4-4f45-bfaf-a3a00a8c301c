'use client';

import dynamic from 'next/dynamic';

const PolicySidebar = dynamic(
  () => import('./PolicySidebar'),
  { 
    ssr: false,
    loading: () => (
      <div className="w-72 pr-4">
        <div className="h-7 bg-gray-200 rounded w-3/4 mb-6"></div>
        <div className="space-y-3">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="h-5 bg-gray-100 rounded w-full"></div>
          ))}
        </div>
      </div>
    )
  }
);

export default function ClientSidebar() {
  return (
    <div className="w-64 lg:w-72 pr-4 border-r border-gray-100">
      <PolicySidebar />
    </div>
  );
}
