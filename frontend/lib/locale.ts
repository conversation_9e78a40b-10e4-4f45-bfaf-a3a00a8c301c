import { locales, defaultLocale, Locale } from '@/i18n/settings'

/**
 * Extract locale from pathname
 * @param pathname - The current pathname from usePathname() hook
 * @returns The locale string (e.g., 'en', 'vi', 'ja')
 */
export function getLocaleFromPathname(pathname: string): Locale {
  const segments = pathname.split('/')
  const firstSegment = segments[1]
  
  if (firstSegment && locales.includes(firstSegment as Locale)) {
    return firstSegment as Locale
  }
  
  return defaultLocale
} 