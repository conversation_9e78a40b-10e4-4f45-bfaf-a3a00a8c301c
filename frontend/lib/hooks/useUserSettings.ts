"use client";

import { useState, useEffect } from 'react';
import { UserSettings, CurrencyType } from '@/lib/types';

/**
 * Hook cung cấp các chức năng liên quan đến thiết lập người dùng
 * Tự động xác định mức giá và đơn vị tiền tệ mặc định dựa trên ngôn ngữ
 * @param locale Ngôn ngữ hiện tại (en, vi, ja)
 * @returns Thiết lập người dùng và các hàm tiện ích
 */
export default function useUserSettings(locale?: string) {
  const [userSettings, setUserSettings] = useState<UserSettings | null>(null);
  const [loading, setLoading] = useState(true);

  // Hàm lấy currency symbol
  const getCurrencySymbol = (currency: CurrencyType = "USD") => {
    switch (currency) {
      case "USD": return "$";
      case "VND": return "₫";
      case "JPY": return "¥";
      default: return "$";
    }
  };

  // Hàm lấy currency mặc định dựa trên locale
  const getDefaultCurrency = (userLocale: string = 'en'): CurrencyType => {
    switch (userLocale) {
      case 'vi': return 'VND';
      case 'ja': return 'JPY';
      default: return 'USD';
    }
  };

  // Hàm lấy role rates mặc định dựa trên locale
  const getDefaultRoleRates = (userLocale: string = 'en') => {
    switch (userLocale) {
      case 'vi':
        return {
          "PM": 4000000,
          "Developer": 3000000,
          "Designer": 3000000,
          "Tester": 3000000,
          "Backend": 3000000,
          "Frontend": 3000000,
          "UI": 3000000,
          "UX": 3000000,
          "QA": 3000000,
          "DevOps": 3500000,
          "BA": 4000000,
          "Architect": 5000000
        };
      case 'ja':
        return {
          "PM": 23000,
          "Developer": 18000,
          "Designer": 18000,
          "Tester": 17500,
          "Backend": 18000,
          "Frontend": 18000,
          "UI": 18000,
          "UX": 18000,
          "QA": 17500,
          "DevOps": 20000,
          "BA": 23000,
          "Architect": 25000
        };
      default:
        return {
          "PM": 200,
          "Developer": 150,
          "Designer": 150,
          "Tester": 150,
          "Backend": 150,
          "Frontend": 150,
          "UI": 150,
          "UX": 150,
          "QA": 150,
          "DevOps": 180,
          "BA": 200,
          "Architect": 250
        };
    }
  };

  // Hàm fetch settings từ API hoặc sử dụng mặc định nếu không có 
  const fetchUserSettings = async () => {
    try {
      setLoading(true);
      
      // Kiểm tra token
      const token = typeof window !== 'undefined' ? localStorage.getItem("token") : null;
      if (!token) {
        // Nếu không có token, tạo settings mặc định dựa trên locale
        const defaultSettings = {
          id: 0,
          user_id: 0,
          currency: getDefaultCurrency(locale),
          role_rates: getDefaultRoleRates(locale)
        };
        setUserSettings(defaultSettings);
        setLoading(false);
        return;
      }

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/users/settings`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        // Nếu có response nhưng không có role_rates hoặc role_rates là object rỗng
        if (!data.role_rates || Object.keys(data.role_rates).length === 0) {
          data.role_rates = getDefaultRoleRates(locale);
        }
        // Nếu không có currency, đặt mặc định theo locale
        if (!data.currency) {
          data.currency = getDefaultCurrency(locale);
        }
        setUserSettings(data);
      } else {
        console.error("Failed to fetch user settings");
        // Đặt settings mặc định nếu fetch thất bại
        const defaultSettings = {
          id: 0,
          user_id: 0,
          currency: getDefaultCurrency(locale),
          role_rates: getDefaultRoleRates(locale)
        };
        setUserSettings(defaultSettings);
      }
    } catch (error) {
      console.error("Error fetching user settings:", error);
      // Đặt settings mặc định nếu lỗi
      const defaultSettings = {
        id: 0,
        user_id: 0,
        currency: getDefaultCurrency(locale),
        role_rates: getDefaultRoleRates(locale)
      };
      setUserSettings(defaultSettings);
    } finally {
      setLoading(false);
    }
  };

  // Effect để load settings khi component mount hoặc locale thay đổi
  useEffect(() => {
    fetchUserSettings();
  }, [locale]);

  // Hàm refresh settings
  const refreshSettings = async () => {
    await fetchUserSettings();
  };

  return {
    userSettings,
    loading,
    refreshSettings,
    getCurrencySymbol,
    getDefaultCurrency,
    getDefaultRoleRates
  };
} 