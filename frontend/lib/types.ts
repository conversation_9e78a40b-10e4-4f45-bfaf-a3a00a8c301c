// Interface for Project
export interface Project {
  id: number;
  name: string;
  description: string;
  software_type: string;
  technology_stack: string;
  requirements: string;
  user_id: number;
  created_at: string;
  updated_at: string;
  estimation_results: any;
}

// Interfaces for Estimation
export interface DevelopmentItem {
  feature: string;
  description: string;
  backend_effort: number;
  frontend_effort: number;
  unit_test_effort: number;
  priority?: "MUST_HAVE" | "NICE_TO_HAVE";
  is_selected?: boolean;
}

export interface SummaryItem {
  category: string;
  effort: number;
  description: string;
}

export interface EstimateResponse {
  development_items: DevelopmentItem[];
  summary_items: SummaryItem[];
  grand_total: number;
}

// User settings interfaces
export type CurrencyType = "USD" | "VND" | "JPY";

export interface RoleRate {
  [role: string]: number;
}

export interface UserSettings {
  id?: number;
  user_id: number;
  currency: CurrencyType;
  role_rates: RoleRate;
}

export interface PasswordChangeRequest {
  current_password: string;
  new_password: string;
  confirm_password: string;
}

// Currency formatting
export const CurrencySymbols: Record<CurrencyType, string> = {
  USD: "$",
  VND: "₫",
  JPY: "¥"
};

export const formatCurrency = (amount: number, currency: CurrencyType): string => {
  switch (currency) {
    case "USD":
      return `$${amount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
    case "VND":
      return `${Math.round(amount).toLocaleString('vi-VN')}₫`;
    case "JPY":
      return `¥${Math.round(amount).toLocaleString('ja-JP')}`;
    default:
      return `$${amount.toLocaleString()}`;
  }
}; 