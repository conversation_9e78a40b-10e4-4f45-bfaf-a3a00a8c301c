import { defaultLocale } from '@/i18n/settings';

export const LOCALE_STORAGE_KEY = 'app_locale';

export function getStoredLocale(): string {
  if (typeof window === 'undefined') return defaultLocale;
  
  const storedLocale = localStorage.getItem(LOCALE_STORAGE_KEY);
  return storedLocale || defaultLocale;
}

export function setStoredLocale(locale: string): void {
  if (typeof window === 'undefined') return;
  
  localStorage.setItem(LOCALE_STORAGE_KEY, locale);
}

export function getLocaleFromPathname(pathname: string): string {
  if (!pathname) return defaultLocale;
  
  // Path format: /en/some/path or /vi/some/path
  const paths = pathname.split('/');
  return paths[1] || defaultLocale;
}

export function isLoggedIn(): boolean {
  if (typeof window === 'undefined') return false;
  
  return !!localStorage.getItem('token');
} 