"use client"

import React from 'react'
import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import Link from "next/link"
import { useRouter, usePara<PERSON> } from "next/navigation"
import { useTranslations } from 'next-intl'
import { Label } from '@/components/ui/label'
import { FcGoogle } from 'react-icons/fc'
import { BarChart3, UserPlus, AlertCircle, CheckCircle } from 'lucide-react'
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Checkbox } from "@/components/ui/checkbox"
import { PasswordStrength<PERSON>he<PERSON>, passwordMeetsRequirements } from "@/components/password-strength-checker"
import { withAuthRedirect } from '@/components/auth/withAuthRedirect'

// Add TypeScript interface for Google credential response
interface GoogleCredentialResponse {
  credential: string;
  select_by: string;
}

// Add type definition for window.google
declare global {
  interface Window {
    google?: {
      accounts: {
        id: {
          initialize: (config: any) => void;
          renderButton: (element: HTMLElement, options: any) => void;
          prompt: () => void;
        };
      };
    };
  }
}

function RegisterPage() {
  const [loading, setLoading] = useState(false)
  const [googleLoading, setGoogleLoading] = useState(false)
  const [error, setError] = useState('')
  const [step, setStep] = useState<'register' | 'verify' | 'success'>('register')
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [passwordConfirm, setPasswordConfirm] = useState('')
  const [verificationCode, setVerificationCode] = useState('')
  const [emailSent, setEmailSent] = useState(false)
  const [agreeTerms, setAgreeTerms] = useState(false)
  const [passwordFocused, setPasswordFocused] = useState(false)
  const [formSubmitted, setFormSubmitted] = useState(false)
  
  const router = useRouter()
  const t = useTranslations()
  const params = useParams()
  const locale = params.locale as string
  
  // Google Sign-In initialization
  useEffect(() => {
    // Load Google Identity Services script
    const loadGoogleScript = () => {
      const script = document.createElement('script')
      script.src = 'https://accounts.google.com/gsi/client'
      script.async = true
      script.defer = true
      document.body.appendChild(script)
      return script
    }
    
    const script = loadGoogleScript()
    
    return () => {
      // Cleanup script when component unmounts
      if (script) {
        document.body.removeChild(script)
      }
    }
  }, [])
  
  // Initialize Google Sign-In when window.google is available
  useEffect(() => {
    const initializeGoogleSignIn = () => {
      if (window.google) {
        const clientId = process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID;
        
        if (!clientId) {
          setError(t('auth.googleConfigError', { defaultValue: 'Google Sign-In configuration error. Please contact support.' }));
          return;
        }
        
        window.google.accounts.id.initialize({
          client_id: clientId,
          callback: handleGoogleSignIn,
          auto_select: false,
        });
      } else {
        // If not available yet, try again after a short delay
        setTimeout(initializeGoogleSignIn, 500);
      }
    };
    
    // Initialize after a delay to ensure the script is loaded
    const timer = setTimeout(initializeGoogleSignIn, 1000);
    
    return () => clearTimeout(timer);
  }, [t])

  // Handle Google Sign-In response
  async function handleGoogleSignIn(response: GoogleCredentialResponse) {
    try {
      setGoogleLoading(true)
      setError('')
      
      // Extract JWT token from the response
      const token = response.credential
      
      // Decode the JWT to get user information
      const base64Url = token.split('.')[1]
      const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/')
      const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
        return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2)
      }).join(''))
      
      const { email: googleEmail, sub, name, picture } = JSON.parse(jsonPayload)
      
      // Send to backend
      const apiResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/auth/google`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: googleEmail,
          google_id: sub,
          token,
          name,
          picture
        }),
      })
      
      const data = await apiResponse.json()
      
      if (!apiResponse.ok) {
        throw new Error(data.detail || t('auth.registerFailed'))
      }
      
      // Store token and redirect
      localStorage.setItem('token', data.access_token)
      localStorage.setItem('locale', locale)
      router.push(`/${locale}/projects`)
    } catch (err: any) {
      console.error("Google registration error:", err)
      setError(err.message)
      setGoogleLoading(false)
    }
  }
  
  // Manual Google Sign-In trigger
  function handleGoogleSignInManual() {
    setGoogleLoading(true)
    if (window.google) {
      window.google.accounts.id.prompt()
    } else {
      setError(t('auth.googleNotAvailable', { defaultValue: 'Google Sign-In is not available at the moment. Please try again later.' }))
      setGoogleLoading(false)
    }
  }

  async function handleSubmit(event: React.FormEvent<HTMLFormElement>) {
    event.preventDefault()
    setLoading(true)
    setError('')

    const emailValue = email.trim()
    
    if (!emailValue || emailValue.length === 0) {
      setError(t('auth.invalidEmail', { defaultValue: 'Please enter a valid email address' }))
      setLoading(false)
      return
    }

    // Validate passwords match
    if (password !== passwordConfirm) {
      setError(t('auth.passwordsDontMatch', {defaultValue: 'Passwords do not match'}))
      setLoading(false)
      return
    }
    
    // Validate password strength
    if (!passwordMeetsRequirements(password)) {
      setError('')
      setLoading(false)
      setFormSubmitted(true)
      return
    }

    // Validate terms agreement
    if (!agreeTerms) {
      setError(t('auth.mustAgreeTerms', {defaultValue: 'You must agree to the terms and conditions'}))
      setLoading(false)
      return
    }

    try {
      // Gửi yêu cầu đăng ký 
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/auth/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: emailValue,
          password,
          password_confirm: passwordConfirm,
          language: locale,
        }),
      })

      // Clone response trước khi đọc body
      const responseClone = response.clone();
      
      // Thử phân tích JSON từ response
      let data;
      try {
        data = await response.json();
      } catch (jsonError) {
        console.error('Error parsing JSON response:', jsonError);
        // Nếu không phải JSON, lấy text từ bản clone
        try {
          const textResponse = await responseClone.text();
          console.error('Raw response:', textResponse);
        } catch (textError) {
          console.error('Failed to get response text:', textError);
        }
        throw new Error(t('auth.serverError', {defaultValue: 'Server error. Please try again later.'}));
      }

      if (!response.ok) {
        // Prioritize getting messages (validate errors returned from backend)
        if (data.messages && Array.isArray(data.messages)) {
          // Merge all messages (or show individual messages if desired)
          throw new Error(data.messages.join(', '));
        } else if (data.detail) {
          // Handle other errors as usual
          if (typeof data.detail === 'object') {
            if (Array.isArray(data.detail)) {
              // FastAPI validation error format
              const errors = data.detail.map((err: any) =>
                `${err.loc[1]}: ${err.msg}`
              ).join(', ');
              throw new Error(errors);
            } else {
              // For other cases, convert object to string
              throw new Error(JSON.stringify(data.detail));
            }
          } else {
            throw new Error(data.detail);
          }
        } else {
          throw new Error(t('auth.registerFailed'));
        }
      }

      // Save email for use in next steps
      setEmail(emailValue)

      // Move to verification step
      setStep('verify')
      setLoading(false)
    } catch (err: any) {
      console.error("Registration error:", err);
      setError(err.message)
      setLoading(false)
    }
  }

  async function handleVerification(event: React.FormEvent<HTMLFormElement>) {
    event.preventDefault()
    setLoading(true)
    setError('')

    try {
      // Send verification code
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/auth/verify-email`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          code: verificationCode,
        }),
      })

      // Clone response trước khi đọc body
      const responseClone = response.clone();
      
      // Try to parse JSON from response
      let data;
      try {
        data = await response.json();
      } catch (jsonError) {
        console.error('Error parsing JSON response:', jsonError);
        // If not JSON, get text from clone
        try {
          const textResponse = await responseClone.text();
          console.error('Raw response:', textResponse);
        } catch (textError) {
          console.error('Failed to get response text:', textError);
        }
        throw new Error(t('auth.serverError', {defaultValue: 'Server error. Please try again later.'}));
      }

      if (!response.ok) {
        if (data.detail) {
          throw new Error(typeof data.detail === 'object' ? JSON.stringify(data.detail) : data.detail);
        } else {
          throw new Error(t('auth.verificationFailed', {defaultValue: 'Verification failed'}));
        }
      }

      // Verification successful, move to success step
      setStep('success')
      
      // Wait 3 seconds then redirect to login page
      setTimeout(() => {
        router.push(`/${locale}/login?verified=true`)
      }, 3000)
    } catch (err: any) {
      console.error("Verification error:", err);
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  // Resend verification code
  async function resendVerificationCode() {
    setLoading(true)
    setError('')

    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/auth/resend-verification`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
        }),
      })

      // Clone response trước khi đọc body
      const responseClone = response.clone();
      
      // Try to parse JSON from response
      let data;
      try {
        data = await response.json();
      } catch (jsonError) {
        console.error('Error parsing JSON response:', jsonError);
        // If not JSON, get text from clone
        try {
          const textResponse = await responseClone.text();
          console.error('Raw response:', textResponse);
        } catch (textError) {
          console.error('Failed to get response text:', textError);
        }
        throw new Error(t('auth.serverError', {defaultValue: 'Server error. Please try again later.'}));
      }

      if (!response.ok) {
        if (data.detail) {
          throw new Error(typeof data.detail === 'object' ? JSON.stringify(data.detail) : data.detail);
        } else {
          throw new Error(t('auth.resendFailed', {defaultValue: 'Failed to resend verification code'}));
        }
      }

      setEmailSent(true)
    } catch (err: any) {
      console.error("Resend error:", err);
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen flex flex-col md:flex-row">
      {/* Left side - Branding & Info */}
      <div className="w-full md:w-1/2 bg-gradient-to-br from-blue-600 to-indigo-700 text-white p-8 flex flex-col">
        <div className="flex items-center mb-12">
          <div className="w-10 h-10 bg-white rounded-md flex items-center justify-center mr-3">
            <BarChart3 className="text-blue-600" />
          </div>
          <Link href={`/${locale}`} className="text-xl font-bold">HapoEst</Link>
        </div>
        
        <div className="flex-grow flex flex-col justify-center max-w-md mx-auto">
          <h1 className="text-3xl md:text-4xl font-bold mb-6">
            {t('app.title')} - {t('nav.register')}
          </h1>
          <p className="text-xs md:text-sm mb-8 text-blue-100">
            {t('app.description')}
          </p>
          <div className="hidden md:block">
            <div className="flex justify-center items-center mb-6">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="320"
                height="240"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="text-blue-100"
              >
                <rect width="16" height="20" x="4" y="2" rx="2" />
                <path d="M8 2v4" />
                <path d="M16 2v4" />
                <path d="M16 14H8" />
                <path d="M16 18H8" />
                <path d="M8 10h4" />
              </svg>
            </div>
            <div className="flex justify-center gap-4">
              <div className="w-24 h-24 bg-blue-500/30 rounded-lg flex items-center justify-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="40"
                  height="40"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="1.5"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="text-blue-100"
                >
                  <path d="M3 3v18h18" />
                  <path d="m19 9-5 5-4-4-3 3" />
                </svg>
              </div>
              <div className="w-24 h-24 bg-blue-500/30 rounded-lg flex items-center justify-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="40"
                  height="40"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="1.5"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="text-blue-100"
                >
                  <circle cx="12" cy="12" r="10" />
                  <path d="m9 9 6 6" />
                  <path d="m15 9-6 6" />
                </svg>
              </div>
              <div className="w-24 h-24 bg-blue-500/30 rounded-lg flex items-center justify-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="40"
                  height="40"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="1.5"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="text-blue-100"
                >
                  <path d="M16 16h6" />
                  <path d="M19 13v6" />
                  <path d="M12 15c-1.1 0-2-.9-2-2 0-1.1.9-2 2-2" />
                  <rect width="8" height="8" x="2" y="9" rx="2" />
                  <path d="M7 4c0-1.7 1.3-3 3-3h8v4h-4" />
                </svg>
              </div>
            </div>
          </div>
        </div>
        
        <div className="mt-auto text-sm text-blue-200">
          © 2025 Hapoest - {t('footer.allRightsReserved')}
        </div>
      </div>
      
      {/* Right side - Register/Verify Form */}
      <div className="w-full md:w-1/2 flex flex-col items-center justify-center p-8 bg-white">
        <div className="w-full max-w-md space-y-6">
          {step === 'register' && (
            <>
              <div className="flex flex-col space-y-2 text-center">
                <h1 className="text-2xl font-semibold tracking-tight">
                  {t("auth.registerTitle")}
                </h1>
                <p className="text-xs md:text-sm text-muted-foreground">
                  {t("auth.registerSubtitle")}
                </p>
              </div>
              
              <form onSubmit={handleSubmit} className="space-y-4" data-hs-ignore="true">
                <div className="space-y-2">
                  <Label htmlFor="email">{t("auth.email")}</Label>
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    placeholder="<EMAIL>"
                    required
                    className="w-full"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="password">{t("auth.password")}</Label>
                  <Input
                    id="password"
                    name="password"
                    type="password"
                    required
                    className="w-full"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    onFocus={() => setPasswordFocused(true)}
                    onBlur={() => setPasswordFocused(password.length > 0)}
                  />
                  <p className="text-xs text-gray-500">{t('auth.passwordRequirements', {defaultValue: 'At least 8 characters'})}</p>
                  {/* Show password strength checker when password field is focused or has content */}
                  {(passwordFocused || formSubmitted) && (
                    <PasswordStrengthChecker 
                      password={password} 
                      t={t} 
                      showErrors={formSubmitted}
                    />
                  )}
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="passwordConfirm">{t("auth.confirmPassword")}</Label>
                  <Input
                    id="passwordConfirm"
                    name="passwordConfirm"
                    type="password"
                    required
                    className="w-full"
                    value={passwordConfirm}
                    onChange={(e) => setPasswordConfirm(e.target.value)}
                  />
                  
                  {/* Show password match indicator when both fields have content */}
                  {password && passwordConfirm && (
                    <div className="mt-1 flex items-center gap-2 text-sm">
                      {password === passwordConfirm ? (
                        <>
                          <CheckCircle className="h-4 w-4 text-green-500" />
                          <span className="text-green-700">{t('auth.passwordsMatch', { defaultValue: 'Passwords match' })}</span>
                        </>
                      ) : (
                        <>
                          <AlertCircle className="h-4 w-4 text-red-500" />
                          <span className="text-red-700">{t('auth.passwordsDoNotMatch', { defaultValue: 'Passwords do not match' })}</span>
                        </>
                      )}
                    </div>
                  )}
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox 
                    id="terms" 
                    checked={agreeTerms} 
                    onCheckedChange={(checked) => setAgreeTerms(checked as boolean)} 
                  />
                  <label
                    htmlFor="terms"
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    {t('auth.agreeToTerms', {defaultValue: 'I agree to the terms and conditions'})}
                  </label>
                </div>
                
                {error && (
                  <div className="text-sm text-red-500 p-2 bg-red-50 rounded-md">
                    {error}
                  </div>
                )}
                
                <Button type="submit" className="w-full bg-blue-600 hover:bg-blue-700" disabled={loading}>
                  {loading ? t("common.loading") : t("auth.register")}
                </Button>
              </form>
              
              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <span className="w-full border-t" />
                </div>
                <div className="relative flex justify-center text-xs uppercase">
                  <span className="bg-white px-2 text-muted-foreground">
                    {t("common.or")}
                  </span>
                </div>
              </div>
              
              {/* Google Sign-In button */}
              <Button 
                variant="outline" 
                type="button" 
                className="w-full" 
                onClick={handleGoogleSignInManual}
                disabled={googleLoading}
              >
                <FcGoogle className="mr-2 h-4 w-4" />
                {googleLoading ? t("common.loading") : t("auth.googleRegister", {defaultValue: "Sign up with Google"})}
              </Button>
              
              <div className="text-center text-sm">
                {t("auth.alreadyAccount")}{" "}
                <Link 
                  href={`/${locale}/login`} 
                  className="text-blue-600 hover:text-blue-800 font-medium"
                >
                  {t("auth.login")}
                </Link>
              </div>
            </>
          )}

          {step === 'verify' && (
            <>
              <div className="flex flex-col space-y-2 text-center">
                <h1 className="text-2xl font-semibold tracking-tight">
                  {t('auth.verifyEmail', {defaultValue: 'Verify Your Email'})}
                </h1>
                <p className="text-xs md:text-sm text-muted-foreground">
                  {t('auth.verifyEmailSubtitle', {defaultValue: 'Enter the verification code sent to your email'})}
                </p>
              </div>

              {emailSent && (
                <Alert className="bg-blue-50 border-blue-200">
                  <CheckCircle className="h-4 w-4 text-blue-600" />
                  <AlertTitle className="text-blue-800">
                    {t('auth.emailSent', {defaultValue: 'Email Sent'})}
                  </AlertTitle>
                  <AlertDescription className="text-blue-700">
                    {t('auth.emailSentDescription', {defaultValue: 'We\'ve sent a verification code to your email.'})}
                  </AlertDescription>
                </Alert>
              )}
              
              <form onSubmit={handleVerification} className="space-y-4" data-hs-ignore="true">
                <div className="space-y-2">
                  <Label htmlFor="verificationCode">
                    {t('auth.verificationCode', {defaultValue: 'Verification Code'})}
                  </Label>
                  <Input
                    id="verificationCode"
                    value={verificationCode}
                    onChange={(e) => setVerificationCode(e.target.value)}
                    placeholder="123456"
                    required
                    className="w-full text-center text-lg tracking-widest"
                  />
                </div>
                
                {error && (
                  <div className="text-sm text-red-500 p-2 bg-red-50 rounded-md">
                    {error}
                  </div>
                )}
                
                <Button type="submit" className="w-full bg-blue-600 hover:bg-blue-700" disabled={loading}>
                  {loading ? t("common.loading") : t('auth.verify', {defaultValue: 'Verify'})}
                </Button>

                <div className="text-center text-sm">
                  <button 
                    type="button" 
                    onClick={resendVerificationCode}
                    className="text-blue-600 hover:text-blue-800 font-medium"
                    disabled={loading}
                  >
                    {t('auth.resendCode', {defaultValue: 'Resend verification code'})}
                  </button>
                </div>
              </form>
            </>
          )}

          {step === 'success' && (
            <div className="text-center space-y-4">
              <div className="mx-auto bg-green-100 rounded-full w-16 h-16 flex items-center justify-center">
                <CheckCircle className="h-8 w-8 text-green-600" />
              </div>
              <h2 className="text-xl font-semibold">
                {t('auth.registrationSuccess', {defaultValue: 'Registration Successful'})}
              </h2>
              <p className="text-gray-500">
                {t('auth.redirectingToLogin', {defaultValue: 'Your account has been created successfully. Redirecting to login page...'})}
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
export default withAuthRedirect(RegisterPage);
