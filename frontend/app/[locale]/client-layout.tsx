"use client"

import { LanguageSwitcher } from '@/components/ui/language-switcher'
import { cn } from '@/lib/utils'
import { inter } from '@/lib/fonts'
import Link from 'next/link'
import Image from 'next/image'

export default function ClientLayout({
  children,
  locale,
}: {
  children: React.ReactNode
  locale: string
}) {
  return (
    <html lang={locale}>
      <body className={cn("min-h-screen bg-background font-sans antialiased", inter.className)}>
        <div className="relative flex min-h-screen flex-col">
          <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
            <div className="container flex h-14 items-center">
              <Link href={`/${locale}`} className="flex items-center space-x-2">
                <Image 
                  src="/logo.png" 
                  alt="Hapoest Logo" 
                  width={32} 
                  height={32}
                  className="h-8 w-auto"
                />
                <span className="font-bold">Hapoest</span>
              </Link>
              <div className="flex flex-1 items-center justify-end space-x-4">
                <LanguageSwitcher />
              </div>
            </div>
          </header>
          <div className="flex-1">{children}</div>
        </div>
      </body>
    </html>
  )
} 