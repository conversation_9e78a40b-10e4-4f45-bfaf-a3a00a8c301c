'use client'

import Link from 'next/link'
import { useParams } from 'next/navigation'
import { useEffect, useState } from 'react'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { 
  ArrowRight, 
  ChevronDown, 
  LineChart, 
  Users, 
  Clock, 
  DollarSign, 
  BarChart3, 
  CheckCircle2,
  LayoutGrid,
  User,
  LogOut,
  Settings
} from 'lucide-react'
import Image from 'next/image'

export default function Home() {
  const { locale = 'en' } = useParams()
  const [messages, setMessages] = useState<any>(null)
  const [isLoggedIn, setIsLoggedIn] = useState(false)

  // Helper function to get Haposoft URL based on locale
  const getHaposoftUrl = (currentLocale: string) => {
    return currentLocale === 'ja' ? 'https://haposoft.com/ja' : 'https://haposoft.com/en'
  }

  useEffect(() => {
    const loadMessages = async () => {
      try {
        const msgs = await import(`../../messages/${locale}/index.json`)
        setMessages(msgs.default)
      } catch (error) {
        console.error('Failed to load messages:', error)
      }
    }
    
    loadMessages()
    
    // Check if user is logged in
    const token = localStorage.getItem('token')
    setIsLoggedIn(!!token)
  }, [locale])

  if (!messages) return <div>Loading...</div>
  
  return (
    <div className="min-h-screen flex flex-col overflow-hidden w-full">

      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-600 to-indigo-700 text-white py-20 w-full">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full overflow-hidden">
          <div className="flex flex-col md:flex-row items-center justify-between">
            <div className="md:w-1/2 mb-10 md:mb-0">
              <h1 className="text-4xl font-bold mb-6">
                {messages.landing.hero.title}
              </h1>
              <p className="text-xl mb-8 text-blue-100">
                {messages.landing.hero.subtitle}
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Button size="lg" asChild>
                  <Link href={isLoggedIn ? `/${locale}/projects` : `/${locale}/register`}>
                    {isLoggedIn ? messages.landing.hero.getStarted : messages.landing.hero.tryNow}
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </Link>
                </Button>
                <Button size="lg" variant="outline" className="bg-white/10" asChild>
                  <Link href={`/${locale}/about`}>
                    {messages.landing.hero.learnMore}
                  </Link>
                </Button>
              </div>
            </div>
            <div className="md:w-1/2 flex justify-center">
              <div className="bg-white/10 backdrop-blur-md rounded-xl p-6 max-w-md w-full">
                <div className="rounded-lg overflow-hidden shadow-2xl">
                  <div className="bg-white p-4 text-gray-900">
                    <div className="flex items-center mb-4">
                      <div className="h-3 w-3 rounded-full bg-red-500 mr-2"></div>
                      <div className="h-3 w-3 rounded-full bg-yellow-500 mr-2"></div>
                      <div className="h-3 w-3 rounded-full bg-green-500"></div>
                    </div>
                    <div className="space-y-3">
                      <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                      <div className="h-4 bg-gray-200 rounded"></div>
                      <div className="h-4 bg-gray-200 rounded w-5/6"></div>
                      <div className="flex space-x-2">
                        <div className="h-8 bg-blue-100 rounded w-1/3 flex items-center justify-center text-xs text-blue-600 font-medium">Backend</div>
                        <div className="h-8 bg-purple-100 rounded w-1/3 flex items-center justify-center text-xs text-purple-600 font-medium">Frontend</div>
                        <div className="h-8 bg-green-100 rounded w-1/3 flex items-center justify-center text-xs text-green-600 font-medium">Testing</div>
                      </div>
                      <div className="h-20 bg-gray-200 rounded flex flex-col items-center justify-center">
                        <div className="h-6 w-24 bg-gray-300 rounded mb-2"></div>
                        <div className="h-4 w-16 bg-gray-300 rounded"></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-24 bg-gray-50 w-full">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              {messages.landing.features.title}
            </h2>
            <p className="text-md text-gray-600 max-w-3xl mx-auto">
              {messages.landing.features.subtitle}
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Feature 1 */}
            <div className="bg-white rounded-xl shadow-md p-6 hover:shadow-lg transition-shadow">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                <LineChart className="h-6 w-6 text-blue-600" />
              </div>
              <h3 className="text-xl font-bold mb-2 text-gray-900">{messages.landing.features.feature1.title}</h3>
              <p className="text-xs md:text-sm text-gray-600">{messages.landing.features.feature1.description}</p>
            </div>

            {/* Feature 2 */}
            <div className="bg-white rounded-xl shadow-md p-6 hover:shadow-lg transition-shadow">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4">
                <Users className="h-6 w-6 text-green-600" />
              </div>
              <h3 className="text-xl font-bold mb-2 text-gray-900">{messages.landing.features.feature2.title}</h3>
              <p className="text-xs md:text-sm text-gray-600">{messages.landing.features.feature2.description}</p>
            </div>

            {/* Feature 3 */}
            <div className="bg-white rounded-xl shadow-md p-6 hover:shadow-lg transition-shadow">
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4">
                <Clock className="h-6 w-6 text-purple-600" />
              </div>
              <h3 className="text-xl font-bold mb-2 text-gray-900">{messages.landing.features.feature3.title}</h3>
              <p className="text-xs md:text-sm text-gray-600">{messages.landing.features.feature3.description}</p>
            </div>

            {/* Feature 4 */}
            <div className="bg-white rounded-xl shadow-md p-6 hover:shadow-lg transition-shadow">
              <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mb-4">
                <DollarSign className="h-6 w-6 text-yellow-600" />
              </div>
              <h3 className="text-xl font-bold mb-2 text-gray-900">{messages.landing.features.feature4.title}</h3>
              <p className="text-xs md:text-sm text-gray-600">{messages.landing.features.feature4.description}</p>
            </div>

            {/* Feature 5 */}
            <div className="bg-white rounded-xl shadow-md p-6 hover:shadow-lg transition-shadow">
              <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mb-4">
                <BarChart3 className="h-6 w-6 text-red-600" />
              </div>
              <h3 className="text-xl font-bold mb-2 text-gray-900">{messages.landing.features.feature5.title}</h3>
              <p className="text-xs md:text-sm text-gray-600">{messages.landing.features.feature5.description}</p>
            </div>

            {/* Feature 6 */}
            <div className="bg-white rounded-xl shadow-md p-6 hover:shadow-lg transition-shadow">
              <div className="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center mb-4">
                <CheckCircle2 className="h-6 w-6 text-indigo-600" />
              </div>
              <h3 className="text-xl font-bold mb-2 text-gray-900">{messages.landing.features.feature6.title}</h3>
              <p className="text-xs md:text-sm text-gray-600">{messages.landing.features.feature6.description}</p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-blue-600 text-white w-full">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center w-full overflow-hidden">
          <h2 className="text-3xl font-bold mb-4">{messages.landing.cta.title}</h2>
          <p className="text-md mb-8 max-w-3xl mx-auto text-blue-100">{messages.landing.cta.subtitle}</p>
          <Button size="lg" className="bg-white text-blue-600 hover:bg-blue-50" asChild>
            <Link href={isLoggedIn ? `/${locale}/projects` : `/${locale}/register`}>
              {messages.landing.cta.button}
              <ArrowRight className="ml-2 h-5 w-5" />
            </Link>
          </Button>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12 w-full">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full overflow-hidden">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center mb-4">
                <div className="w-10 h-10 bg-blue-600 rounded-md flex items-center justify-center mr-3">
                  <BarChart3 className="text-white" />
                </div>
                <span className="text-xl font-bold">Hapoest</span>
              </div>
              <p className="text-xs md:text-sm text-gray-400 mb-4">{messages.footer.description}</p>
            </div>
            
            <div>
              <h3 className="text-lg font-semibold mb-4">{messages.footer.links.title}</h3>
              <ul className="space-y-2">
                <li><Link href={getHaposoftUrl(locale as string)} target="_blank" className="text-xs md:text-sm text-gray-400 hover:text-white">{messages.footer.links.about}</Link></li>
                <li><Link href="#" className="text-xs md:text-sm text-gray-400 hover:text-white">{messages.footer.links.features}</Link></li>
                <li><Link href="#" className="text-xs md:text-sm text-gray-400 hover:text-white">{messages.footer.links.pricing}</Link></li>
                <li><Link href="#" className="text-xs md:text-sm text-gray-400 hover:text-white">{messages.footer.links.blog}</Link></li>
              </ul>
            </div>
            
            <div>
              <h3 className="text-lg font-semibold mb-4">{messages.footer.contact.title}</h3>
              <ul className="space-y-2">
                <li><Link href={`mailto:${messages.footer.contact.email}`} className="text-gray-400 hover:text-white">{messages.footer.contact.email}</Link></li>
                <li><Link href={`tel:${messages.footer.contact.phone}`} className="text-gray-400 hover:text-white">{messages.footer.contact.phone}</Link></li>
                <li><span className="text-xs md:text-sm text-gray-400">{messages.footer.contact.address}</span></li>
              </ul>
            </div>
            
            <div>
              <h3 className="text-lg font-semibold mb-4">{messages.footer.legal.title}</h3>
              <ul className="space-y-2">
                <li><Link target="_blank" href="/privacy-policy" className="text-xs md:text-sm text-gray-400 hover:text-white">{messages.footer.legal.privacy}</Link></li>
                <li><Link target="_blank" href="/terms-of-service" className="text-xs md:text-sm text-gray-400 hover:text-white">{messages.footer.legal.terms}</Link></li>
                <li><Link target="_blank" href="/cookie-policy" className="text-xs md:text-sm text-gray-400 hover:text-white">{messages.footer.legal.cookies}</Link></li>
              </ul>
            </div>
          </div>
          
          <div className="border-t border-gray-800 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-400">&copy; 2025 Hapoest. {messages.footer.allRightsReserved}</p>
          </div>
        </div>
      </footer>
    </div>
  )
}