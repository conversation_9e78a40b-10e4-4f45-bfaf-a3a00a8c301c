"use client"

import React from 'react'
import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { useTranslations } from 'next-intl'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { FcGoogle } from 'react-icons/fc'
import { BarChart3, ArrowRight, CheckCircle, InfoIcon } from 'lucide-react'
import { useParams } from 'next/navigation'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { withAuthRedirect } from '@/components/auth/withAuthRedirect';

// Add TypeScript interface for Google credential response
interface GoogleCredentialResponse {
  credential: string;
  select_by: string;
}

// Add type definition for window.google
declare global {
  interface Window {
    google?: {
      accounts: {
        id: {
          initialize: (config: any) => void;
          renderButton: (element: HTMLElement, options: any) => void;
          prompt: () => void;
        };
      };
    };
  }
}

function LoginPage() {
  const [loading, setLoading] = useState(false)
  const [googleLoading, setGoogleLoading] = useState(false)
  const [error, setError] = useState('')
  const router = useRouter()
  const t = useTranslations()
  const params = useParams()
  const locale = params.locale as string
  const [message, setMessage] = useState({ type: '', text: '' })
  
  // Google Sign-In initialization
  useEffect(() => {
    // Load Google Identity Services script
    const loadGoogleScript = () => {
      const script = document.createElement('script')
      script.src = 'https://accounts.google.com/gsi/client'
      script.async = true
      script.defer = true
      document.body.appendChild(script)
      return script
    }
    
    const script = loadGoogleScript()
    
    return () => {
      // Cleanup script when component unmounts
      if (script) {
        document.body.removeChild(script)
      }
    }
  }, [])

  // Handle Google Sign-In response
  async function handleGoogleSignIn(response: GoogleCredentialResponse) {
    try {
      setGoogleLoading(true)
      setError('')
      
      // Extract JWT token from the response
      const token = response.credential
      
      // Decode the JWT to get user information
      const base64Url = token.split('.')[1]
      const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/')
      const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
        return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2)
      }).join(''))
      
      const { email, sub, name, picture } = JSON.parse(jsonPayload)
      
      // Send to backend
      const apiResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/auth/google`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          google_id: sub,
          token,
          name,
          picture
        }),
      })
      
      const data = await apiResponse.json()
      
      if (!apiResponse.ok) {
        throw new Error(data.detail || t('auth.loginFailed'))
      }
      
      // Store token and redirect
      localStorage.setItem('token', data.access_token)
      localStorage.setItem('locale', locale)
      router.push(`/${locale}/projects`)
    } catch (err: any) {
      console.error("Google login error:", err)
      setError(err.message)
      setGoogleLoading(false)
    }
  }
  
  // Manual Google Sign-In trigger (fallback)
  function handleGoogleSignInManual() {
    setGoogleLoading(true)
    if (window.google) {
      window.google.accounts.id.prompt()
    } else {
      setError(t('auth.googleNotAvailable', { defaultValue: 'Google Sign-In is not available at the moment. Please try again later.' }))
      setGoogleLoading(false)
    }
  }

  async function handleSubmit(event: React.FormEvent<HTMLFormElement>) {
    event.preventDefault()
    setLoading(true)
    setError('')

    const formData = new FormData(event.currentTarget)
    const email = formData.get('email') as string
    const password = formData.get('password') as string

    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          username: email,
          password,
        }),
      })

      const data = await response.json()

      if (!response.ok) {
        // Xử lý lỗi chi tiết từ backend
        if (response.status === 401) {
          throw new Error(t('auth.incorrectLoginInformation'));
        } else if (data.detail) {
          if (typeof data.detail === 'object') {
            // Trường hợp FastAPI validation error
            if (Array.isArray(data.detail)) {
              const errors = data.detail.map((err: any) => 
                `${err.loc[1]}: ${err.msg}`
              ).join(', ');
              throw new Error(errors);
            } else {
              // Trường hợp khác, chuyển object thành string
              throw new Error(JSON.stringify(data.detail));
            }
          } else {
            throw new Error(data.detail);
          }
        } else {
          throw new Error(t('auth.loginFailed'));
        }
      }

      // Store token and redirect
      localStorage.setItem('token', data.access_token)
      localStorage.setItem('locale', locale)
      router.push(`/${locale}/projects`)
    } catch (err: any) {
      console.error("Login error:", err);
      setError(err.message)
      setLoading(false)
    }
  }

  // Xử lý query params và xóa token cũ
  useEffect(() => {
    // Clear any existing tokens on login page visit
    localStorage.removeItem('token')

    // Kiểm tra query params
    const urlParams = new URLSearchParams(window.location.search)
    if (urlParams.get('verified') === 'true') {
      setMessage({
        type: 'success',
        text: t('auth.emailVerified', { defaultValue: 'Your email has been verified successfully. You can now log in.' })
      })
    } else if (urlParams.get('registered') === 'true') {
      setMessage({
        type: 'info',
        text: t('auth.pleaseCheckEmail', { defaultValue: 'Please check your email to verify your account before logging in.' })
      })
    }
  }, [t, setMessage])
  
  // Initialize Google Sign-In when window.google is available
  useEffect(() => {
    const initializeGoogleSignIn = () => {
      if (window.google) {
        const clientId = process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID;
        
        if (!clientId) {
          setError(t('auth.googleConfigError', { defaultValue: 'Google Sign-In configuration error. Please contact support.' }));
          return;
        }
        
        window.google.accounts.id.initialize({
          client_id: clientId,
          callback: handleGoogleSignIn,
          auto_select: false,
        });
      } else {
        // If not available yet, try again after a short delay
        setTimeout(initializeGoogleSignIn, 500);
      }
    };
    
    // Initialize after a delay to ensure the script is loaded
    const timer = setTimeout(initializeGoogleSignIn, 1000);
    
    return () => clearTimeout(timer);
  }, [t]);

  return (
    <div className="min-h-screen flex flex-col md:flex-row">
      {/* Left side - Branding & Info */}
      <div className="w-full md:w-1/2 bg-gradient-to-br from-blue-600 to-indigo-700 text-white p-8 flex flex-col">
        <div className="flex items-center mb-12">
          <div className="w-10 h-10 bg-white rounded-md flex items-center justify-center mr-3">
            <BarChart3 className="text-blue-600" />
          </div>
          <Link href={`/${locale}`} className="text-xl font-bold">Hapoest</Link>
        </div>
        
        <div className="flex-grow flex flex-col justify-center max-w-md mx-auto">
          <h1 className="text-3xl md:text-4xl font-bold mb-6">
            {t('app.title')} - {t('nav.login')}
          </h1>
          <p className="text-xs md:text-sm mb-8 text-blue-100">
            {t('app.description')}
          </p>
          <div className="hidden md:block">
            <div className="flex justify-center items-center mb-6">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="320"
                height="240"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="text-blue-100"
              >
                <rect width="16" height="20" x="4" y="2" rx="2" />
                <path d="M8 2v4" />
                <path d="M16 2v4" />
                <path d="M16 14H8" />
                <path d="M16 18H8" />
                <path d="M8 10h4" />
              </svg>
            </div>
            <div className="flex justify-center gap-4">
              <div className="w-24 h-24 bg-blue-500/30 rounded-lg flex items-center justify-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="40"
                  height="40"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="1.5"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="text-blue-100"
                >
                  <path d="M3 3v18h18" />
                  <path d="m19 9-5 5-4-4-3 3" />
                </svg>
              </div>
              <div className="w-24 h-24 bg-blue-500/30 rounded-lg flex items-center justify-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="40"
                  height="40"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="1.5"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="text-blue-100"
                >
                  <circle cx="12" cy="12" r="10" />
                  <path d="m9 9 6 6" />
                  <path d="m15 9-6 6" />
                </svg>
              </div>
              <div className="w-24 h-24 bg-blue-500/30 rounded-lg flex items-center justify-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="40"
                  height="40"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="1.5"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="text-blue-100"
                >
                  <path d="M16 16h6" />
                  <path d="M19 13v6" />
                  <path d="M12 15c-1.1 0-2-.9-2-2 0-1.1.9-2 2-2" />
                  <rect width="8" height="8" x="2" y="9" rx="2" />
                  <path d="M7 4c0-1.7 1.3-3 3-3h8v4h-4" />
                </svg>
              </div>
            </div>
          </div>
        </div>
        
        <div className="mt-auto text-sm text-blue-200">
          © 2025 Hapoest - {t('footer.allRightsReserved')}
        </div>
      </div>
      
      {/* Right side - Login Form */}
      <div className="w-full md:w-1/2 flex flex-col items-center justify-center p-8 bg-white">
        <div className="w-full max-w-sm space-y-6">
          <div className="flex flex-col space-y-2 text-center">
            <h1 className="text-2xl font-semibold tracking-tight">
              {t("auth.loginTitle")}
            </h1>
            <p className="text-sm text-muted-foreground">
              {t("auth.loginSubtitle")}
            </p>
          </div>
          
          <form onSubmit={handleSubmit} className="space-y-4" data-hs-ignore="true">
            {message.text && (
              <Alert className={`${
                message.type === 'success' ? 'bg-green-50 border-green-200' : 
                message.type === 'info' ? 'bg-blue-50 border-blue-200' : ''
              }`}>
                {message.type === 'success' && <CheckCircle className="h-4 w-4 text-green-600" />}
                {message.type === 'info' && <InfoIcon className="h-4 w-4 text-blue-600" />}
                <AlertDescription className={`${
                  message.type === 'success' ? 'text-green-700' : 
                  message.type === 'info' ? 'text-blue-700' : ''
                }`}>
                  {message.text}
                </AlertDescription>
              </Alert>
            )}

            <div className="space-y-2">
              <Label htmlFor="email">{t("auth.email")}</Label>
              <Input
                id="email"
                name="email"
                type="email"
                placeholder="<EMAIL>"
                required
                className="w-full"
              />
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label htmlFor="password">{t("auth.password")}</Label>
                <Link 
                  href={`/${locale}/forgot-password`} 
                  className="text-xs text-blue-600 hover:text-blue-800"
                >
                  {t("auth.forgotPassword")}
                </Link>
              </div>
              <Input
                id="password"
                name="password"
                type="password"
                required
                className="w-full"
              />
            </div>
            
            {error && (
              <div className="text-sm text-red-500 p-2 bg-red-50 rounded-md">
                {error}
              </div>
            )}
            
            <Button type="submit" className="w-full bg-blue-600 hover:bg-blue-700" disabled={loading}>
              {loading ? t("common.loading") : t("auth.login")}
            </Button>
          </form>

          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <span className="w-full border-t" />
            </div>
            <div className="relative flex justify-center text-xs uppercase">
              <span className="bg-white px-2 text-muted-foreground">
                {t("common.or")}
              </span>
            </div>
          </div>

          {/*Google Sign-In button */}
          <Button
            variant="outline"
            type="button"
            className="w-full"
            onClick={handleGoogleSignInManual}
            disabled={googleLoading}
          >
            <FcGoogle className="mr-2 h-4 w-4" />
            {googleLoading ? t("common.loading") : t("auth.googleLogin")}
          </Button>
          
          <div className="text-center text-sm">
            {t("auth.noAccount")}{" "}
            <Link 
              href={`/${locale}/register`} 
              className="text-blue-600 hover:text-blue-800 font-medium"
            >
              {t("auth.register")}
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}

export default withAuthRedirect(LoginPage);
