"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { useTranslations } from "next-intl"
import { useParams } from "next/navigation"

export default function NewProjectPage() {
  const { locale } = useParams()
  const t = useTranslations()
  const [name, setName] = useState("")
  const [description, setDescription] = useState("")
  const [softwareType, setSoftwareType] = useState("")
  const [technologyStack, setTechnologyStack] = useState("")
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState("")
  const router = useRouter()
  
  useEffect(() => {
    const token = typeof window !== "undefined" ? localStorage.getItem("token") : null
    
    if (!token) {
      router.push(`/${locale}/login`)
    }
  }, [router, locale])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError("")
    
    try {
      const token = localStorage.getItem("token")
      
      if (!token) {
        router.push(`/${locale}/login`)
        return
      }
      
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/projects`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          name,
          description,
          software_type: softwareType,
          technology_stack: technologyStack,
        }),
      })
      
      if (!response.ok) {
        const data = await response.json()
        throw new Error(data.detail || t('projects.createFailed'))
      }
      
      const project = await response.json()
      router.push(`/${locale}/projects/${project.id}`)
    } catch (err: any) {
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="flex flex-col min-h-screen bg-white">

      {/* Main Content */}
      <main className="flex-1 p-6">
        <div className="max-w-2xl mx-auto">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold">{t('projects.createNew')}</h2>
            <Link href={`/${locale}/projects`} passHref>
              <Button variant="outline">{t('common.back')}</Button>
            </Link>
          </div>
          
          {error && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
              {error}
            </div>
          )}
          
          <form onSubmit={handleSubmit} className="space-y-4" data-hs-ignore="true">
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                {t('projects.projectName')} <span className="text-red-500">*</span>
              </label>
              <Input
                id="name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                required
                className="w-full"
              />
            </div>
            
            <div>
              <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                {t('projects.description')}
              </label>
              <Textarea
                id="description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                rows={4}
                className="w-full"
              />
            </div>
            
            <div>
              <label htmlFor="softwareType" className="block text-sm font-medium text-gray-700 mb-1">
                {t('projects.softwareType')}
              </label>
              <Input
                id="softwareType"
                value={softwareType}
                onChange={(e) => setSoftwareType(e.target.value)}
                placeholder="Web App, Mobile App, Desktop App"
                className="w-full"
              />
            </div>
            
            <div>
              <label htmlFor="technologyStack" className="block text-sm font-medium text-gray-700 mb-1">
                {t('projects.techStack')}
              </label>
              <Input
                id="technologyStack"
                value={technologyStack}
                onChange={(e) => setTechnologyStack(e.target.value)}
                placeholder="React, Node.js, PostgreSQL"
                className="w-full"
              />
            </div>
            
            <div className="pt-4">
              <Button type="submit" className="w-full" disabled={loading}>
                {loading ? t('common.loading') : t('projects.createNew')}
              </Button>
            </div>
          </form>
        </div>
      </main>
      
      {/* Footer */}
      <footer className="p-4 text-sm text-gray-500 flex justify-center">
        <p>© 2024 {t('app.title')}</p>
      </footer>
    </div>
  )
} 