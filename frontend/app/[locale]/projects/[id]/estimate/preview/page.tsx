"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { useRout<PERSON> } from "next/navigation"
import Link from "next/link"
import { useTranslations } from "next-intl"
import { Project as BaseProject } from '@/types'
import { Checkbox } from "@/components/ui/checkbox"
import useUserSettings from "@/lib/hooks/useUserSettings"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Textarea } from "@/components/ui/textarea"
import {
  <PERSON><PERSON><PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"

// Helper function to parse numbers consistently regardless of locale
const parseLocaleFloat = (value: any): number => {
  if (typeof value === 'number') return value;
  if (!value) return 0;
  
  // Convert string number from locale format to standard format
  // Replace comma with dot for decimal separator
  const normalized = String(value).replace(/,/g, '.');
  return parseFloat(normalized) || 0;
}

// Extend Project interface để thêm field requirements
interface Project extends BaseProject {
  requirements?: string;
}

interface DevelopmentItem {
  feature: string
  description: string
  backend_effort: number
  frontend_effort: number
  unit_test_effort: number
  priority: "MUST_HAVE" | "NICE_TO_HAVE"
  is_selected: boolean
}

interface SummaryItem {
  category: string
  effort: number
  description: string
  role?: string
  day_rate?: number
  cost?: number
  is_selected?: boolean
}

const EstimatePreviewPage = ({ params }: { params: { id: string, locale: string } }) => {
  const { id, locale } = params
  const router = useRouter()
  const t = useTranslations('common')
  const tProjects = useTranslations('projects')
  const tEstimate = useTranslations('estimate')
  const { userSettings, loading: settingsLoading, getCurrencySymbol } = useUserSettings(locale)
  const [project, setProject] = useState<Project | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [developmentItems, setDevelopmentItems] = useState<DevelopmentItem[]>([])
  const [summaryItems, setSummaryItems] = useState<SummaryItem[]>([])
  const [grandTotal, setGrandTotal] = useState<number>(0)
  const [editRequirementsOpen, setEditRequirementsOpen] = useState(false)
  const [requirements, setRequirements] = useState("")
  const [savingRequirements, setSavingRequirements] = useState(false)

  useEffect(() => {
    const token = localStorage.getItem("token")
    if (!token) {
      router.push(`/${locale}/login`)
      return
    }

    const fetchData = async () => {
      try {
        setLoading(true)
        
        // First try to get data from session storage
        const previewData = sessionStorage.getItem('estimatePreview')
        
        if (previewData) {
          try {
            const data = JSON.parse(previewData)
            
            setProject(data.project)
            setRequirements(data.project?.requirements || "")
            
            // Make sure development items have the is_selected property
            const devItems = (data.development_items || []).map((item: any) => ({
              ...item,
              is_selected: item.is_selected !== undefined ? item.is_selected : true
            }))
            
            setDevelopmentItems(devItems)
            
            // Do not set summary items immediately, wait until userSettings loads
            if (data.summary_items && data.summary_items.length > 0) {
              // Replace "Total Development Effort" with "Development"
              const updatedSummaryItems = data.summary_items.map((item: SummaryItem) => {
                if (item.category === "Total Development Effort") {
                  return {
                    ...item,
                    category: "Development"
                  }
                }
                return {
                  ...item,
                  // Do not save day_rate here, will get from userSettings
                  day_rate: undefined
                }
              })
              
              // Save summary items but will update day_rate after userSettings loads
              setSummaryItems(updatedSummaryItems)
            } else {
              // Do not create summary items immediately, wait for userSettings to load
            }
            
            setGrandTotal(data.grand_total || 0)
          } catch (error) {
            console.error('Error parsing preview data:', error)
            throw new Error('Error loading preview data')
          }
        } else {
          // If no data in session storage, try to fetch from API
          const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/projects/${id}`, {
            headers: {
              'Authorization': `Bearer ${token}`
            }
          })
          
          if (!response.ok) {
            if (response.status === 401) {
              localStorage.removeItem("token")
              router.push(`/${locale}/login`)
              return
            }
            throw new Error(tProjects('projectDoesNotExist'))
          }
          
          const projectData = await response.json()
          setProject(projectData)
          setRequirements(projectData.requirements || "")
          
          if (projectData.estimation_results) {
            // Make sure development items have the is_selected property
            const devItems = (projectData.estimation_results.development_items || []).map((item: any) => ({
              ...item,
              is_selected: item.is_selected !== undefined ? item.is_selected : true
            }))
            
            setDevelopmentItems(devItems)

            // First set the initial summary items
            const initialSummaryItems = projectData.estimation_results.summary_items || []
            setSummaryItems(initialSummaryItems)
            setGrandTotal(projectData.estimation_results.grand_total || 0)
            
            // Then ensure Development exists and items are in proper order
            // Update day_rate after userSettings loads
          } else {
            throw new Error('No estimate data available for preview')
          }
        }
      } catch (error) {
        console.error('Error in fetchData:', error)
        setError(error instanceof Error ? error.message : 'Error loading data')
        setLoading(false)
      } finally {
        setLoading(false)
      }
    }
    
    fetchData()
  }, [id, locale, router, tEstimate, tProjects])

  // Ensure recalculate after userSettings loads
  useEffect(() => {
    if (userSettings && developmentItems.length > 0) {
      recalculateTotals(developmentItems, summaryItems);
    }
  }, [userSettings]);

  // Ensure recalculate after data loads and userSettings loads
  useEffect(() => {
    if (!loading && !settingsLoading && developmentItems.length > 0 && userSettings) {
      recalculateTotals(developmentItems, summaryItems);
    }
  }, [loading, settingsLoading, developmentItems.length, userSettings]);

  // Generate summary items when development items change
  useEffect(() => {
    if (loading || !developmentItems || !userSettings) {
      return;
    }
    
    createStandardSummaryItems(developmentItems);
  }, [developmentItems, loading, userSettings]);

  // Helper function to create standard summary items
  const createStandardSummaryItems = (devItems: DevelopmentItem[]) => {
    // Ensure userSettings is loaded
    if (!userSettings) {
      return;
    }

    const totalBackend = devItems.reduce((sum, item) => sum + (item.backend_effort || 0), 0)
    const totalFrontend = devItems.reduce((sum, item) => sum + (item.frontend_effort || 0), 0)
    const totalUnitTest = devItems.reduce((sum, item) => sum + (item.unit_test_effort || 0), 0)
    const totalDev = totalBackend + totalFrontend + totalUnitTest
    
    // Get rate values from user settings
    const pmRate = userSettings.role_rates?.PM || 800
    const designerRate = userSettings.role_rates?.Designer || 600
    const developerRate = userSettings.role_rates?.Developer || 500
    const testerRate = userSettings.role_rates?.Tester || 400
    
    const standardSummaryItems = [
      {
        category: tEstimate('summaryCategories.development'),
        effort: totalDev,
        description: tEstimate('summaryDescriptions.totalDevelopment'),
        role: tEstimate('roles.Developer'),
        day_rate: developerRate,
        cost: totalDev * developerRate,
        is_selected: true
      },
      {
        category: tEstimate('summaryCategories.requirementsDefinition'),
        effort: Math.round(totalDev * 0.1 * 10) / 10, // 10% of development effort
        description: tEstimate('summaryDescriptionsDetail.requirementsAnalysis', { percent: 10 }),
        role: tEstimate('roles.BA'),
        day_rate: pmRate,
        cost: Math.round(totalDev * 0.1 * 10) / 10 * pmRate,
        is_selected: true
      },
      {
        category: tEstimate('summaryCategories.uiUxDesign'),
        effort: Math.round(totalDev * 0.15 * 10) / 10, // 15% of development effort
        description: tEstimate('summaryDescriptionsDetail.uiUxDesign', { percent: 15 }),
        role: tEstimate('roles.UI'),
        day_rate: designerRate,
        cost: Math.round(totalDev * 0.15 * 10) / 10 * designerRate,
        is_selected: true
      },
      {
        category: tEstimate('summaryCategories.integrationTest'),
        effort: Math.round(totalDev * 0.2 * 10) / 10, // 20% of development effort
        description: tEstimate('summaryDescriptionsDetail.integrationTesting', { percent: 20 }),
        role: tEstimate('roles.QA'),
        day_rate: testerRate,
        cost: Math.round(totalDev * 0.2 * 10) / 10 * testerRate,
        is_selected: true
      },
      {
        category: tEstimate('summaryCategories.uatSupport'),
        effort: Math.round(totalDev * 0.1 * 10) / 10, // 10% of development effort
        description: tEstimate('summaryDescriptionsDetail.uatSupport', { percent: 10 }),
        role: tEstimate('roles.QA'),
        day_rate: testerRate,
        cost: Math.round(totalDev * 0.1 * 10) / 10 * testerRate,
        is_selected: true
      },
      {
        category: tEstimate('summaryCategories.projectManagement'),
        effort: Math.round(totalDev * 0.15 * 10) / 10, // 15% of development effort
        description: tEstimate('summaryDescriptionsDetail.projectManagement', { percent: 15 }),
        role: tEstimate('roles.PM'),
        day_rate: pmRate,
        cost: Math.round(totalDev * 0.15 * 10) / 10 * pmRate,
        is_selected: true
      }
    ]
    
    setSummaryItems(standardSummaryItems)
    
    // Calculate total effort and total cost from selected items
    const totalEffort = standardSummaryItems
      .filter(item => item.is_selected === true)
      .reduce((sum, item) => sum + (item.effort || 0), 0);
    
    setGrandTotal(parseLocaleFloat(totalEffort.toFixed(1)))
  }

  const handleItemSelection = (index: number) => {
    if (developmentItems[index].priority === "MUST_HAVE") return

    const newItems = [...developmentItems]
    newItems[index] = {
      ...newItems[index],
      is_selected: !newItems[index].is_selected
    }
    setDevelopmentItems(newItems)
    recalculateTotals(newItems, summaryItems)
  }

  const updateDevelopmentItem = (index: number, key: keyof DevelopmentItem, value: any) => {
    const newItems = [...developmentItems]
    newItems[index] = { 
      ...newItems[index], 
      [key]: (key === "backend_effort" || key === "frontend_effort" || key === "unit_test_effort") 
        ? parseLocaleFloat(value) 
        : value 
    }
    
    // Only auto-calculate unit test effort initially
    // Don't recalculate if explicitly editing unit_test_effort
    if ((key === "backend_effort" || key === "frontend_effort") && !newItems[index].unit_test_effort) {
      const backend = key === "backend_effort" ? parseLocaleFloat(value) : newItems[index].backend_effort || 0
      const frontend = key === "frontend_effort" ? parseLocaleFloat(value) : newItems[index].frontend_effort || 0
      
      // Unit test effort is calculated as (backend + frontend) / 2
      newItems[index].unit_test_effort = parseLocaleFloat(((backend + frontend) / 2).toFixed(1))
    }
    
    setDevelopmentItems(newItems)
    recalculateTotals(newItems, summaryItems)
  }

  const recalculateTotals = (devItems: DevelopmentItem[], sumItems: SummaryItem[]) => {
    // Ensure only calculate when userSettings is loaded
    if (!userSettings) {
      return;
    }
    

    // Calculate total development effort for selected items only
    const selectedDevItems = devItems.filter(item => item.is_selected)
    const totalBackend = selectedDevItems.reduce((sum, item) => sum + parseLocaleFloat(item.backend_effort), 0)
    const totalFrontend = selectedDevItems.reduce((sum, item) => sum + parseLocaleFloat(item.frontend_effort), 0)
    const totalUnitTest = selectedDevItems.reduce((sum, item) => sum + parseLocaleFloat(item.unit_test_effort), 0)
    const totalDevelopment = totalBackend + totalFrontend + totalUnitTest
    
    // Get rate values from user settings or use defaults
    const pmRate = userSettings.role_rates?.PM || 800
    const designerRate = userSettings.role_rates?.Designer || 600
    const developerRate = userSettings.role_rates?.Developer || 500
    const testerRate = userSettings.role_rates?.Tester || 400
    
    let orderedSummaryItems: SummaryItem[] = []
    
    const reqDefEffort = parseLocaleFloat((totalDevelopment * 0.1).toFixed(1))
    const reqDefCost = parseLocaleFloat((reqDefEffort * pmRate).toFixed(2))
    orderedSummaryItems.push({
      category: "Requirements Definition",
      effort: reqDefEffort,
      description: tEstimate('summaryDescriptionsDetail.requirementsAnalysis', { percent: 10 }),
      role: tEstimate('roles.PM'),
      day_rate: pmRate,
      cost: reqDefCost,
      is_selected: sumItems.find(item => item.category === tEstimate('summaryCategories.requirementsDefinition'))?.is_selected
    })
    
    // Add UI/UX Design: 10% of totalDevelopment
    const uiUxEffort = parseLocaleFloat((totalDevelopment * 0.1).toFixed(1))
    const uiUxCost = parseLocaleFloat((uiUxEffort * designerRate).toFixed(2))
    orderedSummaryItems.push({
      category: "UI/UX Design",
      effort: uiUxEffort,
      description: tEstimate('summaryDescriptionsDetail.uiUxDesign', { percent: 10 }),
      role: tEstimate('roles.Designer'),
      day_rate: designerRate,
      cost: uiUxCost,
      is_selected: sumItems.find(item => item.category === tEstimate('summaryCategories.uiUxDesign'))?.is_selected
    })
    
    // Add Development (direct calculation from Development Tab)
    const devCost = parseLocaleFloat((totalDevelopment * developerRate).toFixed(2))
    orderedSummaryItems.push({
      category: "Development",
      effort: totalDevelopment,
      description: tEstimate('summaryDescriptionsDetail.totalDevelopment', { 
        backend: totalBackend.toFixed(1), 
        frontend: totalFrontend.toFixed(1), 
        testing: totalUnitTest.toFixed(1) 
      }),
      role: tEstimate('roles.Developer'),
      day_rate: developerRate,
      cost: devCost,
      is_selected: sumItems.find(item => item.category === tEstimate('summaryCategories.development'))?.is_selected
    })
    
    // Add Integration Test: 25% of totalDevelopment
    const integrationEffort = parseLocaleFloat((totalDevelopment * 0.25).toFixed(1))
    const integrationCost = parseLocaleFloat((integrationEffort * testerRate).toFixed(2))
    orderedSummaryItems.push({
      category: "Integration Test",
      effort: integrationEffort,
      description: tEstimate('summaryDescriptionsDetail.integrationTesting', { percent: 25 }),
      role: tEstimate('roles.QA'),
      day_rate: testerRate,
      cost: integrationCost,
      is_selected: sumItems.find(item => item.category === tEstimate('summaryCategories.integrationTest'))?.is_selected
    })
    
    // Add UAT Support: 10% of totalDevelopment
    const uatEffort = parseLocaleFloat((totalDevelopment * 0.1).toFixed(1))
    const uatCost = parseLocaleFloat((uatEffort * developerRate).toFixed(2))
    orderedSummaryItems.push({
      category: "UAT Support",
      effort: uatEffort,
      description: tEstimate('summaryDescriptionsDetail.uatSupport', { percent: 10 }),
      role: tEstimate('roles.Developer'),
      day_rate: developerRate,
      cost: uatCost,
      is_selected: sumItems.find(item => item.category === tEstimate('summaryCategories.uatSupport'))?.is_selected
    })
    
    // Calculate subtotal without Project Management
    const subtotalEffort = orderedSummaryItems.reduce((sum, item) => sum + (isNaN(parseLocaleFloat(String(item.effort))) ? 0 : parseLocaleFloat(String(item.effort))), 0)
    
    // Add Project Management at 10% of totalDevelopment
    const pmEffort = parseLocaleFloat((totalDevelopment * 0.1).toFixed(1))
    const pmCost = parseLocaleFloat((pmEffort * pmRate).toFixed(2))
    orderedSummaryItems.push({
      category: "Project Management",
      effort: pmEffort,
      description: tEstimate('summaryDescriptionsDetail.projectManagement', { percent: 10 }),
      role: tEstimate('roles.PM'),
      day_rate: pmRate,
      cost: pmCost,
      is_selected: sumItems.find(item => item.category === tEstimate('summaryCategories.projectManagement'))?.is_selected
    })
    
    // Calculate grand total including only selected summary items
    const selectedSummaryItems = orderedSummaryItems.filter(item => item.is_selected === true);
    
    const totalEffort = selectedSummaryItems.reduce((sum, item) => {
      return sum + parseLocaleFloat(item.effort)
    }, 0)

    // Calculate total cost
    const totalCost = selectedSummaryItems.reduce((sum, item) => {
      return sum + parseLocaleFloat(item.cost)
    }, 0)
    
    // Update state
    setSummaryItems(orderedSummaryItems)
    setGrandTotal(parseLocaleFloat(totalEffort.toFixed(1)))
  }

  const handleConfirm = async () => {
    if (!project) return
    if (!userSettings) {
      setError("Đang tải thiết lập người dùng, vui lòng thử lại sau một chút");
      return;
    }

    try {
      const token = localStorage.getItem("token")
      if (!token) {
        router.push(`/${locale}/login`)
        return
      }

      // Keep original is_selected state for all development items
      // MUST_HAVE items are always selected, so ensure they have is_selected = true
      const allItems = developmentItems.map(item => ({
        ...item,
        is_selected: item.priority === "MUST_HAVE" ? true : item.is_selected
      }))
      
      // Filter selected items for calculation
      const selectedItems = allItems.filter(item => item.is_selected)
      
      // Recalculate all effort values
      const totalBackend = selectedItems.reduce((sum, item) => sum + parseLocaleFloat(item.backend_effort), 0)
      const totalFrontend = selectedItems.reduce((sum, item) => sum + parseLocaleFloat(item.frontend_effort), 0)
      const totalUnitTest = selectedItems.reduce((sum, item) => sum + parseLocaleFloat(item.unit_test_effort), 0)
      const totalDevelopment = totalBackend + totalFrontend + totalUnitTest
      
      // Get rate values from user settings or use defaults
      const pmRate = userSettings.role_rates?.PM || 800
      const designerRate = userSettings.role_rates?.Designer || 600
      const developerRate = userSettings.role_rates?.Developer || 500
      const testerRate = userSettings.role_rates?.Tester || 400
      
      // Recreate summary items with standard ratios and day_rate and cost info
      const reqDefEffort = parseLocaleFloat((totalDevelopment * 0.1).toFixed(1))
      const reqDefCost = parseLocaleFloat((reqDefEffort * pmRate).toFixed(2))
      
      const uiUxEffort = parseLocaleFloat((totalDevelopment * 0.1).toFixed(1))
      const uiUxCost = parseLocaleFloat((uiUxEffort * designerRate).toFixed(2))
      
      const devCost = parseLocaleFloat((totalDevelopment * developerRate).toFixed(2))
      
      const integrationEffort = parseLocaleFloat((totalDevelopment * 0.25).toFixed(1))
      const integrationCost = parseLocaleFloat((integrationEffort * testerRate).toFixed(2))
      
      const uatEffort = parseLocaleFloat((totalDevelopment * 0.1).toFixed(1))
      const uatCost = parseLocaleFloat((uatEffort * developerRate).toFixed(2))
      
      const pmEffort = parseLocaleFloat((totalDevelopment * 0.1).toFixed(1))
      const pmCost = parseLocaleFloat((pmEffort * pmRate).toFixed(2))
      
      // Create all summary items with is_selected state from current state
      const allSummaryItems = [
        {
          category: "Requirements Definition",
          effort: reqDefEffort,
          description: tEstimate('summaryDescriptionsDetail.requirementsAnalysis', { percent: 10 }),
          role: tEstimate('roles.PM'),
          day_rate: pmRate,
          cost: reqDefCost,
          is_selected: summaryItems.find(item => item.category === tEstimate('summaryCategories.requirementsDefinition'))?.is_selected
        },
        {
          category: "UI/UX Design",
          effort: uiUxEffort,
          description: tEstimate('summaryDescriptionsDetail.uiUxDesign', { percent: 10 }),
          role: tEstimate('roles.Designer'),
          day_rate: designerRate,
          cost: uiUxCost,
          is_selected: summaryItems.find(item => item.category === tEstimate('summaryCategories.uiUxDesign'))?.is_selected
        },
        {
          category: "Development",
          effort: totalDevelopment,
          description: tEstimate('summaryDescriptionsDetail.totalDevelopment', { 
            backend: totalBackend.toFixed(1), 
            frontend: totalFrontend.toFixed(1), 
            testing: totalUnitTest.toFixed(1) 
          }),
          role: tEstimate('roles.Developer'),
          day_rate: developerRate,
          cost: devCost,
          is_selected: summaryItems.find(item => item.category === tEstimate('summaryCategories.development'))?.is_selected
        },
        {
          category: "Integration Test",
          effort: integrationEffort,
          description: tEstimate('summaryDescriptionsDetail.integrationTesting', { percent: 25 }),
          role: tEstimate('roles.QA'),
          day_rate: testerRate,
          cost: integrationCost,
          is_selected: summaryItems.find(item => item.category === tEstimate('summaryCategories.integrationTest'))?.is_selected
        },
        {
          category: "UAT Support",
          effort: uatEffort,
          description: tEstimate('summaryDescriptionsDetail.uatSupport', { percent: 10 }),
          role: tEstimate('roles.Developer'),
          day_rate: developerRate,
          cost: uatCost,
          is_selected: summaryItems.find(item => item.category === tEstimate('summaryCategories.uatSupport'))?.is_selected
        },
        {
          category: "Project Management",
          effort: pmEffort,
          description: tEstimate('summaryDescriptionsDetail.projectManagement', { percent: 10 }),
          role: tEstimate('roles.PM'),
          day_rate: pmRate,
          cost: pmCost,
          is_selected: summaryItems.find(item => item.category === tEstimate('summaryCategories.projectManagement'))?.is_selected
        }
      ]
      
      // Calculate total effort and total cost from selected items only
      const selectedSummaryItems = allSummaryItems.filter(item => item.is_selected === true);

      const updatedGrandTotal = selectedSummaryItems.reduce((sum, item) => 
        sum + parseLocaleFloat(item.effort), 0)
        
      const totalCost = selectedSummaryItems.reduce((sum, item) => 
        sum + parseLocaleFloat(item.cost), 0)
      
      // Prepare data for saving - make sure it matches the structure in the project model
      const estimationResults = {
        development_items: allItems.filter(item => item.is_selected === true), // Chỉ lưu development items đã được chọn
        summary_items: allSummaryItems.filter(item => item.is_selected === true), // Chỉ lưu summary items đã được chọn
        grand_total: parseLocaleFloat(updatedGrandTotal.toFixed(1)),
        total_cost: parseLocaleFloat(totalCost.toFixed(2)),
        day_rate: developerRate, // Use developer rate as the default
        currency: userSettings?.currency || "USD"
      }

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/projects/${project.id}/estimates`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          name: `Estimate ${new Date().toLocaleDateString()} ${new Date().toLocaleTimeString()}`,
          development_items: allItems.filter(item => item.is_selected === true),
          summary_items: allSummaryItems.filter(item => item.is_selected === true),
          grand_total: parseLocaleFloat(updatedGrandTotal.toFixed(1)),
          total_cost: parseLocaleFloat(totalCost.toFixed(2)),
          currency: userSettings?.currency || "USD"
        })
      })

      if (!response.ok) {
        const errorBody = await response.text()
        console.error("Error response:", response.status, errorBody)
        throw new Error(`${tEstimate('saveError')}: ${response.status}`)
      }

      // Clear preview data from session storage
      sessionStorage.removeItem('estimatePreview')

      // Redirect to estimates list page
      router.push(`/${locale}/projects/${id}`)
    } catch (error) {
      console.error("Error saving estimate:", error)
      setError(error instanceof Error ? error.message : tEstimate('saveError'))
    }
  }

  const saveRequirements = async () => {
    if (!project) return

    try {
      setSavingRequirements(true)
      const token = localStorage.getItem("token")
      
      if (!token) {
        router.push(`/${locale}/login`)
        return
      }

      // Create a new object with just the requirements property to avoid
      // type issues with the Project interface
      const updateData = {
        requirements: requirements
      }

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/projects/${id}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(updateData)
      })

      if (!response.ok) {
        throw new Error("Failed to update requirements")
      }

      // Update the project in state with TypeScript-safe approach
      if (project) {
        setProject({
          ...project,
          requirements: requirements
        })
      }
      
      // Close the dialog
      setEditRequirementsOpen(false)
    } catch (error) {
      console.error("Error saving requirements:", error)
      setError(error instanceof Error ? error.message : "Error saving requirements")
    } finally {
      setSavingRequirements(false)
    }
  }

  if (loading || settingsLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-gray-900 mx-auto"></div>
          <p className="mt-4">{loading ? t('loading') : 'Loading user settings...'}</p>
        </div>
      </div>
    )
  }

  if (!project) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-red-600 mb-4">{tProjects('projectDoesNotExist')}</h2>
          <Link href={`/${locale}/projects`}>
            <Button>{tProjects('backToList')}</Button>
          </Link>
        </div>
      </div>
    )
  }

  if (!userSettings) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-gray-900 mx-auto"></div>
          <p className="mt-4">Loading user settings...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="flex flex-col min-h-screen bg-white">
      <main className="flex-1 p-6">
        <div className="max-w-6xl mx-auto">
          <div>
            <Link href={`/${locale}/projects/${id}/estimate`} className="text-blue-500 hover:underline">
              ← {t('back')}
            </Link>
          </div>
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold">{tEstimate('previewTitle')}: {project.name}</h2>
          </div>
          
          {error && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
              {error}
            </div>
          )}
          
          <div className="bg-white rounded-lg border overflow-hidden">
            <div className="p-4 border-b bg-gray-50">
              <h3 className="text-lg font-medium">{tEstimate('previewSubtitle')}</h3>
              <p className="text-sm text-gray-500 mt-1">{tEstimate('previewDescription')}</p>
            </div>
            
            <div className="p-6">
              <div className="mb-4">
                <div className="flex items-center space-x-4 mb-2">
                  <div className="flex items-center">
                    <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                    <span className="text-sm">{tEstimate('mustHave')}</span>
                  </div>
                  <div className="flex items-center">
                    <div className="w-3 h-3 bg-yellow-500 rounded-full mr-2"></div>
                    <span className="text-sm">{tEstimate('niceToHave')}</span>
                  </div>
                </div>
              </div>

              <div className="border rounded-md overflow-hidden">
                <div className="overflow-x-auto max-w-full">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="w-12 px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider sticky left-0 bg-gray-50">
                          {tEstimate('select')}
                        </th>
                        <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          {tEstimate('feature')}
                        </th>
                        <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          {tEstimate('priority')}
                        </th>
                        <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-pre-line">
                          {tEstimate('backendEffort')}
                        </th>
                        <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-pre-line">
                          {tEstimate('frontendEffort')}
                        </th>
                        <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-pre-line">
                          {tEstimate('unitTestEffort')}
                        </th>
                        <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          {tEstimate('description')}
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {developmentItems.map((item, index) => (
                        <tr key={index} className={item.priority === "MUST_HAVE" ? "bg-green-50" : "bg-yellow-50"}>
                          <td className=" px-3 py-4 whitespace-nowrap text-center sticky left-0 z-10" style={{backgroundColor: item.priority === "MUST_HAVE" ? '#f0fdf4' : '#fefce8'}}>
                            <div className="w-12">
                              <Checkbox
                                  checked={item.is_selected}
                                  onCheckedChange={() => handleItemSelection(index)}
                                  disabled={item.priority === "MUST_HAVE"}
                                  className="mx-auto"
                              />
                            </div>
                          </td>
                          <td className="px-3 py-4 whitespace-nowrap font-medium">
                              {item.feature}
                          </td>
                          <td className="px-3 py-4 whitespace-nowrap">
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                              item.priority === "MUST_HAVE" ? "bg-green-100 text-green-800" : "bg-yellow-100 text-yellow-800"
                            }`}>
                              {item.priority === "MUST_HAVE" ? tEstimate('mustHave') : tEstimate('niceToHave')}
                            </span>
                          </td>
                          <td className="px-3 py-4 whitespace-nowrap">
                            <input
                              type="number"
                              value={item.backend_effort}
                              onChange={(e) => updateDevelopmentItem(index, "backend_effort", e.target.value)}
                              className="border rounded px-2 py-1 w-20"
                            />
                          </td>
                          <td className="px-3 py-4 whitespace-nowrap">
                            <input
                              type="number"
                              value={item.frontend_effort}
                              onChange={(e) => updateDevelopmentItem(index, "frontend_effort", e.target.value)}
                              className="border rounded px-2 py-1 w-20"
                            />
                          </td>
                          <td className="px-3 py-4 whitespace-nowrap">
                            <input
                              type="number"
                              value={item.unit_test_effort}
                              onChange={(e) => updateDevelopmentItem(index, "unit_test_effort", e.target.value)}
                              className="border rounded px-2 py-1 w-20"
                            />
                          </td>
                          <td className="px-3 py-4 max-w-md">
                            <td className="px-3 py-4 max-w-md">
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                    <div className="truncate">
                                    {item.description}
                                  </div>
                                </TooltipTrigger>
                                <TooltipContent
                                    className="max-w-md max-h-60 overflow-y-auto p-3 bg-white border border-gray-200 shadow-lg rounded-md"
                                    side="top"
                                    align="start"
                                >
                                  <pre className="whitespace-pre-wrap font-sans text-sm">
                                    {item.description}
                                  </pre>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                            </td>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>

              <div className="mt-6">
                <h4 className="text-lg font-medium mb-4">{tEstimate('summary')}</h4>
                <div className="border rounded-md overflow-hidden">
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {tEstimate('select')}
                          </th>
                          <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {tEstimate('category')}
                          </th>
                          <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {tEstimate('role')}
                          </th>
                          <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {tEstimate('effort')}
                          </th>
                          <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20">
                            {tEstimate('dayRate')} ({getCurrencySymbol(userSettings?.currency)})
                          </th>
                          <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {tEstimate('totalCost')} ({getCurrencySymbol(userSettings?.currency)})
                          </th>
                          <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {tEstimate('description')}
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {summaryItems.map((item, index) => (
                          <tr key={index} className={item.category === "Development" ? "bg-blue-50" : ""}>
                            <td className="px-3 py-4 whitespace-nowrap text-center">
                              <Checkbox
                                checked={item.is_selected === undefined ? true : item.is_selected}
                                onCheckedChange={(checked) => {
                                  const updatedItems = [...summaryItems];
                                  
                                  // Tìm index thực sự của item trong mảng summaryItems
                                  const realIndex = summaryItems.findIndex(i => i.category === item.category);
                                  if (realIndex !== -1) {
                                    updatedItems[realIndex] = {
                                      ...updatedItems[realIndex],
                                      // Chuyển đổi từ 'indeterminate' thành boolean xác định
                                      is_selected: checked === true
                                    };
                                    
                                    setSummaryItems(updatedItems);
                                    
                                    // Luôn recalculate sau khi thay đổi trạng thái is_selected
                                    setTimeout(() => {
                                      recalculateTotals(developmentItems, updatedItems);
                                    }, 0);
                                  } else {
                                    console.error(`Could not find item with category ${item.category}`);
                                  }
                                }}
                                className="mx-auto"
                              />
                            </td>
                            <td className="px-3 py-4 whitespace-nowrap font-medium">{item.category}</td>
                            <td className="px-3 py-4 whitespace-nowrap">{item.role}</td>
                            <td className="px-3 py-4 whitespace-nowrap">
                              {item.effort}
                            </td>
                            <td className="px-3 py-4 whitespace-nowrap">
                              <div className="min-w-[90px]">
                                {getCurrencySymbol(userSettings?.currency)}
                                {(() => {
                                  // Sử dụng giá trị day_rate đã có trong item
                                  return item.day_rate?.toLocaleString();
                                })()}
                              </div>
                            </td>
                            <td className="px-3 py-4 whitespace-nowrap">
                              <div className="min-w-[100px]">
                                {getCurrencySymbol(userSettings?.currency)}
                                {(() => {
                                  // Sử dụng giá trị cost đã tính trong item
                                  return item.cost?.toLocaleString();
                                })()}
                              </div>

                            </td>
                            <td className="px-3 py-4 max-w-md">
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <div className="truncate">
                                      {item.description}
                                    </div>
                                  </TooltipTrigger>
                                  <TooltipContent 
                                    className="max-w-md max-h-60 overflow-y-auto p-3 bg-white border border-gray-200 shadow-lg rounded-md"
                                    side="top"
                                    align="start"
                                  >
                                    <pre className="whitespace-pre-wrap font-sans text-sm">
                                      {item.description}
                                    </pre>
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                      <tfoot className="bg-gray-50">
                        <tr>
                          <td className="px-3 py-4 whitespace-nowrap font-medium">{tEstimate('grandTotal')}</td>
                          <td className="px-3 py-4"></td>
                          <td className="px-3 py-4"></td>
                          <td className="px-3 py-4 whitespace-nowrap font-medium">{grandTotal}</td>
                          <td className="px-3 py-4"></td>
                          <td className="px-3 py-4 whitespace-nowrap font-medium">
                            {getCurrencySymbol(userSettings?.currency)}
                            {summaryItems
                              .filter(item => item.is_selected === true)
                              .reduce((sum, item) => sum + (item.cost || 0), 0)
                              .toLocaleString()}
                          </td>
                          <td className="px-3 py-4">{tEstimate('totalManDays')}</td>
                        </tr>
                      </tfoot>
                    </table>
                  </div>
                </div>
              </div>

              <div className="mt-6 flex justify-end space-x-4">
                <Button variant="outline" onClick={() => router.push(`/${locale}/projects/${id}`)}>
                  {t('cancel')}
                </Button>
                <Button onClick={handleConfirm}>
                  {tEstimate('confirm')}
                </Button>
              </div>
            </div>
          </div>
        </div>
      </main>

      <Dialog open={editRequirementsOpen} onOpenChange={setEditRequirementsOpen}>
        <DialogContent className="sm:max-w-[800px]">
          <DialogHeader>
            <DialogTitle>{tProjects("projectRequirements")}</DialogTitle>
            <DialogDescription>
              {tProjects("requirementsPlaceholder")}
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <Textarea
              rows={15}
              value={requirements}
              onChange={(e) => setRequirements(e.target.value)}
              placeholder={tProjects("requirementsPlaceholder")}
            />
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setEditRequirementsOpen(false)}>
              {t('cancel')}
            </Button>
            <Button onClick={saveRequirements} disabled={savingRequirements}>
              {savingRequirements && (
                <span className="mr-2 h-4 w-4 animate-spin">⏳</span>
              )}
              {t('save')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

export default EstimatePreviewPage