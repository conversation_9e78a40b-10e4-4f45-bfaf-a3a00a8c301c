"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { useRouter, useParams } from "next/navigation"
import Link from "next/link"
import { FileUpIcon, Download, FileDownIcon, Plus } from "lucide-react"
import { useTranslations } from "next-intl"
import { Estimate, Project } from '@/types'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import useUserSettings from "@/lib/hooks/useUserSettings"

const ProjectEstimatePage = ({ params }: { params: { id: string, locale: string } }) => {
  const { id, locale } = params
  const router = useRouter()
  const t = useTranslations('common')
  const tProjects = useTranslations('projects')
  const tEstimate = useTranslations('estimate')
  const { userSettings, getCurrencySymbol } = useUserSettings(locale)
  const [project, setProject] = useState<Project | null>(null)
  const [loading, setLoading] = useState(true)
  const [estimating, setEstimating] = useState<boolean>(false)
  const [error, setError] = useState<string | null>(null)
  const [requirementsText, setRequirementsText] = useState<string>("")
  const [estimates, setEstimates] = useState<Estimate[]>([])
  const [selectedEstimateId, setSelectedEstimateId] = useState<number | null>(null)
  const [showEstimateSelector, setShowEstimateSelector] = useState<boolean>(false)

  const fetchProject = async () => {
    try {
      const token = localStorage.getItem("token")
      if (!token) {
        router.push(`/${locale}/login`)
        return
      }
    
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/projects/${id}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })
        
      if (!response.ok) {
        if (response.status === 401) {
          // Clear invalid token and redirect to login
          localStorage.removeItem("token")
          router.push(`/${locale}/login`)
          return
        }
        throw new Error(tProjects('projectDoesNotExist'))
      }
        
      const data = await response.json()
      setProject(data)
        
      // If project has requirements text, set it to the textarea
      if (data.requirements_text) {
        setRequirementsText(data.requirements_text)
      }
    } catch (error) {
      console.error("Error fetching project:", error);
      setError(error instanceof Error ? error.message : t('common.error'));
    } finally {
      setLoading(false);
    }
  }

  const fetchEstimates = async () => {
    try {
      const token = localStorage.getItem("token")
      if (!token) {
        router.push(`/${locale}/login`)
        return
      }

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/projects/${id}/estimates`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (!response.ok) {
        if (response.status === 401) {
          localStorage.removeItem("token")
          router.push(`/${locale}/login`)
          return
        }
        // If no estimates exist yet, this is not an error
        if (response.status === 404) {
          setEstimates([])
          setShowEstimateSelector(true)
          return
        }
        throw new Error("Failed to load estimates")
      }

      const data = await response.json()
      setEstimates(data)

      // If we have estimates, select the first one by default
      if (data.length > 0) {
        setSelectedEstimateId(data[0].id)
        // Don't call fetchEstimateDetails here as we don't have it defined yet
        // Instead, redirect to the estimate detail page
        router.push(`/${locale}/projects/${id}/estimate/${data[0].id}`)
      } else {
        // If no estimates, show the estimate selector
        setShowEstimateSelector(true)
      }
    } catch (error) {
      console.error("Error fetching estimates:", error);
      // Don't show this error to the user as it's not critical
    }
  }

  const createNewEstimate = async () => {
    try {
      const token = localStorage.getItem("token")
      if (!token) {
        router.push(`/${locale}/login`)
        return
      }

      setEstimating(true)

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/projects/${id}/estimates`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          name: `Estimate ${new Date().toLocaleDateString()}`,
          day_rate: userSettings?.role_rates?.Developer || 500,
          currency: userSettings?.currency || 'USD'
        })
      })

      if (!response.ok) {
        if (response.status === 401) {
          localStorage.removeItem("token")
          router.push(`/${locale}/login`)
          return
        }
        throw new Error("Failed to create new estimate")
      }

      const data = await response.json()
      
      // Redirect to the new estimate
      router.push(`/${locale}/projects/${id}/estimate/${data.id}`)
    } catch (error) {
      setError(error instanceof Error ? error.message : t('common.error'));
    } finally {
      setEstimating(false)
    }
  }

  useEffect(() => {
    const token = localStorage.getItem("token")
    if (!token) {
      router.push(`/${locale}/login`)
      return
    }
    fetchProject()
    fetchEstimates()
  }, [id, locale, router])

  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-gray-900 mx-auto"></div>
          <p className="mt-4">{t('common.loading')}</p>
        </div>
      </div>
    )
  }

  if (!project) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-red-600 mb-4">{t('projects.projectDoesNotExist')}</h2>
          <Link href={`/${locale}/projects`}>
            <Button>{t('projects.backToList')}</Button>
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="flex flex-col min-h-screen bg-white">
      {/* Main Content */}
      <main className="flex-1 p-6">
        <div className="max-w-6xl mx-auto">
          <div>
            <Link href={`/${locale}/projects/${id}`} className="text-blue-500 hover:underline">
              ← {t('backToList')}
            </Link>
            <h1 className="text-2xl font-bold mt-4 mb-6">{project.name} - {tEstimate('estimates')}</h1>
          </div>
          
          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6">
              {error}
            </div>
          )}
          
          {showEstimateSelector && (
            <div className="bg-white rounded-lg border p-6 mb-6">
              <h3 className="text-lg font-medium mb-4">{t('estimate.selectEstimate')}</h3>
              
              {estimates.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {estimates.map(estimate => (
                    <div key={estimate.id} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                      <h4 className="font-medium">{estimate.name}</h4>
                      <p className="text-sm text-gray-500 mb-2">
                        {new Date(estimate.created_at).toLocaleDateString()}
                      </p>
                      <div className="flex justify-end">
                        <Link href={`/${locale}/projects/${id}/estimate/${estimate.id}`}>
                          <Button variant="outline" size="sm">
                            {tProjects("viewDetails")}
                          </Button>
                        </Link>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500 mb-4">{t('estimate.noEstimates')}</p>
              )}
              
              <div className="mt-6">
                <Button onClick={createNewEstimate} disabled={estimating}>
                  {estimating ? t('common.loading') : t('estimate.createNewEstimate')}
                </Button>
              </div>
            </div>
          )}
          
          {!showEstimateSelector && estimates.length === 0 && (
            <div className="bg-white rounded-lg border p-6 mb-6">
              <h3 className="text-lg font-medium mb-4">{t('estimate.noEstimates')}</h3>
              <Button onClick={createNewEstimate} disabled={estimating}>
                {estimating ? t('common.loading') : t('estimate.createNewEstimate')}
              </Button>
            </div>
          )}
        </div>
      </main>
    </div>
  )
}

export default ProjectEstimatePage