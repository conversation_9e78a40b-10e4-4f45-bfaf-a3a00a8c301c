"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { useToast } from "@/components/ui/use-toast"
import { useRouter, useParams } from "next/navigation"
import Link from "next/link"
import { useTranslations } from "next-intl"
import { Estimate, Project } from '@/types'
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import useUserSettings from "@/lib/hooks/useUserSettings"
import {CurrencyType} from "@/lib/types"
import { CustomNumberInput } from "@/components/ui/custom-number-input"

interface DevelopmentItem {
  feature: string
  description: string
  backend_effort: number
  frontend_effort: number
  unit_test_effort: number
  priority?: "MUST_HAVE" | "NICE_TO_HAVE"
  is_selected?: boolean
}

interface SummaryItem {
  category: string
  effort: number
  description: string
  role?: string
  day_rate?: number
  cost?: number
  is_selected?: boolean
}

const EditEstimatePage = () => {
  const params = useParams()
  const id = params.id as string
  const estimateId = params.estimateId as string
  const locale = params.locale as string
  const router = useRouter()
  const t = useTranslations('common')
  const tProjects = useTranslations('projects')
  const tEstimate = useTranslations('estimate')
  const { toast } = useToast()
  const { userSettings, getCurrencySymbol } = useUserSettings(locale)
  
  const [project, setProject] = useState<Project | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [estimate, setEstimate] = useState<Estimate | null>(null)
  const [estimateName, setEstimateName] = useState<string>('')
  const [currency, setCurrency] = useState<string>('USD')
  const [activeTab, setActiveTab] = useState<string>("development")
  const [developmentItems, setDevelopmentItems] = useState<DevelopmentItem[]>([])
  const [summaryItems, setSummaryItems] = useState<SummaryItem[]>([])
  const [grandTotal, setGrandTotal] = useState<number>(0)
  const [totalCost, setTotalCost] = useState<number>(0)
  
  // Hàm xác định role từ category
  const getCategoryRole = (category: string): string => {
    const categoryToRoleMap: Record<string, string> = {
      'Backend Development': 'Backend',
      'Frontend Development': 'Frontend',
      'UI/UX Design': 'Designer',
      'Project Management': 'PM',
      'QA Testing': 'QA',
      'DevOps': 'DevOps',
      'Requirements Analysis': 'BA',
      'Architecture': 'Architect',
      'Integration Testing': 'Tester',
      'UAT Support': 'Tester'
    };
    
    return categoryToRoleMap[category] || 'Developer';
  };
  
  const getDefaultDayRateForCategory = (category: string): number => {
    const role = getCategoryRole(category);
    return userSettings?.role_rates?.[role] || 500;
  };
  
  const fetchProject = async () => {
    try {
      const token = localStorage.getItem("token")
      if (!token) {
        router.push(`/${locale}/login`)
        return
      }
    
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/projects/${id}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })
        
      if (!response.ok) {
        if (response.status === 401) {
          localStorage.removeItem("token")
          router.push(`/${locale}/login`)
          return
        }
        throw new Error(tProjects('projectDoesNotExist'))
      }
        
      const data = await response.json()
      setProject(data)
    } catch (error) {
      console.error("Error fetching project:", error);
      setError(error instanceof Error ? error.message : t('error'));
    }
  }

  const fetchEstimateDetails = async () => {
    try {
      const token = localStorage.getItem("token")
      if (!token) {
        router.push(`/${locale}/login`)
        return
      }

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/projects/${id}/estimates/${estimateId}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (!response.ok) {
        if (response.status === 401) {
          localStorage.removeItem("token")
          router.push(`/${locale}/login`)
          return
        }
        throw new Error("Failed to load estimate details")
      }

      const data = await response.json()
      setEstimate(data)
      setEstimateName(data.name || '')
      setCurrency(data.currency || (userSettings?.currency || 'USD'))
      
      if (data.development_items) {
        const processedDevItems = data.development_items.map((item: any) => ({
          ...item,
          is_selected: item.is_selected !== undefined ? item.is_selected : true
        }));
        setDevelopmentItems(processedDevItems);
      }
        
      if (data.summary_items) {
        // Đảm bảo mỗi summary item có day_rate phù hợp với role của nó
        const processedSummaryItems = data.summary_items.map((item: any) => {
          // Xác định role từ category
          const role = getCategoryRole(item.category);
          // Lấy day_rate từ user settings nếu không có sẵn
          const itemDayRate = item.day_rate || (userSettings?.role_rates?.[role] || getDefaultDayRateForCategory(item.category));
          
          return {
            ...item,
            day_rate: itemDayRate,
            is_selected: item.is_selected !== undefined ? item.is_selected : true
          };
        });
        
        setSummaryItems(processedSummaryItems);
        recalculateTotals(processedSummaryItems);
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : t('error'));
    } finally {
      setLoading(false);
    }
  }
  
  const recalculateTotals = (items: SummaryItem[]) => {
    
    // Lọc các items được chọn
    const selectedItems = items.filter(item => item.is_selected === true);
    
    // Tính toán lại grand total dựa trên các items được chọn
    const newGrandTotal = selectedItems.reduce((sum, item) => {
      // Đảm bảo effort là số hợp lệ
      const effort = parseFloat(item.effort?.toString() || '0') || 0;
      return sum + effort;
    }, 0);
    
    const formattedGrandTotal = parseFloat(newGrandTotal.toFixed(1));
    
    // Tính toán lại total cost dựa trên các items được chọn
    const newTotalCost = selectedItems.reduce((sum, item) => {
      // Đảm bảo effort và day_rate là số hợp lệ
      const effort = parseFloat(item.effort?.toString() || '0') || 0;
      const itemDayRate = parseFloat(item.day_rate?.toString() || '0') || getDefaultDayRateForCategory(item.category);
      const itemCost = effort * itemDayRate;
      return sum + itemCost;
    }, 0);
    
    const formattedTotalCost = parseFloat(newTotalCost.toFixed(2));
    
    // Cập nhật state
    setGrandTotal(formattedGrandTotal);
    setTotalCost(formattedTotalCost);
  }
  
  const toggleDevelopmentItem = (index: number) => {
    // Tạo bản sao mới của development items để tránh tham chiếu
    const updatedDevItems = JSON.parse(JSON.stringify(developmentItems));
    
    // Đảo trạng thái is_selected
    updatedDevItems[index].is_selected = !updatedDevItems[index].is_selected;
    
    // Cập nhật state development items
    setDevelopmentItems(updatedDevItems);
    
    // Cập nhật summary items dựa trên development items
    updateSummaryItemsBasedOnDevelopmentItems(updatedDevItems);
  }
  
  // Hàm mới để cập nhật summary items dựa trên development items
  const updateSummaryItemsBasedOnDevelopmentItems = (devItems: DevelopmentItem[]) => {
    
    // Bước 1: Lọc các development items đã được chọn
    const selectedDevItems = devItems.filter(item => item.is_selected === true);
    
    // Bước 2: Tính tổng effort cho backend, frontend, unit test
    let totalBackendEffort = 0;
    let totalFrontendEffort = 0;
    let totalUnitTestEffort = 0;
    
    for (const item of selectedDevItems as DevelopmentItem[]) {
      const backendEffort = parseFloat(String(item.backend_effort || '0')) || 0;
      const frontendEffort = parseFloat(String(item.frontend_effort || '0')) || 0;
      const unitTestEffort = parseFloat(String(item.unit_test_effort || '0')) || 0;
      
      totalBackendEffort += backendEffort;
      totalFrontendEffort += frontendEffort;
      totalUnitTestEffort += unitTestEffort;
    }
    
    // Bước 3: Tạo bản sao mới của summary items
    const updatedSummaryItems = JSON.parse(JSON.stringify(summaryItems));
    
    // Bước 4: Cập nhật effort cho các summary items dựa trên category
    for (let i = 0; i < updatedSummaryItems.length; i++) {
      const item = updatedSummaryItems[i];
      const category = String(item.category || '');
      
      if (category.includes('Development')) {
        updatedSummaryItems[i].effort = totalBackendEffort + totalFrontendEffort + totalUnitTestEffort;
      }
    }
    
    // Bước 5: Lọc các summary items đã được chọn
    const selectedSummaryItems = updatedSummaryItems.filter((item: SummaryItem) => item.is_selected === true);
    
    // Bước 6: Tính toán lại grand total
    let newGrandTotal = 0;
    
    for (const item of selectedSummaryItems as SummaryItem[]) {
      const effort = parseFloat(String(item.effort || '0')) || 0;
      newGrandTotal += effort;
    }
    
    const calculatedGrandTotal = parseFloat(newGrandTotal.toFixed(1));
    
    // Bước 7: Tính toán lại total cost
    let newTotalCost = 0;
    
    for (const item of selectedSummaryItems as SummaryItem[]) {
      const effort = parseFloat(String(item.effort || '0')) || 0;
      const itemDayRate = parseFloat(String(item.day_rate || '0')) || getDefaultDayRateForCategory(String(item.category || ''));
      const itemCost = effort * itemDayRate;
      
      newTotalCost += itemCost;
    }
    
    const calculatedTotalCost = parseFloat(newTotalCost.toFixed(2));
    
    // Bước 8: Cập nhật state
    setSummaryItems(updatedSummaryItems);
    setGrandTotal(calculatedGrandTotal);
    setTotalCost(calculatedTotalCost);
  }
  
  const toggleSummaryItem = (index: number) => {
    const updatedItems = [...summaryItems];
    updatedItems[index].is_selected = !updatedItems[index].is_selected;
    setSummaryItems(updatedItems);
    
    recalculateTotals(updatedItems);
  }
  
  const updateDevelopmentItem = (index: number, field: keyof DevelopmentItem, value: any) => {
    const updatedItems = [...developmentItems];
    updatedItems[index] = { ...updatedItems[index], [field]: value };
    setDevelopmentItems(updatedItems);
    
    // Update summary items based on development items changes
    updateSummaryItemsBasedOnDevelopmentItems(updatedItems);
  }
  
  const updateSummaryItem = (index: number, field: keyof SummaryItem, value: any) => {
    const updatedItems = [...summaryItems];
    updatedItems[index] = {
      ...updatedItems[index],
      [field]: value
    };
    
    // If role changes, update day rate based on new role
    if (field === 'role' && userSettings?.role_rates?.[value]) {
      updatedItems[index].day_rate = userSettings.role_rates[value];
    }
    
    // If category changes, update role based on category
    if (field === 'category') {
      const newRole = getCategoryRole(value);
      updatedItems[index].role = newRole;
      
      // Update day rate based on new role
      if (userSettings?.role_rates?.[newRole]) {
        updatedItems[index].day_rate = userSettings.role_rates[newRole];
      }
    }
    
    setSummaryItems(updatedItems);
    recalculateTotals(updatedItems);
  }
  
  const saveEstimate = async () => {
    try {
      setSaving(true);
      
      const token = localStorage.getItem("token");
      if (!token) {
        router.push(`/${locale}/login`);
        return;
      }
      
      // Step 1: Create a copy of development items
      const clonedDevItems = JSON.parse(JSON.stringify(developmentItems));
      
      // Step 2: Filter selected development items
      const selectedDevItems = clonedDevItems.filter((item: DevelopmentItem) => item.is_selected === true);
      
      // Step 3: Calculate total effort for backend, frontend, unit test
      let totalBackendEffort = 0;
      let totalFrontendEffort = 0;
      let totalUnitTestEffort = 0;
      
      for (const item of selectedDevItems) {
        const backendEffort = parseFloat(String(item.backend_effort || '0')) || 0;
        const frontendEffort = parseFloat(String(item.frontend_effort || '0')) || 0;
        const unitTestEffort = parseFloat(String(item.unit_test_effort || '0')) || 0;
        
        totalBackendEffort += backendEffort;
        totalFrontendEffort += frontendEffort;
        totalUnitTestEffort += unitTestEffort;
      }
      
      // Step 4: Create a copy of summary items
      const clonedSummaryItems = JSON.parse(JSON.stringify(summaryItems));
      
      // Step 5: Update effort for summary items based on category
      for (let i = 0; i < clonedSummaryItems.length; i++) {
        const item = clonedSummaryItems[i];
        const category = String(item.category || '');
        
        if (category.includes('Backend')) {
          clonedSummaryItems[i].effort = totalBackendEffort;
        } else if (category.includes('Frontend')) {
          clonedSummaryItems[i].effort = totalFrontendEffort;
        } else if (category.includes('Testing') || category.includes('Unit Test')) {
          clonedSummaryItems[i].effort = totalUnitTestEffort;
        } else {
        }
      }
      
      // Step 6: Filter selected summary items
      const selectedSummaryItems = clonedSummaryItems.filter((item: DevelopmentItem) => item.is_selected === true);
      
      // Step 7: Calculate grand total
      let newGrandTotal = 0;
      
      for (const item of selectedSummaryItems) {
        const effort = parseFloat(String(item.effort || '0')) || 0;
        newGrandTotal += effort;
      }
      
      const calculatedGrandTotal = parseFloat(newGrandTotal.toFixed(1));
      
      // Step 8: Calculate total cost
      let newTotalCost = 0;
      
      for (const item of selectedSummaryItems) {
        const effort = parseFloat(String(item.effort || '0')) || 0;
        const itemDayRate = parseFloat(String(item.day_rate || '0')) || getDefaultDayRateForCategory(String(item.category || ''));
        const itemCost = effort * itemDayRate;
        
        newTotalCost += itemCost;
      }
      
      const calculatedTotalCost = parseFloat(newTotalCost.toFixed(2));
      
      // Step 9: Create payload with updated data
      const payload = {
        name: estimateName,
        currency: currency,
        development_items: clonedDevItems,
        summary_items: clonedSummaryItems,
        grand_total: calculatedGrandTotal,
        total_cost: calculatedTotalCost
      };
      
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/projects/${id}/estimates/${estimateId}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
      });
      
      if (!response.ok) {
        if (response.status === 401) {
          localStorage.removeItem("token");
          router.push(`/${locale}/login`);
          return;
        }
        throw new Error("Failed to update estimate");
      }
      
      // Update state with new values
      setSummaryItems(clonedSummaryItems);
      setGrandTotal(calculatedGrandTotal);
      setTotalCost(calculatedTotalCost);
      
      toast({
        title: t('success'),
        description: tEstimate('estimateSaved') || 'Estimate saved successfully',
        variant: 'default',
      });
      
      // Redirect user to estimate details page
      router.push(`/${locale}/projects/${id}/estimate/${estimateId}`);
    } catch (error) {
      console.error("Error saving estimate:", error);
      setError(error instanceof Error ? error.message : t('error'));
      setSaving(false);
    }
  };

  useEffect(() => {
    fetchProject();
    fetchEstimateDetails();
    
    // Set default values from user settings
    if (userSettings) {
      if (!currency && userSettings.currency) {
        setCurrency(userSettings.currency);
      }
    }
  }, [id, estimateId, locale, router, userSettings]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-gray-900 mx-auto"></div>
          <p className="mt-4">{t('loading')}</p>
        </div>
      </div>
    );
  }

  if (!project || !estimate) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-red-600 mb-4">{error || t('projects.projectDoesNotExist')}</h2>
          <Link href={`/${locale}/projects`}>
            <Button>{t('backToList')}</Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col min-h-screen bg-white">
      {/* Main Content */}
      <main className="flex-1 p-6">
        <div className="max-w-6xl mx-auto">
          <div>
            <Link href={`/${locale}/projects/${id}/estimate/${estimateId}`} className="text-blue-500 hover:underline">
              ← {tEstimate('backToDetails')}
            </Link>
            <h1 className="text-2xl font-bold mt-4 mb-6">{project.name} - {tEstimate('editEstimate')}</h1>
          </div>
          
          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6">
              {error}
            </div>
          )}
          
          <div className="bg-white rounded-lg border overflow-hidden mb-6">
            <div className="p-4 border-b bg-gray-50">
              <h3 className="text-lg font-medium">{tEstimate('estimateDetails')}</h3>
            </div>
            
            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                <div>
                  <Label htmlFor="estimate-name">{tEstimate('name')}</Label>
                  <Input 
                    id="estimate-name"
                    value={estimateName}
                    onChange={(e) => setEstimateName(e.target.value)}
                    className="mt-1"
                  />
                </div>
                <div>
                  <Label htmlFor="currency">{tEstimate('currency')}</Label>
                  <select
                    id="currency"
                    value={currency}
                    onChange={(e) => setCurrency(e.target.value)}
                    className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 mt-1"
                  >
                    <option value="USD">USD ($)</option>
                    <option value="JPY">JPY (¥)</option>
                    <option value="VND">VND (₫)</option>
                  </select>
                </div>
              </div>
              
              <Tabs defaultValue="development" className="w-full" onValueChange={setActiveTab}>
                <TabsList className="mb-4">
                  <TabsTrigger value="development">{tEstimate('developmentItems')}</TabsTrigger>
                  <TabsTrigger value="summary">{tEstimate('summaryItems')}</TabsTrigger>
                </TabsList>
                
                <TabsContent value="development" className="space-y-4">
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {tEstimate('include')}
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {tEstimate('feature')}
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {tEstimate('description')}
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-pre-line">
                            {tEstimate('backendEffort')}
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-pre-line">
                            {tEstimate('frontendEffort')}
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-pre-line">
                            {tEstimate('unitTestEffort')}
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {developmentItems.map((item, index) => (
                          <tr key={index} className={!item.is_selected ? "opacity-50" : ""}>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <Checkbox 
                                checked={item.is_selected} 
                                onCheckedChange={() => toggleDevelopmentItem(index)}
                              />
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                              <Input 
                                value={item.feature}
                                onChange={(e) => updateDevelopmentItem(index, 'feature', e.target.value)}
                              />
                            </td>
                            <td className="px-6 py-4 text-sm text-gray-500">
                              <Input 
                                value={item.description}
                                onChange={(e) => updateDevelopmentItem(index, 'description', e.target.value)}
                              />
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              <CustomNumberInput
                                value={item.backend_effort}
                                onChange={(value) => updateDevelopmentItem(index, 'backend_effort', value)}
                                onBlur={(value) => updateDevelopmentItem(index, 'backend_effort', value)}
                              />
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              <CustomNumberInput
                                value={item.frontend_effort}
                                onChange={(value) => updateDevelopmentItem(index, 'frontend_effort', value)}
                                onBlur={(value) => updateDevelopmentItem(index, 'frontend_effort', value)}
                              />
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              <CustomNumberInput
                                value={item.unit_test_effort}
                                onChange={(value) => updateDevelopmentItem(index, 'unit_test_effort', value)}
                                onBlur={(value) => updateDevelopmentItem(index, 'unit_test_effort', value)}
                              />
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </TabsContent>
                
                <TabsContent value="summary" className="space-y-4">
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {tEstimate('include')}
                          </th>
                          <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {tEstimate('category')}
                          </th>
                          <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {tEstimate('description')}
                          </th>
                          <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {tEstimate('effort')}
                          </th>
                          <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {tEstimate('role')}
                          </th>
                          <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {tEstimate('dayRate')}
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {summaryItems.map((item, index) => (
                          <tr key={index} className={!item.is_selected ? "opacity-50" : ""}>
                            <td className="px-4 py-4 whitespace-nowrap">
                              <Checkbox 
                                checked={item.is_selected} 
                                onCheckedChange={() => toggleSummaryItem(index)}
                              />
                            </td>
                            <td className="px-4 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                              <Input 
                                value={item.category}
                                onChange={(e) => updateSummaryItem(index, 'category', e.target.value)}
                              />
                            </td>
                            <td className="px-4 py-4 text-sm text-gray-500">
                              <Input 
                                value={item.description}
                                onChange={(e) => updateSummaryItem(index, 'description', e.target.value)}
                              />
                            </td>
                            <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                              <CustomNumberInput
                                value={item.effort}
                                onChange={(value) => updateSummaryItem(index, 'effort', value)}
                                className="w-20"
                              />
                            </td>
                            <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                              <select
                                value={item.role || getCategoryRole(item.category)}
                                onChange={(e) => updateSummaryItem(index, 'role', e.target.value)}
                                className="w-full rounded-md border border-gray-300 py-2 px-3 text-sm"
                              >
                                {Object.keys(userSettings?.role_rates || {}).map((role) => (
                                  <option key={role} value={role}>
                                    {tEstimate(`roles.${role}`)}
                                  </option>
                                ))}
                              </select>
                            </td>
                            <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                              <CustomNumberInput
                                value={item.day_rate || (userSettings?.role_rates?.[item.role || getCategoryRole(item.category)] || 0)}
                                onChange={(value) => updateSummaryItem(index, 'day_rate', value)}
                                className="w-36"
                              />
                            </td>
                          </tr>
                        ))}
                        <tr className="bg-gray-50 font-medium">
                          <td colSpan={4} className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                            {tEstimate('grandTotal')}
                          </td>
                          <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                            {grandTotal.toFixed(1)}
                          </td>
                          <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                            {getCurrencySymbol(currency as CurrencyType)}{Math.round(totalCost).toLocaleString()}
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </TabsContent>
              </Tabs>
              
              <div className="mt-6 flex justify-end space-x-3">
                <Button variant="outline" onClick={() => router.push(`/${locale}/projects/${id}/estimate/${estimateId}`)}>
                  {t('cancel')}
                </Button>
                <Button onClick={saveEstimate} disabled={saving} className="bg-black text-white hover:bg-gray-800">
                  {saving ? t('saving') : t('save')}
                </Button>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default EditEstimatePage;
