"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { useRouter, useParams } from "next/navigation"
import Link from "next/link"
import { FileUpIcon, Download, FileDownIcon, FileSpreadsheet } from "lucide-react"
import { useTranslations } from 'next-intl';

const formatNumber = (num: number) => {
  return num.toLocaleString('en-US', {
    minimumFractionDigits: 0,
    maximumFractionDigits: 2
  });
};

import { Estimate, Project } from '@/types'
import { CurrencyType } from '@/lib/types'
import { toast } from "@/components/ui/use-toast"
// Use dynamic import for exceljs
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialog<PERSON><PERSON><PERSON>,
  AlertDialogD<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>D<PERSON>og<PERSON>ead<PERSON>,
  AlertDialogT<PERSON>le,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import useUserSettings from "@/lib/hooks/useUserSettings"

interface DevelopmentItem {
  feature: string
  description: string
  backend_effort: number
  frontend_effort: number
  unit_test_effort: number
  priority?: "MUST_HAVE" | "NICE_TO_HAVE"
  is_selected?: boolean
}

interface SummaryItem {
  category: string
  effort: number
  description: string
  role?: string
  day_rate?: number
  cost?: number
  is_selected?: boolean
}

const ProjectEstimateDetailPage = () => {
  const params = useParams()
  const id = params.id as string
  const estimateId = params.estimateId as string
  const locale = params.locale as string
  const router = useRouter()
  const t = useTranslations('common')
  const tProjects = useTranslations('projects')
  const tEstimate = useTranslations('estimate')
  const { userSettings, getCurrencySymbol } = useUserSettings(locale)
  const [project, setProject] = useState<Project | null>(null)
  const [loading, setLoading] = useState(true)
  const [estimating, setEstimating] = useState<boolean>(false)
  const [error, setError] = useState<string | null>(null)
  const [estimate, setEstimate] = useState<Estimate | null>(null)
  const [activeTab, setActiveTab] = useState<string>("development")
  const [developmentItems, setDevelopmentItems] = useState<DevelopmentItem[]>([])
  const [summaryItems, setSummaryItems] = useState<SummaryItem[]>([])
  const [grandTotal, setGrandTotal] = useState<number>(0)
  const [userToken, setUserToken] = useState<string | null>(null)
  const [dayRate, setDayRate] = useState<number>(500) // Default day rate is $500
  const [totalCost, setTotalCost] = useState<number>(0)
  const [exportingPdf, setExportingPdf] = useState<boolean>(false)
  const [exportingExcel, setExportingExcel] = useState<boolean>(false)

  // Function to translate category to the current language
  const translateCategory = (category: string): string => {
    // Mapping English categories to keys in the language file
    const categoryMap: Record<string, string> = {
      'Requirements Definition': 'summaryCategories.requirementsDefinition',
      'Development': 'summaryCategories.development',
      'UI/UX Design': 'summaryCategories.uiUxDesign',
      'Integration Test': 'summaryCategories.integrationTest',
      'UAT Support': 'summaryCategories.uatSupport',
      'Project Management': 'summaryCategories.projectManagement'
    }
    
    // If there is a key mapping, use the translation function, otherwise return the original
    return categoryMap[category] ? tEstimate(categoryMap[category]) : category
  }
  
  // Function to translate description to the current language
  const translateDescription = (description: string): string => {
    // Mapping English descriptions to keys in the language file
    const descriptionMap: Record<string, string> = {
      'Total development effort': 'summaryDescriptions.totalDevelopment',
      'Requirements gathering and analysis': 'summaryDescriptions.requirementsAnalysis',
      'User interface and experience design': 'summaryDescriptions.uiUxDesign',
      'Integration and system testing': 'summaryDescriptions.integrationTesting',
      'User acceptance testing support': 'summaryDescriptions.uatSupport',
      'Overall project management activities': 'summaryDescriptions.projectManagement'
    }
    
    // If there is a key mapping, use the translation function, otherwise return the original
    return descriptionMap[description] ? tEstimate(descriptionMap[description]) : description
  }
  
  // Function to translate development item description
  const translateDevItemDescription = (description: string): string => {
    // In the current case, we only need to translate fixed descriptions
    // If there are other descriptions, we can add them to the mapping
    
    // Based on the selected language
    if (locale === 'ja') {
      // Japanese
      return description
        .replace('Requirements gathering and analysis', '要件収集と分析')
        .replace('User interface and experience design', 'ユーザーインターフェースとエクスペリエンスデザイン')
        .replace('Total development effort', '総開発工数')
        .replace('Integration and system testing', '統合およびシステムテスト')
        .replace('User acceptance testing support', 'ユーザー受け入れテストサポート')
        .replace('Project management overhead', 'プロジェクト管理オーバーヘッド')
    } else if (locale === 'vi') {
      // Tiếng Việt
      return description
        .replace('Requirements gathering and analysis', 'Thu thập và phân tích yêu cầu')
        .replace('User interface and experience design', 'Thiết kế giao diện và trải nghiệm người dùng')
        .replace('Total development effort', 'Tổng công số phát triển')
        .replace('Integration and system testing', 'Kiểm thử tích hợp và hệ thống')
        .replace('User acceptance testing support', 'Hỗ trợ kiểm thử chấp nhận người dùng')
        .replace('Project management overhead', 'Chi phí quản lý dự án')
    }
    
    // Default to English
    return description
  }

  const fetchProject = async () => {
    try {
      const token = localStorage.getItem("token")
      if (!token) {
        router.push(`/${locale}/login`)
        return
      }
    
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/projects/${id}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })
        
      if (!response.ok) {
        if (response.status === 401) {
          // Clear invalid token and redirect to login
          localStorage.removeItem("token")
          router.push(`/${locale}/login`)
          return
        }
        throw new Error(tProjects('projectDoesNotExist'))
      }
        
      const data = await response.json()
      setProject(data)
    } catch (error) {
      console.error("Error fetching project:", error);
      setError(error instanceof Error ? error.message : t('error'));
    } finally {
      setLoading(false);
    }
  }

  const fetchEstimateDetails = async () => {
    try {
      const token = localStorage.getItem("token")
      if (!token) {
        router.push(`/${locale}/login`)
        return
      }

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/projects/${id}/estimates/${estimateId}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (!response.ok) {
        if (response.status === 401) {
          localStorage.removeItem("token")
          router.push(`/${locale}/login`)
          return
        }
        throw new Error("Failed to load estimate details")
      }

      const data = await response.json()
      setEstimate(data)
      
      // Ensure development items maintain is_selected property
      if (data.development_items) {
        const processedDevItems = data.development_items.map((item: any) => ({
          ...item,
          // Keep is_selected from API or default to true if not present
        }));
        
        setDevelopmentItems(processedDevItems);
      }
        
      // Ensure summary items maintain is_selected property
      if (data.summary_items) {
        // Store all items with is_selected property
        const processedSummaryItems = data.summary_items.map((item: any) => ({
          ...item,
          // Keep is_selected from API or default to true if not present
          is_selected: item.is_selected !== undefined ? item.is_selected : true
        }));
        
        setSummaryItems(processedSummaryItems);
        
        // Recalculate grand total based on selected items
        const selectedItems = processedSummaryItems.filter((item: any) => item.is_selected === true);
        
        const newGrandTotal = selectedItems.reduce((sum: number, item: any) => sum + (item.effort || 0), 0);
        
        setGrandTotal(parseFloat(newGrandTotal.toFixed(2)));
        
        // Recalculate total cost based on selected items only
        const newTotalCost = selectedItems.reduce((sum: number, item: any) => {
          const effort = item.effort || 0;
          const itemDayRate = item.day_rate || getDefaultDayRate(item.category);
          return sum + (effort * itemDayRate);
        }, 0);
        
        setTotalCost(parseFloat(newTotalCost.toFixed(2)));
      } else {
        setGrandTotal(data.grand_total || 0);
        setTotalCost(data.total_cost || 0);
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : t('error'));
    }
  }

  // Handle PDF export
  const handleExportPdf = async () => {
    try {
      if (!project || !estimate) {
        return;
      }
      setExportingPdf(true);
      
      // Determine API language
      let apiLanguage = locale;
      if (apiLanguage === 'ja') apiLanguage = 'jp';
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/projects/${id}/estimates/${estimateId}/export-pdf?language=${apiLanguage}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${userToken}`,
        },
      });
      
      if (!response.ok) {
        throw new Error(`Error: ${response.status}`);
      }

      // Get blob from response
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      
      const link = document.createElement('a');
      link.href = url;
      link.download = `${project.name}_${estimate.name}.pdf`;
      
      // Append to the document, click it, and remove it
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Clean up the URL object
      window.URL.revokeObjectURL(url);

      toast({
        title: t('success'),
        description: tProjects('pdfExported'),
      });
    } catch (error) {
      console.error('Error exporting PDF:', error);
      toast({
        title: t('error'),
        description: tProjects('pdfExportFailed'),
        variant: 'destructive'
      });
    } finally {
      setExportingPdf(false);
    }
  };
  
  // Handle Excel export
  const handleExportExcel = async () => {
    try {
      if (!project || !estimate) {
        return;
      }
      setExportingExcel(true);
      
      // Tải ExcelJS từ CDN
      await new Promise((resolve, reject) => {
        if ((window as any).ExcelJS) {
          resolve(true);
          return;
        }
        
        const script = document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/npm/exceljs@4.3.0/dist/exceljs.min.js';
        script.async = true;
        script.onload = () => resolve(true);
        script.onerror = () => reject(new Error('Failed to load ExcelJS'));
        document.head.appendChild(script);
      });
      
      // Use ExcelJS from window object
      const ExcelJS = (window as any).ExcelJS;

      // Function to remove invalid characters for worksheet name
      function sanitizeSheetName(name: string) {
        // Remove invalid special characters for worksheet name
        return name.replace(/[*?:\\/\[\]]/g, '').slice(0, 31); // Excel only allows a maximum of 31 characters.
      }

      // Prepare data for Excel file
      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet(sanitizeSheetName(project.name));
      
      // Create header style
      const headerStyle = {
        font: { bold: true },
        fill: {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'FFE0E0E0' }
        },
        border: {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' }
        }
      };
      
      // Create cell style
      const cellStyle = {
        border: {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' }
        }
      };
      
      // Prepare translated strings
      const projectNameLabel = locale === 'ja' ? 'プロジェクト名' : 
                              locale === 'vi' ? 'Tên dự án' : 'Project Name';
      const estimateNameLabel = locale === 'ja' ? '見積もり名' : 
                               locale === 'vi' ? 'Tên ước tính' : 'Estimate Name';
      const creationDateLabel = locale === 'ja' ? '作成日' : 
                               locale === 'vi' ? 'Ngày tạo' : 'Creation Date';
      
      // Add project and estimate info
      const infoRow1 = worksheet.addRow([projectNameLabel, project.name]);
      infoRow1.eachCell((cell: any) => {
        cell.border = cellStyle.border;
        if (cell.col === 1) {
          cell.font = { bold: true };
        }
      });
      
      const infoRow2 = worksheet.addRow([estimateNameLabel, estimate.name]);
      infoRow2.eachCell((cell: any) => {
        cell.border = cellStyle.border;
        if (cell.col === 1) {
          cell.font = { bold: true };
        }
      });
      
      const infoRow3 = worksheet.addRow([creationDateLabel, new Date(estimate.created_at).toLocaleDateString()]);
      infoRow3.eachCell((cell: any) => {
        cell.border = cellStyle.border;
        if (cell.col === 1) {
          cell.font = { bold: true };
        }
      });
      
      worksheet.addRow([]);
      
      // Prepare summary strings
      const summaryLabel = locale === 'ja' ? '概要' : 
                           locale === 'vi' ? 'Tổng quan' : 'Summary';
      const summaryTotalEffortLabel = locale === 'ja' ? '合計労働時間' : 
                                      locale === 'vi' ? 'Tổng công số' : 'Total Effort';
      const totalCostLabel = locale === 'ja' ? '総コスト' : 
                             locale === 'vi' ? 'Tổng chi phí' : 'Total Cost';
      
      // Thêm thông tin tổng quan
      const summaryTitle = worksheet.addRow([summaryLabel]);
      summaryTitle.font = { bold: true, size: 14 };
      
      const effortRow = worksheet.addRow([summaryTotalEffortLabel, `${grandTotal.toFixed(2)} days`]);
      effortRow.eachCell((cell: any) => {
        cell.border = cellStyle.border;
        if (cell.col === 1) {
          cell.font = { bold: true };
        }
      });
      
      const costRow = worksheet.addRow([totalCostLabel, `${getCurrencySymbol(estimate?.currency as CurrencyType)}${Math.round(totalCost).toLocaleString()}`]);
      costRow.eachCell((cell: any) => {
        cell.border = cellStyle.border;
        if (cell.col === 1) {
          cell.font = { bold: true };
        }
      });
      
      worksheet.addRow([]);
      
      // Prepare development items strings
      const devItemsLabel = locale === 'ja' ? '開発項目' : 
                            locale === 'vi' ? 'Các hạng mục phát triển' : 'Development Items';
      const featureLabel = locale === 'ja' ? '機能' : 
                           locale === 'vi' ? 'Tính năng' : 'Feature';
      const descriptionLabel = locale === 'ja' ? '説明' : 
                               locale === 'vi' ? 'Mô tả chi tiết' : 'Description';
      const backendEffortLabel = locale === 'ja' ? 'バックエンド（人日）' : 
                                 locale === 'vi' ? 'Backend (ngày công)' : 'Backend (man days)';
      const frontendEffortLabel = locale === 'ja' ? 'フロントエンド（人日）' : 
                                  locale === 'vi' ? 'Frontend (ngày công)' : 'Frontend (man days)';
      const unitTestEffortLabel = locale === 'ja' ? '単体テスト（人日）' : 
                                  locale === 'vi' ? 'Kiểm thử đơn vị (ngày công)' : 'Unit Test (man days)';
      const totalEffortLabel = locale === 'ja' ? '合計労働時間' : 
                               locale === 'vi' ? 'Tổng công số' : 'Total Effort';
      
      // Add development items table
      if (developmentItems.length > 0) {
        worksheet.addRow([devItemsLabel]).font = { bold: true, size: 14 };
        const devHeaders = [
          featureLabel,
          descriptionLabel,
          backendEffortLabel,
          frontendEffortLabel,
          unitTestEffortLabel,
          totalEffortLabel
        ];
        
        // Add header with style
        const headerRow = worksheet.addRow(devHeaders);
        headerRow.eachCell((cell: any) => {
          cell.font = { bold: true };
          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'FFE0E0E0' }
          };
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' }
          };
        });
        
        // Add data with border
        const startRow = worksheet.rowCount + 1;
        developmentItems.forEach(item => {
          const totalItemEffort = item.backend_effort + item.frontend_effort + item.unit_test_effort;
          const row = worksheet.addRow([
            item.feature,
            item.description,
            item.backend_effort.toFixed(2),
            item.frontend_effort.toFixed(2),
            item.unit_test_effort.toFixed(2),
            totalItemEffort.toFixed(2)
          ]);
          
          row.eachCell((cell: any) => {
            cell.border = {
              top: { style: 'thin' },
              left: { style: 'thin' },
              bottom: { style: 'thin' },
              right: { style: 'thin' }
            };
          });
        });
        
        worksheet.addRow([]);
      }
      
      // Prepare summary items strings
      const summaryItemsLabel = locale === 'ja' ? '要約項目' : 
                                locale === 'vi' ? 'Các hạng mục tổng hợp' : 'Summary Items';
      const categoryLabel = locale === 'ja' ? 'カテゴリ' : 
                            locale === 'vi' ? 'Phân loại' : 'Category';
      const effortLabel = locale === 'ja' ? '工数' : 
                          locale === 'vi' ? 'Công số dự kiến' : 'Effort';
      const dayRateLabel = locale === 'ja' ? '日紊' : 
                           locale === 'vi' ? 'Đơn giá ngày' : 'Day Rate';
      const costLabel = locale === 'ja' ? 'コスト' : 
                        locale === 'vi' ? 'Chi phí' : 'Cost';
      const grandTotalLabel = locale === 'ja' ? '合計' : 
                              locale === 'vi' ? 'Tổng cộng' : 'Grand Total';
      
      // Add summary items table
      if (summaryItems.length > 0) {
        worksheet.addRow([summaryItemsLabel]).font = { bold: true, size: 14 };
        const summaryHeaders = [
          categoryLabel,
          descriptionLabel,
          effortLabel,
          dayRateLabel,
          costLabel
        ];
        
        // Add header with style
        const headerRow = worksheet.addRow(summaryHeaders);
        headerRow.eachCell((cell: any) => {
          cell.font = { bold: true };
          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'FFE0E0E0' }
          };
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' }
          };
        });
        
        // Add data with border
        summaryItems.forEach(item => {
          const itemCost = (item.effort || 0) * (item.day_rate || getDefaultDayRate(item.category));
          const row = worksheet.addRow([
            item.category,
            item.description,
            item.effort?.toFixed(2),
            `${getCurrencySymbol(estimate?.currency as CurrencyType)}${Math.round(item.day_rate || getDefaultDayRate(item.category)).toLocaleString()}`,
            `${getCurrencySymbol(estimate?.currency as CurrencyType)}${Math.round(itemCost).toLocaleString()}`
          ]);
          
          row.eachCell((cell: any) => {
            cell.border = {
              top: { style: 'thin' },
              left: { style: 'thin' },
              bottom: { style: 'thin' },
              right: { style: 'thin' }
            };
          });
        });
        
        // Add total cost with style
        const totalRow = worksheet.addRow([
          grandTotalLabel,
          '',
          grandTotal.toFixed(2),
          '',
          `${getCurrencySymbol(estimate?.currency as CurrencyType)}${Math.round(totalCost).toLocaleString()}`
        ]);
        
        totalRow.eachCell((cell: any) => {
          cell.font = { bold: true };
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' }
          };
        });
      }
      
      // Format table
      worksheet.columns.forEach((column: any) => {
        column.width = 20;
      });
      
      // Export file Excel
      const buffer = await workbook.xlsx.writeBuffer();
      const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      const url = window.URL.createObjectURL(blob);
      
      const link = document.createElement('a');
      link.href = url;
      link.download = `${project.name}_${estimate.name}.xlsx`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      window.URL.revokeObjectURL(url);
      
      toast({
        title: t('success'),
        description: tProjects('excelExported') || 'Excel file exported successfully',
      });
    } catch (error) {
      console.error('Error exporting Excel:', error);
      toast({
        title: t('error'),
        description: tProjects('excelExportFailed') || 'Failed to export Excel file',
        variant: 'destructive'
      });
    } finally {
      setExportingExcel(false);
    }
  };
  
  // Function to get default day rate based on role/category
  const getDefaultDayRate = (category: string): number => {
    if (!userSettings || !userSettings.role_rates) {
      return 500; // Default fallback
    }
    
    // Map category to role
    const roleMap: Record<string, string> = {
      'Backend Development': 'Backend Developer',
      'Frontend Development': 'Frontend Developer',
      'Design': 'Designer',
      'Project Management': 'Project Manager',
      'QA Testing': 'QA Engineer',
      'DevOps': 'DevOps Engineer'
    };
    
    const role = roleMap[category] || 'Developer';
    return userSettings.role_rates[role] || 500;
  };

  useEffect(() => {
    const token = localStorage.getItem('token');
    if (!token) {
      router.push(`/${locale}/login`);
      return;
    }
    setUserToken(token);
    
    fetchProject();
    fetchEstimateDetails();
  }, [id, estimateId, locale, router]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-gray-900 mx-auto"></div>
          <p className="mt-4">{t('loading')}</p>
        </div>
      </div>
    );
  }

  if (!project || !estimate) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-red-600 mb-4">{error || tProjects('projectDoesNotExist')}</h2>
          <Link href={`/${locale}/projects`}>
            <Button>{tProjects('backToList')}</Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col min-h-screen bg-white">
      {/* Main Content */}
      <main className="flex-1 p-6">
        <div className="max-w-6xl mx-auto">
          <div>
            <Link href={`/${locale}/projects/${id}`} className="text-blue-500 hover:underline">
              ← {t('backToProject')}
            </Link>
            <h1 className="text-2xl font-bold mt-4 mb-6">{project.name} - {estimate.name}</h1>
          </div>
          
          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6">
              {error}
            </div>
          )}
          
          <div className="bg-white rounded-lg border overflow-hidden mb-6">
            <div className="p-4 border-b bg-gray-50 flex justify-between items-center">
              <h3 className="text-lg font-medium">{tEstimate('estimateDetails')}</h3>
              <div className="flex items-center space-x-3">
                <Link href={`/${locale}/projects/${id}/estimate/${estimateId}/edit`}>
                  <Button variant="outline" size="sm" className="flex items-center gap-1">
                    {t('edit')}
                  </Button>
                </Link>
                <div className="flex space-x-2">
                  <Button 
                    variant="outline" 
                    size="sm" 
                    className="flex items-center gap-1"
                    onClick={handleExportPdf}
                    disabled={exportingPdf}
                  >
                    <FileDownIcon className="w-4 h-4" />
                    {exportingPdf ? t('loading') : tProjects('exportPDF')}
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    className="flex items-center gap-1"
                    onClick={handleExportExcel}
                    disabled={exportingExcel}
                  >
                    <FileSpreadsheet className="w-4 h-4" />
                    {exportingExcel ? t('loading') : (tProjects('exportExcel') || 'Export Excel')}
                  </Button>
                </div>
              </div>
            </div>
            
            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div>
                  <h4 className="font-medium mb-2">{tEstimate('estimateInfo')}</h4>
                  <div className="text-sm">
                    <p><span className="font-medium">{tEstimate('name')}:</span> {estimate.name}</p>
                    <p><span className="font-medium">{tEstimate('createdAt')}:</span> {new Date(estimate.created_at).toLocaleDateString()}</p>
                  </div>
                </div>
                <div>
                  <h4 className="font-medium mb-2">{tEstimate('summary')}</h4>
                  <div className="text-sm">
                    <p><span className="font-medium">{tEstimate('totalEffort')}:</span> {formatNumber(Number(grandTotal.toFixed(2)))} days</p>
                    <p><span className="font-medium">{tEstimate('totalCost')}:</span> {getCurrencySymbol(estimate?.currency as CurrencyType)}{Math.round(totalCost).toLocaleString()}</p>
                  </div>
                </div>
              </div>
              
              <Tabs defaultValue="development" className="w-full" onValueChange={setActiveTab}>
                <TabsList className="mb-4">
                  <TabsTrigger value="development">{tEstimate('developmentItems')}</TabsTrigger>
                  <TabsTrigger value="summary">{tEstimate('summaryItems')}</TabsTrigger>
                </TabsList>
                
                <TabsContent value="development" className="space-y-4">
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            <div className="max-w-[180px]">
                              {tEstimate('feature')}
                            </div>
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {tEstimate('description')}
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-pre-line">
                            {tEstimate('backendEffort')}
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-pre-line">
                            {tEstimate('frontendEffort')}
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-pre-line">
                            {tEstimate('unitTestEffort')}
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {tEstimate('totalEffort')}
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {developmentItems.map((item, index) => (
                          <tr key={index} className={!item.is_selected ? "opacity-50" : ""}>
                            <td className="px-6 py-4 text-sm font-medium text-gray-900">
                              <div className="max-w-[180px]">
                                {item.feature}
                              </div>
                            </td>
                            <td className="px-6 py-4 text-sm text-gray-500">
                              {item.description}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 w-20">
                              <div className="min-w-[90px]">
                                {formatNumber(Number(item.backend_effort.toFixed(2)))}
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500  w-20">
                              <div className="min-w-[90px]">
                                {formatNumber(Number(item.frontend_effort.toFixed(2)))}
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {formatNumber(Number(item.unit_test_effort.toFixed(2)))}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {formatNumber(Number((item.backend_effort + item.frontend_effort + item.unit_test_effort).toFixed(2)))}
                            </td>
                          </tr>
                        ))}
                        {/* total by column */}
                        <tr className="bg-gray-100 font-bold">
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900" colSpan={2}>{tEstimate('grandTotal')}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {formatNumber(
                              Number(
                                developmentItems.reduce((sum, item) => sum + item.backend_effort, 0).toFixed(2)
                              )
                            )}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {formatNumber(
                              Number(
                                developmentItems.reduce((sum, item) => sum + item.frontend_effort, 0).toFixed(2)
                              )
                            )}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {formatNumber(
                              Number(
                                developmentItems.reduce((sum, item) => sum + item.unit_test_effort, 0).toFixed(2)
                              )
                            )}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {formatNumber(
                              Number(
                                developmentItems.reduce((sum, item) => sum + item.backend_effort + item.frontend_effort + item.unit_test_effort, 0).toFixed(2)
                              )
                            )}
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </TabsContent>
                
                <TabsContent value="summary" className="space-y-4">
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {tEstimate('category')}
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {tEstimate('description')}
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {tEstimate('effort')}
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {tEstimate('dayRate')}
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {tEstimate('cost')}
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {summaryItems.map((item, index) => (
                          <tr key={index} className={!item.is_selected ? "opacity-50" : ""}>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                              {translateCategory(item.category)}
                            </td>
                            <td className="px-6 py-4 text-sm text-gray-500">
                              {item.description}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {formatNumber(Number(item.effort.toFixed(2)))}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {getCurrencySymbol(estimate?.currency as CurrencyType)}{Math.round(item.day_rate || getDefaultDayRate(item.category)).toLocaleString()}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {getCurrencySymbol(estimate?.currency as CurrencyType)}{Math.round((item.effort || 0) * (item.day_rate || getDefaultDayRate(item.category))).toLocaleString()}
                            </td>
                          </tr>
                        ))}
                        <tr className="bg-gray-50 font-medium">
                          <td colSpan={2} className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {tEstimate('grandTotal')}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {formatNumber(Number(grandTotal.toFixed(2)))}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900"></td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {estimate?.currency === 'JPY'
                              ? `${getCurrencySymbol(estimate?.currency as CurrencyType)}${Math.round(totalCost).toLocaleString()}`
                              : `${getCurrencySymbol(estimate?.currency as CurrencyType)}${totalCost.toLocaleString()}`
                            }
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </TabsContent>
              </Tabs>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default ProjectEstimateDetailPage;
