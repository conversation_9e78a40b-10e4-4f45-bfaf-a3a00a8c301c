"use client"

import { useEffect } from "react"
import { useRouter, useParams } from "next/navigation"

export default function EstimateRedirect() {
  const router = useRouter()
  const params = useParams()
  const { id, locale } = params

  useEffect(() => {
    // Redirect from the old route to the new route
    router.replace(`/${locale}/projects/${id}/estimates`)
  }, [router, id, locale])

  return (
    <div className="flex items-center justify-center h-screen">
      <div className="text-center">
        <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-gray-900 mx-auto"></div>
        <p className="mt-4">Redirecting...</p>
      </div>
    </div>
  )
}
