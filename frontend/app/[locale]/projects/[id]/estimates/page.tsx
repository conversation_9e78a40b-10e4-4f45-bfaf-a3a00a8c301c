"use client"

import { useEffect } from "react"
import { useRouter, useParams } from "next/navigation"

export default function ProjectEstimatesRedirect() {
  const params = useParams()
  const { locale, id } = params
  const router = useRouter()
  
  useEffect(() => {
    // Redirect to the project details page
    router.replace(`/${locale}/projects/${id}`)
  }, [locale, id, router])
  
  return null // No UI needed for redirect
}
