"use client"

import { useState, use<PERSON>ffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { use<PERSON><PERSON><PERSON>, use<PERSON>ara<PERSON> } from "next/navigation"
import Link from "next/link"
import { CalendarIcon, Code2Icon, PackageIcon, Trash2Icon, FileDownIcon } from "lucide-react"
import { useTranslations } from 'next-intl'
import { getLocaleFromPathname } from '@/lib/locale'
import { usePathname } from 'next/navigation'
import { format } from 'date-fns'
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { AlertDialog, AlertDialogContent, AlertDialogHeader, AlertDialogTitle, AlertDialogDescription, AlertDialogFooter, AlertDialogCancel, AlertDialogAction } from "@/components/ui/alert-dialog"
import { Skeleton } from "@/components/ui/skeleton"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDes<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/dialog"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { LoadingOverlay } from "@/components/ui/loading-overlay"

interface Project {
  id: string
  name: string
  description: string
  software_type: string
  technology_stack: string
  user_id: number
  created_at: string
  updated_at: string
  is_active: boolean
  requirements_text?: string
  requirements_file_path?: string
}

interface Estimate {
  id: number
  name: string
  project_id: number
  grand_total: number
  total_cost?: number
  currency?: string
  is_active: boolean
  created_at: string
  updated_at?: string
}

export default function ProjectDetailPage() {
  const params = useParams()
  const { locale, id } = params
  const router = useRouter()
  const t = useTranslations()
  const tProjects = useTranslations("projects")
  const tEstimates = useTranslations("estimate")
  const tCommon = useTranslations("common")

  // State for project
  const [project, setProject] = useState<Project | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [deleteConfirm, setDeleteConfirm] = useState(false)
  
  // State for estimates
  const [estimates, setEstimates] = useState<Estimate[]>([])
  const [loadingEstimates, setLoadingEstimates] = useState(true)
  const [editRequirementsOpen, setEditRequirementsOpen] = useState(false)
  const [requirements, setRequirements] = useState("")
  const [savingRequirements, setSavingRequirements] = useState(false)
  const [estimateToDelete, setEstimateToDelete] = useState<number | null>(null)
  const [deleteEstimateConfirm, setDeleteEstimateConfirm] = useState(false)
  const [deletingEstimate, setDeletingEstimate] = useState(false)
  
  // Pagination for estimates
  const [currentPage, setCurrentPage] = useState(1)
  const estimatesPerPage = 6
  
  // State for editing project information and description
  const [editInfoOpen, setEditInfoOpen] = useState(false)
  const [editDescriptionOpen, setEditDescriptionOpen] = useState(false)
  const [projectInfo, setProjectInfo] = useState({
    name: "",
    software_type: "",
    technology_stack: ""
  })
  const [projectDescription, setProjectDescription] = useState("")
  const [savingInfo, setSavingInfo] = useState(false)
  const [savingDescription, setSavingDescription] = useState(false)
  const [generatingAIEstimate, setGeneratingAIEstimate] = useState(false)

  useEffect(() => {
    const token = localStorage.getItem("token")
    if (!token) {
      router.push(`/${locale}/login`)
      return
    }

    const fetchProject = async () => {
      try {
        setLoading(true)
        const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/projects/${id}`, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        })

        if (!response.ok) {
          if (response.status === 401) {
            localStorage.removeItem("token")
            router.push(`/${locale}/login`)
            return
          }
          throw new Error("Project not found")
        }

        const data = await response.json()
        setProject(data)
        setRequirements(data.requirements_text || "")
        
        // Initialize project info and description states
        setProjectInfo({
          name: data.name || "",
          software_type: data.software_type || "",
          technology_stack: data.technology_stack || ""
        })
        setProjectDescription(data.description || "")
      } catch (error) {
        console.error("Error fetching project:", error)
        setError(error instanceof Error ? error.message : "An error occurred")
      } finally {
        setLoading(false)
      }
    }

    const fetchEstimates = async () => {
      try {
        setLoadingEstimates(true)
        const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/projects/${id}/estimates`, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        })

        if (!response.ok) {
          if (response.status === 401) {
            localStorage.removeItem("token")
            router.push(`/${locale}/login`)
            return
          }
          // If no estimates exist yet, this is not an error
          if (response.status === 404) {
            setEstimates([])
            return
          }
          throw new Error("Failed to load estimates")
        }

        const data = await response.json()
        setEstimates(data)
      } catch (error) {
        console.error("Error fetching estimates:", error)
        // Don't set error state for estimates, just log it
      } finally {
        setLoadingEstimates(false)
      }
    }

    fetchProject()
    fetchEstimates()
  }, [id, locale, router])

  const handleDelete = async () => {
    try {
      const token = localStorage.getItem("token")
      if (!token) {
        router.push(`/${locale}/login`)
        return
      }
      
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/projects/${id}`, {
        method: "DELETE",
        headers: {
          Authorization: `Bearer ${token}`,
        }
      })
      
      if (!response.ok) {
        throw new Error("Failed to delete project")
      }
      
      router.push(`/${locale}/projects`)
    } catch (err: any) {
      setError(err.message)
    }
  }

  const handleDeleteEstimate = async () => {
    if (!estimateToDelete) return
    
    try {
      const token = localStorage.getItem("token")
      if (!token) {
        router.push(`/${locale}/login`)
        return
      }

      setDeletingEstimate(true)

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/projects/${id}/estimates/${estimateToDelete}`, {
        method: "DELETE",
        headers: {
          Authorization: `Bearer ${token}`,
        },
      })

      if (!response.ok) {
        throw new Error("Failed to delete estimate")
      }

      // Remove the deleted estimate from the state
      setEstimates(estimates.filter(estimate => estimate.id !== estimateToDelete))
      setEstimateToDelete(null)
      setDeleteEstimateConfirm(false)
    } catch (error) {
      console.error("Error deleting estimate:", error)
      setError(error instanceof Error ? error.message : "An error occurred")
    } finally {
      setDeletingEstimate(false)
    }
  }

  async function saveProjectInfo() {
    try {
      setSavingInfo(true)
      const token = localStorage.getItem("token")
      
      if (!token) {
        router.push(`/${locale}/login`)
        return
      }

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/projects/${id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          name: projectInfo.name,
          software_type: projectInfo.software_type,
          technology_stack: projectInfo.technology_stack,
        }),
      })

      if (!response.ok) {
        if (response.status === 401) {
          localStorage.removeItem("token")
          router.push(`/${locale}/login`)
          return
        }
        throw new Error("Failed to save project information")
      }

      const data = await response.json()
      setProject(data)
      setEditInfoOpen(false)
    } catch (error) {
      console.error("Error saving project information:", error)
      setError(error instanceof Error ? error.message : "An error occurred")
    } finally {
      setSavingInfo(false)
    }
  }
  
  async function saveProjectDescription() {
    try {
      setSavingDescription(true)
      const token = localStorage.getItem("token")
      
      if (!token) {
        router.push(`/${locale}/login`)
        return
      }

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/projects/${id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          description: projectDescription,
        }),
      })

      if (!response.ok) {
        if (response.status === 401) {
          localStorage.removeItem("token")
          router.push(`/${locale}/login`)
          return
        }
        throw new Error("Failed to save project description")
      }

      const data = await response.json()
      setProject(data)
      setEditDescriptionOpen(false)
    } catch (error) {
      console.error("Error saving project description:", error)
      setError(error instanceof Error ? error.message : "An error occurred")
    } finally {
      setSavingDescription(false)
    }
  }
  
  async function saveRequirements() {
    try {
      setSavingRequirements(true)
      const token = localStorage.getItem("token")
      
      if (!token) {
        router.push(`/${locale}/login`)
        return
      }

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/projects/${id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          requirements_text: requirements,
        }),
      })

      if (!response.ok) {
        if (response.status === 401) {
          localStorage.removeItem("token")
          router.push(`/${locale}/login`)
          return
        }
        throw new Error("Failed to save requirements")
      }

      const data = await response.json()
      setProject(data)
      setEditRequirementsOpen(false)
    } catch (error) {
      console.error("Error saving requirements:", error)
      setError(error instanceof Error ? error.message : "An error occurred")
    } finally {
      setSavingRequirements(false)
    }
  }
  
  async function generateAIEstimate() {
    try {
      const token = localStorage.getItem("token")
      if (!token) {
        router.push(`/${locale}/login`)
        return
      }

      setGeneratingAIEstimate(true)

      // Call API to generate estimate from requirements but don't save to database yet
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/projects/${id}/estimates/generate-from-requirements/preview`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ locale })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.detail || 'Failed to generate estimate from requirements')
      }

      const data = await response.json()
      
      // Save data to session storage for the preview page to use
      const previewData = {
        project_id: id,
        project: project,
        development_items: data.estimate.development_items,
        summary_items: data.estimate.summary_items,
        grand_total: data.estimate.grand_total,
        name: data.estimate.name,
        currency: data.estimate.currency
      }
      
      sessionStorage.setItem('estimatePreview', JSON.stringify(previewData))
      
      // Redirect to preview page
      router.push(`/${locale}/projects/${id}/estimate/preview`)
    } catch (error) {
      console.error("Error generating AI estimate:", error)
      setError(error instanceof Error ? error.message : 'An error occurred')
    } finally {
      setGeneratingAIEstimate(false)
    }
  }


  if (error || !project) {
    return (
      <div className="w-full py-8">
        <Link href={`/${locale}/projects`} className="text-blue-500 hover:underline inline-flex items-center">
          <span className="mr-1">←</span> <span className="whitespace-nowrap">{tProjects("backToList")}</span>
        </Link>
      </div>
    )
  }

  const formattedDate = project.updated_at
    ? format(new Date(project.updated_at), 'dd/MM/yyyy HH:mm')
    : ''

  return (
    <div className="w-full py-8">
      {generatingAIEstimate && <LoadingOverlay message={tProjects("generatingAIEstimate")} />}
      <div className="mb-6">
        <div className="flex items-center mb-1">
          <Link href={`/${locale}/projects`} className="text-blue-500 hover:underline inline-flex items-center">
            <span className="mr-1">←</span> <span className="whitespace-nowrap">{tProjects("backToList")}</span>
          </Link>
        </div>
        
        <div className="flex flex-wrap justify-between items-center gap-4">
          <h1 className="text-3xl font-bold text-wrap line-clamp-1 max-w-2xl">{project.name}</h1>
          
          <div className="flex items-center gap-2">
            <Button variant="outline" onClick={() => setEditRequirementsOpen(true)}>
              {tProjects("editRequirements")}
            </Button>
            <Button variant="destructive" onClick={() => setDeleteConfirm(true)}>
              <Trash2Icon className="mr-2 h-4 w-4" />
              {tProjects("deleteProject")}
            </Button>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle>{tProjects("projectInfo")}</CardTitle>
            <Button variant="ghost" size="sm" onClick={() => setEditInfoOpen(true)}>
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2"><path d="M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z"></path></svg>
              {t('common.edit')}
            </Button>
          </CardHeader>
          <CardContent>
            <dl className="space-y-4">
              <div>
                <dt className="text-sm font-medium text-gray-500">
                  {tProjects("softwareType")}
                </dt>
                <dd className="mt-1">{project.software_type || "-"}</dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500">
                  {tProjects("techStack")}
                </dt>
                <dd className="mt-1">{project.technology_stack || "-"}</dd>
              </div>
              <div className="flex justify-between">
                <div>
                  <dt className="text-sm font-medium text-gray-500">
                    {tProjects("createdAt")}
                  </dt>
                  <dd className="mt-1 flex items-center">
                    <CalendarIcon className="mr-1 h-4 w-4" />
                    {format(new Date(project.created_at), 'dd/MM/yyyy HH:mm')}
                  </dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-gray-500">
                    {tProjects("lastUpdated")}
                  </dt>
                  <dd className="mt-1 flex items-center">
                    <CalendarIcon className="mr-1 h-4 w-4" />
                    {formattedDate}
                  </dd>
                </div>
              </div>
            </dl>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle>{tProjects("projectDescription")}</CardTitle>
            <Button variant="ghost" size="sm" onClick={() => setEditDescriptionOpen(true)}>
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2"><path d="M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z"></path></svg>
              {t('common.edit')}
            </Button>
          </CardHeader>
          <CardContent>
            <p className="whitespace-pre-line max-h-[150px] overflow-y-auto">
              {project.description || tProjects("noDescription")}
            </p>
          </CardContent>
        </Card>
      </div>

      <Card className="mb-6">
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>{tProjects("projectRequirements")}</CardTitle>
          <div className="flex space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={generateAIEstimate}
              disabled={!project?.requirements_text}
            >
              <span className="mr-2 h-4 w-4 animate-spin">⏳</span>
              {tProjects("aiEstimate")}
            </Button>
            <Button variant="ghost" size="sm" onClick={() => setEditRequirementsOpen(true)}>
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2"><path d="M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z"></path></svg>
              {t('common.edit')}
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="whitespace-pre-line max-h-[300px] overflow-y-auto pr-2">
            {project.requirements_text ? (
              <p>{project.requirements_text}</p>
            ) : project.requirements_file_path ? (
              <p>{tProjects('fileRequirement')}: {project.requirements_file_path.split('/').pop()}</p>
            ) : (
              <p className="text-gray-500 italic">{tProjects("noRequirements")}</p>
            )}
          </div>
        </CardContent>
      </Card>

      <Card className="mb-6">
        <CardHeader>
          <CardTitle>{tProjects("projectEstimation")}</CardTitle>
        </CardHeader>
        <CardContent>
          {loadingEstimates ? (
            <div className="py-4">
              <Skeleton className="h-4 w-3/4 mx-auto mb-2" />
              <Skeleton className="h-4 w-1/2 mx-auto" />
            </div>
          ) : estimates.length === 0 ? (
            <div className="text-center py-4">
              <p className="mb-4 text-gray-600">
                {tProjects("notYetEstimated")}
              </p>
              <div className="flex flex-col sm:flex-row gap-3 justify-center">
                <Button
                  onClick={generateAIEstimate}
                  disabled={generatingAIEstimate || !project?.requirements_text}
                >
                  <span className="mr-2 h-4 w-4 animate-spin">⏳</span>
                  {tProjects("aiEstimate")}
                </Button>
              </div>
            </div>
          ) : (
            <div className="py-4">
              <div className="mt-6">
                
                {/* Pagination calculation */}
                {(() => {
                  const indexOfLastEstimate = currentPage * estimatesPerPage;
                  const indexOfFirstEstimate = indexOfLastEstimate - estimatesPerPage;
                  const currentEstimates = estimates.slice(indexOfFirstEstimate, indexOfLastEstimate);
                  const totalPages = Math.ceil(estimates.length / estimatesPerPage);
                  
                  return (
                    <>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {currentEstimates.map((estimate) => (
                          <div key={estimate.id} className="border rounded-lg p-3 bg-white shadow-sm">
                            {/* Row 1: Estimate name and date */}
                            <div className="flex justify-between items-center mb-2">
                              <h4 className="font-medium text-sm">{estimate.name}</h4>
                              <p className="text-xs text-gray-500">{format(new Date(estimate.created_at), "PPP")}</p>
                            </div>
                            
                            {/* Row 2: Estimate details and actions in one line */}
                            <div className="flex justify-between items-center">
                              <div className="flex space-x-3 text-xs">
                                <span>
                                  <span className="font-medium">{tEstimates("totalEffort")}:</span> {estimate.grand_total.toLocaleString()} {tEstimates("manDays")}
                                </span>
                                {estimate.total_cost && (
                                  <span>
                                    <span className="font-medium">{tEstimates("costTotal")}:</span> {estimate.total_cost.toLocaleString()} {estimate.currency || "USD"}
                                  </span>
                                )}
                              </div>
                              <div className="flex space-x-2">
                                <Button
                                  variant="destructive"
                                  size="sm"
                                  className="h-6 w-6 p-0"
                                  onClick={() => {
                                    setEstimateToDelete(estimate.id)
                                    setDeleteEstimateConfirm(true)
                                  }}
                                >
                                  <Trash2Icon className="h-3 w-3" />
                                </Button>
                                <Link href={`/${locale}/projects/${id}/estimate/${estimate.id}`}>
                                  <Button variant="outline" size="sm" className="h-6 px-2 text-xs">
                                    {tEstimates("details")}
                                  </Button>
                                </Link>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                      
                      {/* Pagination controls */}
                      {totalPages > 1 && (
                        <div className="flex justify-center items-center space-x-2 mt-6">
                          <Button 
                            variant="outline" 
                            size="sm" 
                            onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                            disabled={currentPage === 1}
                          >
                            {t("common.previous")}
                          </Button>
                          
                          <div className="text-sm text-gray-500">
                            {`${tProjects("showing")} ${indexOfFirstEstimate + 1}-${Math.min(indexOfLastEstimate, estimates.length)} ${tProjects("of")} ${estimates.length} ${tEstimates("estimates")}`}
                          </div>
                          
                          <Button 
                            variant="outline" 
                            size="sm" 
                            onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                            disabled={currentPage === totalPages}
                          >
                            {t("common.next")}
                          </Button>
                        </div>
                      )}
                    </>
                  );
                })()}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {deleteConfirm && (
        <AlertDialog open={deleteConfirm} onOpenChange={setDeleteConfirm}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>{tProjects("titleConfirmDelete")}</AlertDialogTitle>
              <AlertDialogDescription>
                <p>{tProjects("confirmDelete")}</p>
               <span className="text-wrap line-clamp-1 max-w-lg">
                 {tProjects("confirmProjectDelete",
                   {project_name: project?.name?.length > 30
                   ? project.name.slice(0, 30) + "..."
                   : project?.name})
                 }</span>
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel onClick={() => setDeleteConfirm(false)}>
                {tCommon("cancel")}
              </AlertDialogCancel>
              <AlertDialogAction onClick={handleDelete}>
                {tCommon("delete")}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      )}

      <Dialog open={editRequirementsOpen} onOpenChange={setEditRequirementsOpen}>
        <DialogContent className="sm:max-w-[800px]">
          <DialogHeader>
            <DialogTitle>{tProjects("projectRequirements")}</DialogTitle>
            <DialogDescription>
              {tProjects("requirementsPlaceholder")}
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <Textarea
              rows={15}
              value={requirements}
              onChange={(e) => setRequirements(e.target.value)}
              placeholder={tProjects("requirementsPlaceholder")}
            />
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setEditRequirementsOpen(false)}>
              {t('common.cancel')}
            </Button>
            <Button onClick={saveRequirements} disabled={savingRequirements}>
              {savingRequirements && (
                <span className="mr-2 h-4 w-4 animate-spin">⏳</span>
              )}
              {t('common.save')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Edit Project Information Dialog */}
      <Dialog open={editInfoOpen} onOpenChange={setEditInfoOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>{tProjects("projectInfo")}</DialogTitle>
            <DialogDescription>
              {t('common.edit')} {tProjects("projectInfo")}
            </DialogDescription>
          </DialogHeader>
          <div className="py-4 space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name">{tProjects("projectName")}</Label>
              <Input
                id="name"
                value={projectInfo.name}
                onChange={(e) => setProjectInfo({...projectInfo, name: e.target.value})}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="software_type">{tProjects("softwareType")}</Label>
              <Input
                id="software_type"
                value={projectInfo.software_type}
                onChange={(e) => setProjectInfo({...projectInfo, software_type: e.target.value})}
                placeholder="Web App, Mobile App, Desktop App"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="technology_stack">{tProjects("techStack")}</Label>
              <Input
                id="technology_stack"
                value={projectInfo.technology_stack}
                onChange={(e) => setProjectInfo({...projectInfo, technology_stack: e.target.value})}
                placeholder="React, Node.js, PostgreSQL"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setEditInfoOpen(false)}>
              {t('common.cancel')}
            </Button>
            <Button onClick={saveProjectInfo} disabled={savingInfo}>
              {savingInfo && (
                <span className="mr-2 h-4 w-4 animate-spin">⏳</span>
              )}
              {t('common.save')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Edit Project Description Dialog */}
      <Dialog open={editDescriptionOpen} onOpenChange={setEditDescriptionOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>{tProjects("projectDescription")}</DialogTitle>
            <DialogDescription>
              {t('common.edit')} {tProjects("projectDescription")}
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <Textarea
              rows={10}
              value={projectDescription}
              onChange={(e) => setProjectDescription(e.target.value)}
              placeholder={tProjects("noDescription")}
            />
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setEditDescriptionOpen(false)}>
              {t('common.cancel')}
            </Button>
            <Button onClick={saveProjectDescription} disabled={savingDescription}>
              {savingDescription && (
                <span className="mr-2 h-4 w-4 animate-spin">⏳</span>
              )}
              {t('common.save')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Estimate Confirmation Dialog */}
      <AlertDialog open={deleteEstimateConfirm} onOpenChange={setDeleteEstimateConfirm}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{tProjects("confirmDelete")}</AlertDialogTitle>
            <AlertDialogDescription>
              {tEstimates("deleteEstimateConfirm")}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setDeleteEstimateConfirm(false)}>
              {t("common.cancel")}
            </AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteEstimate} disabled={deletingEstimate}>
              {deletingEstimate ? (
                <span className="mr-2 h-4 w-4 animate-spin">⏳</span>
              ) : null}
              {t("common.delete")}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}