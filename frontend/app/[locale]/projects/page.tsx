"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { usePathname } from "next/navigation"
import PaginationControls from "@/components/PaginationControls"
import withAuth from "@/components/auth/with-auth"
import { useTranslations } from 'next-intl'
import { getLocaleFromPathname, setStoredLocale } from '@/utils/localeStorage'
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
// Import icons
import { PlusCircle, Info, Settings, AlertCircle } from 'lucide-react'

interface Project {
  id: number
  name: string
  description: string
  software_type: string
  technology_stack: string
  created_at: string
  estimate_min: number | null
  estimate_max: number | null
}

interface PaginationData {
  items: Project[]
  total: number
  page: number
  pages: number
  size: number
}

function ProjectsPage() {
  const t = useTranslations()
  const [paginationData, setPaginationData] = useState<PaginationData>({
    items: [],
    total: 0,
    page: 1,
    pages: 1,
    size: 10
  })
  const [loading, setLoading] = useState<boolean>(true)
  const [error, setError] = useState<string>("") 
  const [searchTerm, setSearchTerm] = useState<string>("") 
  const [userSettings, setUserSettings] = useState<{currency?: string, role_rates?: Record<string, number>} | null>(null)
  const [settingsComplete, setSettingsComplete] = useState<boolean>(false)
  const [showSettingsGuide, setShowSettingsGuide] = useState<boolean>(false)
  const [hasProjects, setHasProjects] = useState<boolean>(false)
  
  // Hàm lấy ký hiệu tiền tệ dựa trên cài đặt người dùng
  const getCurrencySymbol = () => {
    if (!userSettings || !userSettings.currency) return '$' // Mặc định là USD
    
    switch (userSettings.currency) {
      case 'JPY':
        return '¥'
      case 'VND':
        return '₫'
      case 'USD':
      default:
        return '$'
    }
  }
  
  // Check if user has set currency and role rates
  const checkSettingsComplete = () => {
    // If userSettings is not null, check for required fields
    if (userSettings) {
      // Check if currency is set
      const hasCurrency = !!userSettings.currency;
      
      // Check if at least one main role has a rate
      const hasRoleRates = !!userSettings.role_rates;
      const mainRoles = ['Developer', 'Designer', 'PM'];
      const hasMainRoleRates = hasRoleRates && mainRoles.some(role => 
        userSettings.role_rates?.[role] && Number(userSettings.role_rates[role]) > 0
      );
      
      // If user has set currency and at least one main role rate, mark as complete
      const isComplete = hasCurrency && hasMainRoleRates;
      if (isComplete && typeof window !== "undefined") {
        localStorage.setItem("settingsCompleted", "true");
      }
      
      return isComplete;
    }
    
    // If no userSettings from API, check localStorage
    if (typeof window !== "undefined") {
      const settingsCompleted = localStorage.getItem("settingsCompleted");
      return settingsCompleted === "true";
    }
    
    // Default to false if no userSettings or localStorage
    return false;
  }
  
  // Update settings status
  const updateSettingsStatus = () => {
    // Check localStorage first
    let isComplete = false;
    if (typeof window !== "undefined") {
      const settingsCompleted = localStorage.getItem("settingsCompleted");
      if (settingsCompleted === "true") {
        isComplete = true;
      } else {
        // If not in localStorage, check using checkSettingsComplete
        isComplete = checkSettingsComplete();
      }
    } else {
      isComplete = checkSettingsComplete();
    }
    
    setSettingsComplete(isComplete);
    
    // Only show guide if no projects and settings not complete
    setShowSettingsGuide(!isComplete);
  }
  
  const pathname = usePathname()
  
  // Get current locale from pathname
  const currentLocale = getLocaleFromPathname(pathname);

  const fetchProjects = async (page: number = 1) => {
    try {
      setLoading(true)
      const token = typeof window !== "undefined" ? localStorage.getItem("token") : null
      
      if (!token) {
        window.location.href = `/${currentLocale}/login`;
        return
      }
      
      const limit = 10
      const skip = (page - 1) * limit
      
      let endpoint = `${process.env.NEXT_PUBLIC_API_URL}/projects?skip=${skip}&limit=${limit}`
      
      // Use search endpoint if search term is provided
      if (searchTerm && searchTerm.trim() !== "") {
        endpoint = `${process.env.NEXT_PUBLIC_API_URL}/projects/search/${encodeURIComponent(searchTerm.trim())}?skip=${skip}&limit=${limit}`
      }
      
      const response = await fetch(endpoint, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      })
      
      if (!response.ok) {
        if (response.status === 401) {
          localStorage.removeItem("token")
          window.location.href = `/${currentLocale}/login`;
          return
        }
        throw new Error("Failed to fetch projects")
      }
      
      const data = await response.json()
      setPaginationData(data)
      
      // Cập nhật trạng thái có dự án hay không
      setHasProjects(data.items.length > 0)
    } catch (err: any) {
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  // Check if user has set currency and role rates
  const fetchUserSettings = async () => {
    try {
      const token = typeof window !== "undefined" ? localStorage.getItem("token") : null
      
      if (!token) return
      
      // Check if settings are already completed in localStorage
      if (typeof window !== "undefined" && localStorage.getItem("settingsCompleted") === "true") {
        setSettingsComplete(true);
        setShowSettingsGuide(false);
      }
      
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/users/settings`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      })
      
      if (!response.ok) {
        // If no data from API, use localStorage status
        updateSettingsStatus();
        return;
      }
      
      const data = await response.json()
      setUserSettings(data)
      
      // Update settings status
      updateSettingsStatus();
    } catch (err) {
      console.error("Error fetching user settings:", err)
      // Update settings status based on localStorage if error
      updateSettingsStatus();
    }
  }
  
  useEffect(() => {
    // Save locale to localStorage
    setStoredLocale(currentLocale);
    fetchProjects(1)
    fetchUserSettings()
  }, [currentLocale])
  
  // Update settings status when data changes
  useEffect(() => {
    updateSettingsStatus();
  }, [userSettings, hasProjects])

  const handlePageChange = (page: number) => {
    fetchProjects(page)
  }

  const handleCreateProject = () => {
    window.location.href = `/${currentLocale}/projects/new`;
  }
  
  const handleGoToSettings = () => {
    window.location.href = `/${currentLocale}/settings`;
  }

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    fetchProjects(1) // Reset to first page when searching
  }

  const handleSearchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value)
  }

  const handleClearSearch = () => {
    setSearchTerm('')
    fetchProjects(1) // Reset to first page and fetch all projects
  }

  // Handle project info navigation
  const handleProjectInfo = (projectId: number) => {
    window.location.href = `/${currentLocale}/projects/${projectId}`;
  };

  if (loading && paginationData.items.length === 0) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-gray-900 mx-auto"></div>
          <p className="mt-4">{t('common.loading')}</p>
        </div>
      </div>
    )
  }

  return (
    <div className="flex flex-col min-h-screen bg-white">
      <main className="flex-1 p-6">
        <div className="max-w-6xl mx-auto">
          {/* Show setup guide for new users */}
          {showSettingsGuide && (
            <Alert className="mb-6 bg-blue-50 border-blue-200">
              <AlertCircle className="h-5 w-5 text-blue-600" />
              <AlertTitle className="text-blue-800 font-medium">
                {t('settings.setupGuide.title') || 'Complete your settings'}
              </AlertTitle>
              <AlertDescription className="text-blue-700">
                <p className="mb-2">
                  {t('settings.setupGuide.description') || 'Please set up your currency and role rates before creating projects. This will ensure accurate cost calculations.'}
                </p>
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="mt-2 bg-white hover:bg-blue-50 border-blue-200 text-blue-700"
                  onClick={handleGoToSettings}
                >
                  <Settings className="mr-2 h-4 w-4" />
                  {t('settings.setupGuide.goToSettings') || 'Go to Settings'}
                </Button>
              </AlertDescription>
            </Alert>
          )}
          
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
            <h2 className="text-2xl font-bold">{t('projects.title')}</h2>
            <div className="flex flex-col sm:flex-row items-center gap-3 w-full sm:w-auto">
              <form onSubmit={handleSearch} className="relative flex items-center w-full sm:w-[500px]" data-hs-ignore="true">
                <input 
                  type="search" 
                  value={searchTerm} 
                  onChange={handleSearchInputChange} 
                  placeholder={t('projects.searchPlaceholder')} 
                  className="py-2 pl-9 pr-8 text-sm text-gray-700 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-600 w-full"
                  style={{ width: '100%' }}
                />
              <span className="absolute left-2">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-gray-500" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd" />
                </svg>
              </span>
              </form>
              <Button onClick={handleCreateProject} className="w-full md:w-auto mr-4 min-w-[170px] justify-normal" disabled={!settingsComplete}>
                <PlusCircle className="w-4 h-4 mr-2" />
                {t('projects.createNew')}
              </Button>
            </div>
          </div>
          
          {error && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
              {error}
            </div>
          )}
          
          {loading && paginationData.items.length > 0 && (
            <div className="flex justify-center my-4">
              <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-gray-900"></div>
            </div>
          )}
          
          {paginationData.items.length === 0 ? (
            <div className="text-center mt-12 max-w-lg mx-auto">
              <div className="p-6 bg-white border rounded-lg shadow-sm">
                <h3 className="text-lg font-medium text-gray-900 mb-2">{t('projects.noProjects')}</h3>
                
                {!settingsComplete && (
                  <div className="mb-6">
                    <div className="bg-amber-50 border border-amber-200 rounded-md p-4 mb-4">
                      <div className="flex">
                        <div className="flex-shrink-0">
                          <AlertCircle className="h-5 w-5 text-amber-400" />
                        </div>
                        <div className="ml-3">
                          <h3 className="text-sm font-medium text-amber-800">{t('projects.setupNeeded') || 'Setup Needed'}</h3>
                          <div className="mt-2 text-sm text-amber-700">
                            <p>{t('projects.settingsRequired') || 'Please set up your currency and role rates in the settings before creating projects.'}</p>
                          </div>
                          <div className="mt-4">
                            <Button 
                              onClick={handleGoToSettings}
                              variant="outline"
                              className="text-amber-800 bg-amber-50 border-amber-300 hover:bg-amber-100"
                            >
                              <Settings className="w-4 h-4 mr-2" />
                              {t('projects.goToSettings') || 'Go to Settings'}
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
                
                <Button 
                  onClick={handleCreateProject} 
                  className="w-full" 
                  disabled={!settingsComplete}
                >
                  <PlusCircle className="w-4 h-4 mr-2" />
                  {t('projects.createFirstProject') || 'Create your first project'}
                </Button>
              </div>
            </div>
          ) : (
            <>
              {paginationData.items.map((project: Project) => (
                <div key={project.id} className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 border rounded-lg p-4 mb-3 bg-white shadow-sm">
                  <div className="flex-1 min-w-0">
                    <span className="font-semibold text-lg text-wrap line-clamp-1 max-w-lg">{project.name}</span>
                    <div className="text-gray-500 text-sm text-wrap line-clamp-1 max-w-md">{project.description}</div>
                  </div>
                  {project.estimate_min !== null && project.estimate_max !== null && (
                    <div className="flex justify-center items-center flex-shrink-0 my-2 sm:my-0">
                      <span className="inline-block bg-gray-100 text-gray-700 px-3 py-1 rounded text-sm font-medium">
                        {project.estimate_min === project.estimate_max
                          ? `${t('estimate.cost')}: ${getCurrencySymbol()}${project.estimate_min.toLocaleString()}`
                          : `${t('estimate.cost')}: ${getCurrencySymbol()}${project.estimate_min.toLocaleString()} - ${getCurrencySymbol()}${project.estimate_max.toLocaleString()}`}
                      </span>
                    </div>
                  )}
                  <div className="flex gap-2 flex-shrink-0">
                    <Button onClick={() => handleProjectInfo(project.id)} className="min-w-[170px]">
                      <Info className="w-4 h-4 mr-2" />
                      {t('projects.projectInfo')}
                    </Button>
                  </div>
                </div>
              ))}
            </>
          )}
          
          <div className="mt-4">
            <PaginationControls 
              currentPage={paginationData.page} 
              totalPages={paginationData.pages} 
              onPageChange={handlePageChange} 
            />
          </div>
          
          <div className="text-sm text-gray-500 mt-2 text-center">
            <p>
              {(paginationData.page - 1) * paginationData.size + 1}
              -
              {Math.min(
                  paginationData.page * paginationData.size,
                  paginationData.total
              )} ／{t('projects.totalProjects')}：{paginationData.total} {t('projects.projects')}
            </p>
          </div>
        </div>
      </main>
      
      {/* Footer */}
      <footer className="p-4 text-sm text-gray-500 flex justify-center">
        <p>© 2024 {t('app.title')}</p>
      </footer>
    </div>
  )
}

export default withAuth(ProjectsPage)
