import '../globals.css'
import { notFound } from 'next/navigation'
import { locales } from '@/i18n/settings'
import { cn } from '@/lib/utils'
import { inter } from '@/lib/fonts'
import { NextIntlClientProvider, useTranslations } from 'next-intl'
import Header from '@/components/layout/header'
import { Toaster } from "@/components/ui/toaster"

export function generateStaticParams() {
  return locales.map((locale) => ({ locale }))
}

export default async function RootLayout({
  children,
  params,
}: {
  children: React.ReactNode
  params: { locale: string }
}) {
  const { locale } = params

  if (!locale || !locales.includes(locale as any)) {
    notFound()
  }

  let messages;
  try {
    messages = (await import(`../../messages/${locale}/index.json`)).default;
  } catch (error) {
    notFound();
  }

  return (
    <html lang={locale} suppressHydrationWarning>
      <body className={cn("min-h-screen bg-background font-sans antialiased overflow-x-hidden", inter.className)} suppressHydrationWarning>
        <NextIntlClientProvider locale={locale} messages={messages}>
          <div className="relative flex min-h-screen flex-col">
            <Header />
            <main className="flex-1 py-6 w-full">
              <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                {children}
              </div>
            </main>
            <Toaster />
          </div>
        </NextIntlClientProvider>
        {/* Start of HubSpot Chat Only Embed Code */}
        {/* HubSpot Chat Widget */}
        <script
          id="hs-script-loader"
          type="text/javascript"
          src="//js-na1.hs-scripts.com/40175348.js"
          async
          defer
        />
        <script
          dangerouslySetInnerHTML={{
            __html: `
                window.hsConversationsSettings = {
                  loadImmediately: true,
                  disableAttachment: false,
                  enableWidgetCookieBanner: false,
                  disableInitialMessageFetch: false
                };
                
                // Initialize the chat widget
                if (window.hbspt) {
                  window.hbspt.conversations.widget.load();
                } else {
                  window.hsConversationsOnReady = [function() {
                    window.hbspt.conversations.widget.load();
                  }];
                }
              `,
          }}
        />
      </body>
    </html>
  )
}