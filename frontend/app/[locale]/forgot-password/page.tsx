"use client"

import React from 'react'
import { useState } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { useTranslations } from 'next-intl'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { BarChart3, ArrowLeft, Mail, CheckCircle } from 'lucide-react'
import { useParams } from 'next/navigation'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { withAuthRedirect } from '@/components/auth/withAuthRedirect';

function ForgotPasswordPage() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState(false)
  const [email, setEmail] = useState('')
  const [emailError, setEmailError] = useState('')
  const router = useRouter()
  const t = useTranslations()
  const params = useParams()
  const locale = params.locale as string

  // Email validation function
  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  // Handle email input change
  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const emailValue = e.target.value
    setEmail(emailValue)
    
    // Clear previous errors
    setEmailError('')
    setError('')
    
    // Validate email if not empty
    if (emailValue && !validateEmail(emailValue)) {
      setEmailError(t('auth.invalidEmail'))
    }
  }

  async function handleSubmit(event: React.FormEvent<HTMLFormElement>) {
    event.preventDefault()
    setLoading(true)
    setError('')
    setEmailError('')
    setSuccess(false)

    const formData = new FormData(event.currentTarget)
    const emailValue = formData.get('email') as string
    setEmail(emailValue)

    // Validate email format
    if (!validateEmail(emailValue)) {
      setEmailError(t('auth.invalidEmail'))
      setLoading(false)
      return
    }

    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/auth/reset-password-request`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email: emailValue, locale }),
      })

      const data = await response.json()

      if (!response.ok) {
        // Handle different types of errors
        if (data.detail) {
          // If detail is a string, use it directly
          if (typeof data.detail === 'string') {
            throw new Error(data.detail)
          }
          // If detail is an array (validation errors)
          else if (Array.isArray(data.detail)) {
            const errorMessages = data.detail.map((err: any) => {
              if (typeof err === 'string') return err
              if (err.msg) return err.msg
              if (err.message) return err.message
              return JSON.stringify(err)
            }).join(', ')
            throw new Error(errorMessages)
          }
          // If detail is an object
          else if (typeof data.detail === 'object') {
            throw new Error(JSON.stringify(data.detail))
          }
        }
        
        // Fallback error message
        throw new Error(t('auth.resetPasswordFailed'))
      }

      setSuccess(true)
    } catch (err: any) {
      console.error("Reset password error:", err)
      // Ensure we always display a string, not an object
      const errorMessage = err.message || err.toString() || t('auth.resetPasswordFailed')
      setError(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen flex flex-col md:flex-row">
      {/* Left side - Branding & Info */}
      <div className="w-full md:w-1/2 bg-gradient-to-br from-blue-600 to-indigo-700 text-white p-8 flex flex-col">
        <div className="flex items-center mb-12">
          <div className="w-10 h-10 bg-white rounded-md flex items-center justify-center mr-3">
            <BarChart3 className="text-blue-600" />
          </div>
          <Link href={`/${locale}`} className="text-xl font-bold">Hapoest</Link>
        </div>
        
        <div className="flex-grow flex flex-col justify-center max-w-md mx-auto">
          <h1 className="text-3xl md:text-4xl font-bold mb-6">
            {t('auth.forgotPasswordTitle')}
          </h1>
          <p className="text-xs md:text-sm mb-8 text-blue-100 whitespace-pre-line">
            {t('auth.forgotPasswordDescription')}
          </p>
          <div className="hidden md:block">
            <div className="flex justify-center items-center mb-6">
              <Mail className="w-24 h-24 text-blue-200" />
            </div>
            <div className="flex justify-center gap-4">
              <div className="w-24 h-24 bg-blue-500/30 rounded-lg flex items-center justify-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="40"
                  height="40"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="1.5"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="text-blue-100"
                >
                  <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z" />
                  <polyline points="22,6 12,13 2,6" />
                </svg>
              </div>
              <div className="w-24 h-24 bg-blue-500/30 rounded-lg flex items-center justify-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="40"
                  height="40"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="1.5"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="text-blue-100"
                >
                  <path d="M9 12l2 2 4-4" />
                  <path d="M21 12c.552 0 1-.448 1-1s-.448-1-1-1-1 .448-1 1 .448 1 1 1z" />
                  <path d="M3 12c.552 0 1-.448 1-1s-.448-1-1-1-1 .448-1 1 .448 1 1 1z" />
                  <path d="M12 21c.552 0 1-.448 1-1s-.448-1-1-1-1 .448-1 1 .448 1 1 1z" />
                  <path d="M12 3c.552 0 1-.448 1-1s-.448-1-1-1-1 .448-1 1 .448 1 1 1z" />
                </svg>
              </div>
              <div className="w-24 h-24 bg-blue-500/30 rounded-lg flex items-center justify-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="40"
                  height="40"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="1.5"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="text-blue-100"
                >
                  <path d="M9 12l2 2 4-4" />
                  <circle cx="12" cy="12" r="10" />
                </svg>
              </div>
            </div>
          </div>
        </div>
        
        <div className="mt-auto text-sm text-blue-200">
          © 2025 Hapoest - {t('footer.allRightsReserved')}
        </div>
      </div>
      
      {/* Right side - Forgot Password Form */}
      <div className="w-full md:w-1/2 flex flex-col items-center justify-center p-8 bg-white">
        <div className="w-full max-w-md space-y-6">
          <div className="flex flex-col space-y-2 text-center">
            <h1 className="text-2xl font-semibold tracking-tight">
              {t("auth.forgotPasswordTitle")}
            </h1>
            <p className="text-xs md:text-sm text-muted-foreground whitespace-pre-line" >
              {t("auth.forgotPasswordSubtitle")}
            </p>
          </div>
          
          {!success ? (
            <form onSubmit={handleSubmit} className="space-y-4" data-hs-ignore="true">
              <div className="space-y-2">
                <Label htmlFor="email">{t("auth.email")}</Label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={email}
                  onChange={handleEmailChange}
                  required
                  className={`w-full ${emailError ? 'border-red-500 focus:border-red-500' : ''}`}
                />
                {emailError && (
                  <p className="text-xs text-red-600">{emailError}</p>
                )}
              </div>
              
              {error && (
                <Alert className="bg-red-50 border-red-200">
                  <AlertDescription className="text-red-700">
                    {error}
                  </AlertDescription>
                </Alert>
              )}
              
              <Button 
                type="submit" 
                className="w-full bg-blue-600 hover:bg-blue-700" 
                disabled={loading || !!emailError}
              >
                {loading ? t("common.loading") : t("auth.sendResetLink")}
              </Button>
            </form>
          ) : (
            <div className="space-y-4">
              <Alert className="bg-green-50 border-green-200">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <AlertDescription className="text-green-700">
                  {t("auth.resetLinkSent", { email })}
                </AlertDescription>
              </Alert>
              
              <div className="text-center text-sm text-muted-foreground">
                {t("auth.didntReceiveEmail")}{" "}
                <button 
                  onClick={() => {
                    setSuccess(false)
                    setError('')
                  }}
                  className="text-blue-600 hover:text-blue-800 font-medium"
                >
                  {t("auth.tryAgain")}
                </button>
              </div>
            </div>
          )}
          
          <div className="text-center">
            <Link 
              href={`/${locale}/login`} 
              className="text-sm text-blue-600 hover:text-blue-800 font-medium flex items-center justify-center gap-1"
            >
              <ArrowLeft className="w-4 h-4" />
              {t("auth.backToLogin")}
            </Link>
          </div>
          
          <div className="text-center text-sm">
            {t("auth.noAccount")}{" "}
            <Link 
              href={`/${locale}/register`} 
              className="text-blue-600 hover:text-blue-800 font-medium"
            >
              {t("auth.register")}
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}

export default withAuthRedirect(ForgotPasswordPage);
