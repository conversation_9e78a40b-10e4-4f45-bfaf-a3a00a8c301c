"use client"

import React, { Suspense } from 'react'
import { useState, useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import Link from 'next/link'
import { useTranslations } from 'next-intl'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { BarChart3, ArrowLeft, Lock, CheckCircle, AlertCircle } from 'lucide-react'
import { useParams } from 'next/navigation'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { withAuthRedirect } from '@/components/auth/withAuthRedirect'

// Component to handle search params (needs to be wrapped in Suspense)
function ResetPasswordForm() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState(false)
  const [token, setToken] = useState('')
  const [password, setPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [passwordMatch, setPasswordMatch] = useState(true)
  const router = useRouter()
  const searchParams = useSearchParams()
  const t = useTranslations()
  const params = useParams()
  const locale = params.locale as string

  // Only check token on mount or when searchParams change (not t)
  useEffect(() => {
    const resetToken = searchParams.get('token')
    if (resetToken) {
      setToken(resetToken)
      setError("")
    } else {
      setError('invalidResetToken')
    }
  }, [searchParams])

  // Update error translation when locale changes and error is invalidResetToken
  useEffect(() => {
    if (error === 'invalidResetToken') {
      setError(t('auth.invalidResetToken'))
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [t])

  const validatePasswords = (newPassword: string, newConfirmPassword: string) => {
    const match = newPassword === newConfirmPassword
    setPasswordMatch(match)
    return match
  }

  // Always keep passwordMatch in sync with both fields
  useEffect(() => {
    setPasswordMatch(password === confirmPassword)
  }, [password, confirmPassword])

  async function handleSubmit(event: React.FormEvent<HTMLFormElement>) {
    event.preventDefault()
    
    if (!validatePasswords(password, confirmPassword)) {
      setError(t('auth.passwordsDoNotMatch'))
      return
    }

    if (password.length < 8) {
      setError(t('auth.passwordRules.length'))
      return
    }

    setLoading(true)
    setError('')
    setSuccess(false)

    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/auth/reset-password`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          token,
          new_password: password 
        }),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(t('auth.resetPasswordFailed'))
      }

      setSuccess(true)
      // Redirect to login after 3 seconds
      setTimeout(() => {
        router.push(`/${locale}/login`)
      }, 3000)
    } catch (err: any) {
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="w-full max-w-sm space-y-6">
      <div className="flex flex-col space-y-2 text-center">
        <h1 className="text-2xl font-semibold tracking-tight">
          {t("auth.resetPasswordTitle")}
        </h1>
        <p className="text-sm text-muted-foreground">
          {t("auth.resetPasswordSubtitle")}
        </p>
      </div>
      
      {!success ? (
        <form onSubmit={handleSubmit} className="space-y-4" data-hs-ignore="true">
          <div className="space-y-2">
            <Label htmlFor="password">{t("auth.newPassword")}</Label>
            <Input
              id="password"
              name="password"
              type="password"
              value={password}
              onChange={(e) => {
                setPassword(e.target.value)
                if (confirmPassword) {
                  validatePasswords(e.target.value, confirmPassword)
                }
              }}
              required
              className="w-full"
              minLength={8}
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="confirmPassword">{t("auth.confirmNewPassword")}</Label>
            <Input
              id="confirmPassword"
              name="confirmPassword"
              type="password"
              value={confirmPassword}
              onChange={(e) => {
                setConfirmPassword(e.target.value)
                validatePasswords(password, e.target.value)
              }}
              required
              className={`w-full ${!passwordMatch ? 'border-red-300' : ''}`}
              minLength={8}
            />
            {!passwordMatch && confirmPassword && (
              <p className="text-sm text-red-500">{t('auth.passwordsDoNotMatch')}</p>
            )}
            {passwordMatch && confirmPassword && password && (
              <p className="text-sm text-green-500">{t('auth.passwordsMatch')}</p>
            )}
          </div>
          
          {error && (
            <Alert className="bg-red-50 border-red-200">
              <AlertCircle className="h-4 w-4 text-red-600" />
              <AlertDescription className="text-red-700">
                {error}
              </AlertDescription>
            </Alert>
          )}
          
          <Button 
            type="submit" 
            className="w-full bg-blue-600 hover:bg-blue-700" 
            disabled={loading || !token || !passwordMatch}
          >
            {loading ? t("common.loading") : t("auth.resetPassword")}
          </Button>
        </form>
      ) : (
        <div className="space-y-4">
          <Alert className="bg-green-50 border-green-200">
            <CheckCircle className="h-4 w-4 text-green-600" />
            <AlertDescription className="text-green-700">
              {t("auth.resetPasswordSuccess")}
            </AlertDescription>
          </Alert>
          
          <div className="text-center text-sm text-muted-foreground">
            {t("auth.redirectingToLogin")}
          </div>
        </div>
      )}
      
      <div className="text-center">
        <Link 
          href={`/${useParams().locale}/login`} 
          className="text-sm text-blue-600 hover:text-blue-800 font-medium flex items-center justify-center gap-1"
        >
          <ArrowLeft className="w-4 h-4" />
          {t("auth.backToLogin")}
        </Link>
      </div>
    </div>
  )
}

// Loading fallback component
function LoadingFallback() {
  return (
    <div className="w-full max-w-sm space-y-6">
      <div className="flex flex-col space-y-2 text-center">
        <div className="h-8 bg-gray-200 rounded animate-pulse"></div>
        <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
      </div>
      <div className="space-y-4">
        <div className="h-10 bg-gray-200 rounded animate-pulse"></div>
        <div className="h-10 bg-gray-200 rounded animate-pulse"></div>
        <div className="h-10 bg-gray-200 rounded animate-pulse"></div>
      </div>
    </div>
  )
}

function ResetPasswordPage() {
  const t = useTranslations()
  const params = useParams()
  const locale = params.locale as string

  return (
    <div className="min-h-screen flex flex-col md:flex-row">
      {/* Left side - Branding & Info */}
      <div className="w-full md:w-1/2 bg-gradient-to-br from-blue-600 to-indigo-700 text-white p-8 flex flex-col">
        <div className="flex items-center mb-12">
          <div className="w-10 h-10 bg-white rounded-md flex items-center justify-center mr-3">
            <BarChart3 className="text-blue-600" />
          </div>
          <Link href={`/${locale}`} className="text-xl font-bold">Hapoest</Link>
        </div>
        
        <div className="flex-grow flex flex-col justify-center max-w-md mx-auto">
          <h1 className="text-3xl md:text-4xl font-bold mb-6">
            {t('auth.resetPasswordTitle')}
          </h1>
          <p className="text-xl mb-8 text-blue-100">
            {t('auth.forgotPasswordDescription')}
          </p>
          <div className="hidden md:block">
            <div className="flex justify-center items-center mb-6">
              <Lock className="w-24 h-24 text-blue-200" />
            </div>
            <div className="flex justify-center gap-4">
              <div className="w-24 h-24 bg-blue-500/30 rounded-lg flex items-center justify-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="40"
                  height="40"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="1.5"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="text-blue-100"
                >
                  <rect width="18" height="11" x="3" y="11" rx="2" ry="2"/>
                  <circle cx="12" cy="16" r="1"/>
                  <path d="M7 11V7a5 5 0 0 1 10 0v4"/>
                </svg>
              </div>
              <div className="w-24 h-24 bg-blue-500/30 rounded-lg flex items-center justify-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="40"
                  height="40"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="1.5"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="text-blue-100"
                >
                  <path d="M9 12l2 2 4-4" />
                  <circle cx="12" cy="12" r="10" />
                </svg>
              </div>
              <div className="w-24 h-24 bg-blue-500/30 rounded-lg flex items-center justify-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="40"
                  height="40"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="1.5"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="text-blue-100"
                >
                  <path d="M12 1v6M12 17v6M4.22 4.22l4.24 4.24M15.54 15.54l4.24 4.24M1 12h6M17 12h6M4.22 19.78l4.24-4.24M15.54 8.46l4.24-4.24"/>
                </svg>
              </div>
            </div>
          </div>
        </div>
        
        <div className="mt-auto text-sm text-blue-200">
          © 2025 Hapoest - {t('footer.allRightsReserved')}
        </div>
      </div>
      
      {/* Right side - Reset Password Form */}
      <div className="w-full md:w-1/2 flex flex-col items-center justify-center p-8 bg-white">
        <Suspense fallback={<LoadingFallback />}>
          <ResetPasswordForm />
        </Suspense>
      </div>
    </div>
  )
}

export default withAuthRedirect(ResetPasswordPage);
