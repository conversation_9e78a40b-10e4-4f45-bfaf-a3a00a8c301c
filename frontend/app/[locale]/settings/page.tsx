"use client";

import { useState, useEffect } from "react";
import { useRouter, useParams } from "next/navigation";
import { useTranslations } from "next-intl";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2, CheckCircle, AlertCircle, Info, HelpCircle, Save, DollarSign, CircleDollarSign, Coins, Lock, KeyRound, RefreshCcw } from "lucide-react";
import { PasswordChangeRequest, CurrencyType } from "@/lib/types";
import useUserSettings from "@/lib/hooks/useUserSettings";
import { PasswordStrength<PERSON><PERSON><PERSON>, passwordMeetsRequirements } from "@/components/password-strength-checker";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Badge } from "@/components/ui/badge";
import withAuth from "@/components/auth/with-auth";

function SettingsPage() {
  const params = useParams()
  const locale = params.locale as string
  const router = useRouter();
  const t = useTranslations();
  const tSettings = useTranslations("settings");
  const tEstimate = useTranslations("estimate");
  const tAuth = useTranslations("auth")
  const { userSettings, refreshSettings } = useUserSettings(locale);

  const [loading, setLoading] = useState(true);
  const [savingSettings, setSavingSettings] = useState(false);
  const [changingPassword, setChangingPassword] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [localUserSettings, setLocalUserSettings] = useState(userSettings);
  const [showSetupGuide, setShowSetupGuide] = useState(true);
  
  // Password change form
  const [passwordData, setPasswordData] = useState<PasswordChangeRequest>({
    current_password: '',
    new_password: '',
    confirm_password: '',
  });
  
  // UI state for password fields
  const [passwordFocused, setPasswordFocused] = useState(false);

  // Reset messages when password fields change
  const handlePasswordInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setError(null);
    setSuccess(null);
    setPasswordData({ ...passwordData, [e.target.name]: e.target.value });
  };

  // Initialize roles from estimate translations
  const roles = [
    "PM", "Developer", "Designer", "Tester", 
    "Backend", "Frontend", "UI", "UX", 
    "QA", "DevOps", "BA", "Architect"
  ];
  
  // Suggested rates for different roles (in USD)
  const suggestedRates: Record<string, number> = {
    PM: 200,
    Developer: 130,
    Designer: 130,
    Tester: 130,
    Backend: 130,
    Frontend: 130,
    UI: 130,
    UX: 130,
    QA: 130,
    DevOps: 130,
    BA: 130,
    Architect: 130
  };
  
  // Check if settings are complete (at least main roles have rates)
  const isSettingsComplete = () => {
    if (!localUserSettings?.role_rates) return false;
    
    // Check if at least the main roles have rates set
    const mainRoles = ['Developer', 'Designer', 'PM'];
    return mainRoles.every(role => 
      localUserSettings.role_rates[role] && Number(localUserSettings.role_rates[role]) > 0
    );
  };

  // Set the local settings from the global context when it changes
  useEffect(() => {
    if (userSettings) {
      setLocalUserSettings(userSettings);
      setLoading(false);
    }
  }, [userSettings]);

  // Simple safety check in case settings aren't loaded
  if (loading && !localUserSettings) {
    return (
      <div className="flex justify-center items-center min-h-[60vh]">
        <Loader2 className="mr-2 h-8 w-8 animate-spin" />
        <p>{t('common.loading')}</p>
      </div>
    );
  }

  const handlePasswordChange = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Reset messages
    setError(null);
    setSuccess(null);
    
    // Validate passwords match
    if (passwordData.new_password !== passwordData.confirm_password) {
      setError(tSettings('passwordsDoNotMatch'));
      return;
    }
    
    // Validate password strength
    if (!passwordMeetsRequirements(passwordData.new_password)) {
      setError(tAuth('invalidPassword', { defaultValue: 'The password has not been set correctly.' }));
      return;
    }

    const token = localStorage.getItem("token");
    if (!token) {
      router.push(`/${locale}/login`);
      return;
    }

    try {
      setChangingPassword(true);
      setError(null);
      setSuccess(null);
      
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/users/change-password`, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(passwordData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || tSettings('passwordError'));
      }

      // Clear form
      setPasswordData({
        current_password: '',
        new_password: '',
        confirm_password: '',
      });
      
      setSuccess(tSettings('passwordUpdated'));
    } catch (error) {
      console.error("Error changing password:", error);
      setError(error instanceof Error ? error.message : tSettings('passwordError'));
    } finally {
      setChangingPassword(false);
    }
  };

  const handleCurrencyChange = (currency: CurrencyType) => {
    if (!localUserSettings) return;
    
    setLocalUserSettings({
      ...localUserSettings,
      currency
    });
  };

  const handleRoleRateChange = (role: string, value: string) => {
    if (!localUserSettings) return;
    // If you have enough 10 numbers, do not update the State anymore, keep the old value
    if (value.length > 10) return;
    if (value === "") {
      setLocalUserSettings({
        ...localUserSettings,
        role_rates: {
          ...localUserSettings.role_rates,
          [role]: 0,
        },
      });
      return;
    }
    const rate = parseFloat(value);
    if (isNaN(rate) || rate < 0) return;
    
    setLocalUserSettings({
      ...localUserSettings,
      role_rates: {
        ...localUserSettings.role_rates,
        [role]: rate
      }
    });
  };

  const applySuggestedRates = () => {
    if (!localUserSettings) return;
    
    // Create a deep copy of the settings
    const updatedSettings = { ...localUserSettings };
    
    // Initialize role_rates if it doesn't exist
    if (!updatedSettings.role_rates) {
      updatedSettings.role_rates = {};
    }
    
    // Get currency conversion rate (simplified for example)
    const conversionRate = 
      updatedSettings.currency === 'VND' ? 25500 : 
      updatedSettings.currency === 'JPY' ? 146 : 1;
    
    // Apply suggested rates with currency conversion
    roles.forEach(role => {
      if (suggestedRates[role]) {
        updatedSettings.role_rates[role] = Math.round(suggestedRates[role] * conversionRate);
      }
    });
    
    setLocalUserSettings(updatedSettings);
    setSuccess(tSettings('roleRatesUpdated'));
  };

  const saveSettings = async () => {
    if (!localUserSettings) return;
    
    const token = localStorage.getItem("token");
    if (!token) {
      router.push(`/${locale}/login`);
      return;
    }
    
    try {
      setSavingSettings(true);
      setError(null);
      setSuccess(null);
      
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/users/settings`, {
        method: 'PUT',
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          currency: localUserSettings.currency,
          role_rates: localUserSettings.role_rates
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || t('common.error'));
      }
      
      await refreshSettings();
      
      // Check conditions to mark settings as complete
      const hasCurrency = !!localUserSettings.currency;
      const hasRoleRates = !!localUserSettings.role_rates;
      const mainRoles = ['Developer', 'Designer', 'PM'];
      const hasMainRoleRates = hasRoleRates && mainRoles.some(role => 
        localUserSettings.role_rates?.[role] && Number(localUserSettings.role_rates[role]) > 0
      );
      
      // If settings are complete, save to localStorage
      if (hasCurrency && hasMainRoleRates) {
        localStorage.setItem("settingsCompleted", "true");
      }
      
      setSuccess(tSettings('settingsUpdated'));
    } catch (error) {
      console.error("Error saving settings:", error);
      setError(error instanceof Error ? error.message : t('common.error'));
    } finally {
      setSavingSettings(false);
    }
  };

  return (
    <div className="container py-8">
      {error && (
        <Alert variant="destructive" className="mb-4 bg-red-50 border-red-200 text-red-700 ">
          <AlertCircle className="h-4 w-4 text-red-700" />
          <AlertDescription className="text-red-700">{error}</AlertDescription>
        </Alert>
      )}
      
      {success && (
        <Alert variant="default" className="mb-4 bg-green-50 border-green-200 text-green-800">
          <CheckCircle className="h-4 w-4 text-green-500" />
          <AlertDescription>{success}</AlertDescription>
        </Alert>
      )}

      {showSetupGuide && !isSettingsComplete() && (
        <Alert variant="default" className="mb-6 bg-blue-50 border-blue-200 text-blue-800">
          <div className="flex items-start">
            <Info className="h-5 w-5 text-blue-500 mt-0.5 mr-2 flex-shrink-0" />
            <div>
              <h3 className="font-medium text-blue-800 mb-1">{tSettings('settingsSetupGuide')}</h3>
              <p className="text-sm mb-2">{tSettings('settingsRequiredInfo')}</p>
              <ul className="text-sm list-disc pl-5 mb-2 space-y-1">
                <li>{tSettings('currencyImportance')}</li>
                <li>{tSettings('roleRatesImportance')}</li>
                <li>{tSettings('roleRatesExample')}</li>
              </ul>
              <div className="flex justify-end">
                <Button variant="outline" size="sm" onClick={() => setShowSetupGuide(false)} className="text-xs">
                  {t('common.cancel')}
                </Button>
              </div>
            </div>
          </div>
        </Alert>
      )}

      <Tabs defaultValue="role-rates" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="password">{tSettings('changePassword')}</TabsTrigger>
          <TabsTrigger value="currency">{tSettings('currency')}</TabsTrigger>
          <TabsTrigger value="role-rates">
            <div className="flex items-center">
              {tSettings('roleRates')}
              {!isSettingsComplete() && (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <div className="ml-1.5 inline-flex h-4 w-4 items-center justify-center rounded-full bg-amber-500 text-xs font-medium text-white">!</div>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="max-w-xs text-sm">{tSettings('setupRolesTooltip')}</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}
            </div>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="password">
          <Card>
            <CardHeader>
              <CardTitle>{tSettings('changePassword')}</CardTitle>
              <CardDescription>
                {tSettings('account')}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handlePasswordChange} data-hs-ignore="true">
                <div className="grid gap-4">
                  <div className="grid gap-2">
                    <Label htmlFor="current-password">{tSettings('currentPassword')}</Label>
                    <Input
                      id="current-password"
                      type="password"
                      name="current_password"
                      value={passwordData.current_password}
                      onChange={handlePasswordInputChange}
                      required
                    />
                  </div>
                  
                  <div className="grid gap-2">
                    <Label htmlFor="new-password">{tSettings('newPassword')}</Label>
                    <Input
                      id="new-password"
                      type="password"
                      name="new_password"
                      value={passwordData.new_password}
                      onChange={handlePasswordInputChange}
                      onFocus={() => setPasswordFocused(true)}
                      onBlur={() => setPasswordFocused(false)}
                      required
                    />
                    
                    {passwordFocused && passwordData.new_password && (
                      <PasswordStrengthChecker 
                        password={passwordData.new_password} 
                        t={t}
                      />
                    )}
                  </div>
                  
                  <div className="grid gap-2">
                    <Label htmlFor="confirm-password">{tSettings('confirmPassword')}</Label>
                    <Input
                      id="confirm-password"
                      type="password"
                      name="confirm_password"
                      value={passwordData.confirm_password}
                      onChange={handlePasswordInputChange}
                      required
                    />
                    
                    {passwordData.new_password && passwordData.confirm_password && (
                      <div className="flex items-center text-xs mt-1">
                        {passwordData.new_password === passwordData.confirm_password ? (
                          <>
                            <CheckCircle className="h-4 w-4 text-green-500 mr-1" />
                            <span className="text-green-700">{tSettings('passwordMatch', { defaultValue: 'Passwords match' })}</span>
                          </>
                        ) : (
                          <>
                            <AlertCircle className="h-4 w-4 text-red-500 mr-1" />
                            <span className="text-red-700">{tSettings('passwordsDoNotMatch', { defaultValue: 'Passwords do not match' })}</span>
                          </>
                        )}
                      </div>
                    )}
                  </div>
                </div>
                <div className="mt-4">
                  <Button type="submit" disabled={changingPassword}>
                    {changingPassword ? 
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : 
                      <KeyRound className="mr-2 h-4 w-4" />
                    }
                    {tSettings('changePassword')}
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="currency">
          <Card>
            <CardHeader>
              <CardTitle>{tSettings('currencyTitle')}</CardTitle>
              <CardDescription>
                {tSettings('currencyDescription')}
                <p className="mt-1 text-amber-600">{tSettings('currencyImportance')}</p>
              </CardDescription>
            </CardHeader>
            <CardContent>
              <RadioGroup
                value={localUserSettings?.currency || "USD"}
                onValueChange={(value) => handleCurrencyChange(value as CurrencyType)}
                className="space-y-3"
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="USD" id="USD" />
                  <Label htmlFor="USD" className="font-normal flex items-center">
                    <DollarSign className="h-4 w-4 mr-1 text-green-600" />
                    {tSettings('USD')}
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="VND" id="VND" />
                  <Label htmlFor="VND" className="font-normal flex items-center">
                    <Coins className="h-4 w-4 mr-1 text-amber-600" />
                    {tSettings('VND')}
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="JPY" id="JPY" />
                  <Label htmlFor="JPY" className="font-normal flex items-center">
                    <CircleDollarSign className="h-4 w-4 mr-1 text-blue-600" />
                    {tSettings('JPY')}
                  </Label>
                </div>
              </RadioGroup>
            </CardContent>
            <CardFooter>
              <Button onClick={saveSettings} disabled={savingSettings}>
                {savingSettings ? 
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : 
                  <Save className="mr-2 h-4 w-4" />
                }
                {tSettings('saveSettings')}
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="role-rates">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>{tSettings('roleRates')}</CardTitle>
                  <CardDescription>
                    {tSettings('roleRatesDescription')}
                    <p className="mt-1 text-amber-600">{tSettings('roleRatesImportance')}</p>
                  </CardDescription>
                </div>
                {!isSettingsComplete() && (
                  <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200">
                    {tSettings('setupIncomplete')}
                  </Badge>
                )}
              </div>
            </CardHeader>
            <CardContent>
              <div className="mb-4 p-3 bg-gray-50 rounded-md border">
                <div className="flex justify-between items-center mb-2">
                  <h3 className="font-medium text-sm">{tSettings('ratesSuggestion')}</h3>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={applySuggestedRates}
                    className="text-xs flex items-center"
                  >
                    <RefreshCcw className="h-3 w-3 mr-1" />
                    {tSettings('applySuggested')}
                  </Button>
                </div>
                <p className="text-xs text-gray-500">{tSettings('roleRatesExample')}</p>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {roles.map((role) => (
                  <div key={role} className="flex items-center space-x-2">
                    <Label htmlFor={`rate-${role}`} className="w-36 font-medium">
                      {tEstimate(`roles.${role}`)}:
                    </Label>
                    <Input
                      id={`rate-${role}`}
                      type="number"
                      inputMode="numeric"
                      pattern="\d*"
                      min="0"
                      value={localUserSettings?.role_rates?.[role]?.toString() ?? "0"}
                      onChange={e => handleRoleRateChange(role, e.currentTarget.value)}
                      className="max-w-[150px]"
                    />
                    <span className="text-sm text-gray-500">/ {tEstimate('day')}</span>
                  </div>
                ))}
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <div>
                {isSettingsComplete() && (
                  <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                    <CheckCircle className="h-3 w-3 mr-1" />
                    {tSettings('setupComplete')}
                  </Badge>
                )}
              </div>
              <Button onClick={saveSettings} disabled={savingSettings}>
                {savingSettings ? 
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : 
                  <Save className="mr-2 h-4 w-4" />
                }
                {tSettings('saveSettings')}
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

export default withAuth(SettingsPage)
