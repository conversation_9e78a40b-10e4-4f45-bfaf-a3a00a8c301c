'use client';

import { useEffect } from 'react';
import { defaultLocale } from '@/i18n/settings';
import { getStoredLocale, isLoggedIn } from '@/utils/localeStorage';

export default function Home() {
  useEffect(() => {
    // Get locale from localStorage or use default
    const locale = getStoredLocale();
    
    // Check if user is logged in
    if (isLoggedIn()) {
      // Redirect to projects page with the appropriate locale
      window.location.href = `/${locale}/projects`;
    } else {
      // Redirect to home page with the appropriate locale
      window.location.href = `/${locale}`;
    }
  }, []);

  // Loading state while redirecting
  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="text-center">
        <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-gray-900 mx-auto"></div>
        <p className="mt-4">Loading...</p>
      </div>
    </div>
  );
}