export interface Project {
  id: number
  name: string
  description?: string
  software_type?: string
  technology_stack?: string
  requirements_text: string | null
  requirements_file_path: string | null
  user_id: number
  created_at: string
  updated_at?: string
}

export interface DevelopmentItem {
  feature: string
  description: string
  backend_effort: number
  frontend_effort: number
  unit_test_effort: number
  priority?: string
  is_selected?: boolean
}

export interface SummaryItem {
  category: string
  effort: number
  description: string
  role?: string
  day_rate?: number
  cost?: number
  is_selected?: boolean
}

export interface Estimate {
  id: number
  name: string
  project_id: number
  development_items: DevelopmentItem[]
  summary_items: SummaryItem[]
  grand_total: number
  total_cost?: number
  day_rate?: number
  currency?: string
  is_active: boolean
  created_at: string
  updated_at?: string
}

export interface EstimateResponse {
  development_items: DevelopmentItem[]
  summary_items: SummaryItem[]
  grand_total: number
  total_cost?: number
  day_rate?: number
  currency?: string
}