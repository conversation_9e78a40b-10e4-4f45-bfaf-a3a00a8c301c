# Environment files
.env
.env.*
!.env.example

# IDE and editor files
.idea/
.vscode/
*.swp
*.swo
.DS_Store
Thumbs.db

# Cursor specific
.cursor/
.cursor.json
.cursorrules
.cursor-rules/
.cursor-rules.json
context/
.context/

# Docker
docker-compose.override.yml
*.env.docker

# Logs and databases
*.log
*.sqlite
*.sqlite3
*.db

# Cache directories
.cache/
tmp/
temp/

# Local development
*.local
.local/

# System files
.DS_Store
.directory
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Node modules in root (if any)
node_modules/

# Python virtual environment in root (if any)
venv/
env/
.env/
.venv/
.Python
python/

coverage.xml
.coverage