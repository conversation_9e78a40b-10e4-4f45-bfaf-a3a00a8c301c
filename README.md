# Hệ Thống Ước Tính Dự Án Phần Mềm

Ứng dụng web giúp ước tính công số phát triển dự án phần mềm dựa trên AI. Đ<PERSON><PERSON>c phát triển với FastAPI (Backend) và Next.js (Frontend).

## Tính năng chính

- **<PERSON><PERSON><PERSON> thực người dùng**: <PERSON><PERSON><PERSON> ký/đăng nhập bằng email, Google OAuth
- **Quản lý dự án**: <PERSON><PERSON><PERSON>, xem, chỉnh sửa, xóa dự án
- **Nhập yêu cầu dự án**: Nhập text hoặc tải lên file PDF
- **Ước tính dự án**: <PERSON>ân tích yêu cầu bằng AI (Google Gemini) và tạo ước tính
- **Tinh chỉnh ước tính**: Chỉnh sửa kết quả ước tính theo nhu cầu
- **Xuất báo cáo**: Xu<PERSON>t kết quả ước tính ra file Excel

## Công nghệ sử dụng

### Backend
- FastAPI
- PostgreSQL
- SQLAlchemy
- Pydantic
- Google Gemini AI API

### Frontend
- Next.js
- React
- TailwindCSS
- TypeScript

## Cài đặt và chạy

### Sử dụng Docker

1. Đảm bảo đã cài đặt Docker và Docker Compose
2. Clone repository này
3. Tạo file `.env` tại thư mục gốc với các biến môi trường cần thiết (xem `.env.example`)
4. Chạy lệnh:

```bash
docker-compose up -d
```

Ứng dụng sẽ chạy ở:
- Frontend: http://localhost:3000
- Backend API: http://localhost:8000
- Swagger UI: http://localhost:8000/docs

### Phát triển 

#### Backend

```bash
cd backend
pip install -r requirements.txt
python run.py
```

#### Frontend

```bash
cd frontend
npm install
npm run dev
```

## API Endpoints

- `/api/v1/auth`: Xác thực người dùng (đăng ký, đăng nhập, OAuth)
- `/api/v1/projects`: Quản lý dự án (CRUD)
- `/api/v1/projects/{id}/estimate`: Tạo ước tính cho dự án
- `/api/v1/projects/{id}/export-excel`: Xuất báo cáo Excel

## Tác giả

- Your Name - [<EMAIL>]

## Giấy phép

Dự án được phân phối dưới giấy phép MIT. Xem file `LICENSE` để biết thêm chi tiết. 